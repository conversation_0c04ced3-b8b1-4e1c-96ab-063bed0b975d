<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use App\Services\PaymentService;
use App\Services\RazorpayService;
use App\Services\StripeService;
use App\Models\Booking;
use App\Models\Payment;
use Exception;

class PaymentController extends Controller
{
    protected $paymentService;

    public function __construct(PaymentService $paymentService)
    {
        $this->paymentService = $paymentService;
    }

    /**
     * Create payment intent
     */
    public function createPaymentIntent(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'items' => 'required|array|min:1',
            'items.*.event_id' => 'required|exists:events,id',
            'items.*.ticket_type_id' => 'required|exists:ticket_types,id',
            'items.*.quantity' => 'required|integer|min:1',
            'payment_method' => 'required|string|in:razorpay,stripe',
            'customer_info' => 'required|array',
            'customer_info.name' => 'required|string',
            'customer_info.email' => 'required|email',
            'customer_info.phone' => 'nullable|string'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            // Create temporary bookings
            $bookings = [];
            foreach ($request->items as $item) {
                $booking = Booking::create([
                    'booking_reference' => 'TEMP_' . uniqid(),
                    'user_id' => Auth::id(),
                    'event_id' => $item['event_id'],
                    'ticket_type_id' => $item['ticket_type_id'],
                    'quantity' => $item['quantity'],
                    'total_amount' => $item['quantity'] * \App\Models\TicketType::find($item['ticket_type_id'])->price,
                    'payment_status' => 'pending',
                    'payment_method' => $request->payment_method,
                    'qr_code_hash' => \Illuminate\Support\Str::random(32),
                ]);
                $bookings[] = $booking;
            }

            // Create payment intent
            $paymentIntent = $this->paymentService->createPaymentIntent(
                $bookings,
                $request->payment_method,
                $request->customer_info
            );

            return response()->json([
                'success' => true,
                'payment_intent' => $paymentIntent,
                'booking_ids' => collect($bookings)->pluck('id')->toArray()
            ]);

        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Handle payment success
     */
    public function handlePaymentSuccess(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'payment_id' => 'required|string',
            'payment_method' => 'required|string|in:razorpay,stripe',
            'payment_data' => 'required|array'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $result = $this->paymentService->processPaymentSuccess(
                $request->payment_id,
                $request->payment_method,
                $request->payment_data
            );

            if ($result['success']) {
                return response()->json([
                    'success' => true,
                    'message' => 'Payment processed successfully',
                    'bookings' => $result['bookings'],
                    'redirect_url' => route('user.booking', $result['bookings'][0]->id)
                ]);
            }

            return response()->json([
                'success' => false,
                'message' => 'Payment processing failed'
            ], 500);

        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Handle payment failure
     */
    public function handlePaymentFailure(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'payment_id' => 'required|string',
            'reason' => 'required|string'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $this->paymentService->processPaymentFailure(
                $request->payment_id,
                $request->reason
            );

            return response()->json([
                'success' => true,
                'message' => 'Payment failure processed'
            ]);

        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get payment methods
     */
    public function getPaymentMethods()
    {
        try {
            $methods = $this->paymentService->getPaymentMethods();

            return response()->json([
                'success' => true,
                'payment_methods' => $methods
            ]);

        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 500);
        }
    }
}
