<?php

use Illuminate\Support\Facades\Route;

Route::get('/', function () {
    return view('welcome');
});

// Admin Web Routes
Route::prefix('admin')->name('admin.')->group(function () {
    // Admin login page
    Route::get('/login', function () {
        return view('admin.login');
    })->name('login');

    // Protected admin routes (require authentication)
    Route::middleware(['auth:sanctum'])->group(function () {
        Route::get('/dashboard', function () {
            return view('admin.dashboard');
        })->name('dashboard');

        Route::get('/events', function () {
            return view('admin.events.index');
        })->name('events.index');

        Route::get('/events/create', function () {
            return view('admin.events.create');
        })->name('events.create');

        Route::get('/bookings', function () {
            return view('admin.bookings.index');
        })->name('bookings.index');

        Route::get('/users', function () {
            return view('admin.users.index');
        })->name('users.index');

        Route::get('/profile', function () {
            return view('admin.profile');
        })->name('profile');

        Route::get('/settings', function () {
            return view('admin.settings');
        })->name('settings');

        Route::get('/analytics', function () {
            return view('admin.analytics');
        })->name('analytics');

        Route::post('/logout', function () {
            auth()->logout();
            return redirect()->route('admin.login');
        })->name('logout');
    });
});
