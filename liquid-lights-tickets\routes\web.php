<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\PublicController;

// Public Routes
Route::get('/', [PublicController::class, 'index'])->name('public.index');
Route::get('/events', [PublicController::class, 'events'])->name('public.events');
Route::get('/events/{id}', [PublicController::class, 'event'])->name('public.event');
Route::get('/artists', function() { return view('public.artists.index'); })->name('public.artists');
Route::get('/artists/{id}', [PublicController::class, 'artist'])->name('public.artist');
Route::get('/search', [PublicController::class, 'search'])->name('public.search');

// Admin Web Routes
Route::prefix('admin')->name('admin.')->group(function () {
    // Admin login page
    Route::get('/login', function () {
        return view('admin.login');
    })->name('login');

    // Protected admin routes (require authentication)
    Route::middleware(['auth:sanctum'])->group(function () {
        Route::get('/dashboard', function () {
            return view('admin.dashboard');
        })->name('dashboard');

        Route::get('/events', function () {
            return view('admin.events.index');
        })->name('events.index');

        Route::get('/events/create', function () {
            return view('admin.events.create');
        })->name('events.create');

        Route::get('/events/{id}', function ($id) {
            return view('admin.events.show', compact('id'));
        })->name('events.show');

        Route::get('/events/{id}/edit', function ($id) {
            return view('admin.events.edit', compact('id'));
        })->name('events.edit');

        Route::get('/bookings', function () {
            return view('admin.bookings.index');
        })->name('bookings.index');

        Route::get('/bookings/create', function () {
            return view('admin.bookings.create');
        })->name('bookings.create');

        Route::get('/bookings/{id}', function ($id) {
            return view('admin.bookings.show', compact('id'));
        })->name('bookings.show');

        Route::get('/users', function () {
            return view('admin.users.index');
        })->name('users.index');

        Route::get('/users/create', function () {
            return view('admin.users.create');
        })->name('users.create');

        Route::get('/users/{id}', function ($id) {
            return view('admin.users.show', compact('id'));
        })->name('users.show');

        Route::get('/users/{id}/edit', function ($id) {
            return view('admin.users.edit', compact('id'));
        })->name('users.edit');

        Route::get('/profile', function () {
            return view('admin.profile');
        })->name('profile');

        Route::get('/settings', function () {
            return view('admin.settings');
        })->name('settings');

        Route::get('/analytics', function () {
            return view('admin.analytics');
        })->name('analytics');

        Route::post('/logout', function () {
            auth()->logout();
            return redirect()->route('admin.login');
        })->name('logout');
    });
});
