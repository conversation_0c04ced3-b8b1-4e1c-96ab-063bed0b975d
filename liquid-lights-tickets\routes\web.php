<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\PublicController;

// Public Routes
Route::get('/', [PublicController::class, 'index'])->name('public.index');
Route::get('/events', [PublicController::class, 'events'])->name('public.events');
Route::get('/events/{id}', [PublicController::class, 'event'])->name('public.event');
Route::get('/artists', function() { return view('public.artists.index'); })->name('public.artists');
Route::get('/artists/{id}', [PublicController::class, 'artist'])->name('public.artist');
Route::get('/search', [PublicController::class, 'search'])->name('public.search');
Route::get('/offline', function () {
    return view('offline');
})->name('offline');

// Authentication Routes
use App\Http\Controllers\UserAuthController;
Route::get('/login', [UserAuthController::class, 'showLogin'])->name('login');
Route::post('/login', [UserAuthController::class, 'login']);
Route::get('/register', [UserAuthController::class, 'showRegister'])->name('register');
Route::post('/register', [UserAuthController::class, 'register']);
Route::post('/logout', [UserAuthController::class, 'logout'])->name('logout');
Route::get('/forgot-password', [UserAuthController::class, 'showForgotPassword'])->name('forgot-password');
Route::post('/forgot-password', [UserAuthController::class, 'forgotPassword']);
Route::get('/verify-email/{id}/{hash}', [UserAuthController::class, 'verifyEmail'])->name('verification.verify');
Route::post('/email/verification-notification', [UserAuthController::class, 'resendVerification'])->name('verification.send')->middleware('auth');

// Booking Routes
use App\Http\Controllers\BookingController;
Route::get('/cart', [BookingController::class, 'cart'])->name('booking.cart');
Route::get('/checkout', [BookingController::class, 'checkout'])->name('booking.checkout');
Route::post('/api/booking/process', [BookingController::class, 'processBooking'])->name('booking.process');
Route::get('/booking/{id}/confirmation', [BookingController::class, 'confirmation'])->name('booking.confirmation');
Route::post('/api/booking/{id}/cancel', [BookingController::class, 'cancel'])->name('booking.cancel');

// User Dashboard Routes
use App\Http\Controllers\UserDashboardController;
Route::middleware('auth')->group(function () {
    Route::get('/user/dashboard', [UserDashboardController::class, 'dashboard'])->name('user.dashboard');
    Route::get('/user/bookings', [UserDashboardController::class, 'bookings'])->name('user.bookings');
    Route::get('/user/bookings/{id}', [UserDashboardController::class, 'booking'])->name('user.booking');
    Route::get('/user/bookings/{id}/download', [UserDashboardController::class, 'downloadTicket'])->name('user.booking.download');
    Route::get('/user/profile', [UserDashboardController::class, 'profile'])->name('user.profile');
    Route::put('/user/profile', [UserDashboardController::class, 'updateProfile'])->name('user.profile.update');
    Route::put('/user/password', [UserDashboardController::class, 'updatePassword'])->name('user.password.update');
    Route::get('/user/notifications', function () {
        return view('user.notifications');
    })->name('user.notifications');
});

// Payment Routes
use App\Http\Controllers\PaymentController;
Route::middleware('auth')->group(function () {
    Route::post('/api/payment/create-intent', [PaymentController::class, 'createPaymentIntent'])->name('payment.create-intent');
    Route::post('/api/payment/success', [PaymentController::class, 'handlePaymentSuccess'])->name('payment.success');
    Route::post('/api/payment/failure', [PaymentController::class, 'handlePaymentFailure'])->name('payment.failure');
    Route::get('/api/payment/methods', [PaymentController::class, 'getPaymentMethods'])->name('payment.methods');
});

// Webhook Routes (no auth required)
use App\Http\Controllers\WebhookController;
Route::post('/webhooks/razorpay', [WebhookController::class, 'razorpay'])->name('webhooks.razorpay');
Route::post('/webhooks/stripe', [WebhookController::class, 'stripe'])->name('webhooks.stripe');
Route::post('/webhooks/test', [WebhookController::class, 'test'])->name('webhooks.test');

// Check-in Routes (Admin only)
use App\Http\Controllers\CheckInController;
Route::middleware(['auth', 'role:admin'])->prefix('admin')->group(function () {
    Route::get('/checkin/{eventId?}', [CheckInController::class, 'scanner'])->name('admin.checkin.scanner');
    Route::post('/checkin/validate', [CheckInController::class, 'validateQR'])->name('admin.checkin.validate');
    Route::post('/checkin/checkin', [CheckInController::class, 'checkIn'])->name('admin.checkin.checkin');
    Route::post('/checkin/manual', [CheckInController::class, 'manualCheckIn'])->name('admin.checkin.manual');
    Route::get('/checkin/events/{eventId}/stats', [CheckInController::class, 'getEventStats'])->name('admin.checkin.stats');
    Route::get('/checkin/events/{eventId}/recent', [CheckInController::class, 'getRecentCheckIns'])->name('admin.checkin.recent');
});

// Push Notification Routes
use App\Http\Controllers\PushNotificationController;
Route::middleware('auth')->prefix('api/notifications')->group(function () {
    Route::get('/vapid-key', [PushNotificationController::class, 'getVapidPublicKey'])->name('notifications.vapid-key');
    Route::post('/subscribe', [PushNotificationController::class, 'subscribe'])->name('notifications.subscribe');
    Route::post('/unsubscribe', [PushNotificationController::class, 'unsubscribe'])->name('notifications.unsubscribe');
    Route::get('/status', [PushNotificationController::class, 'getSubscriptionStatus'])->name('notifications.status');
    Route::post('/test', [PushNotificationController::class, 'sendTestNotification'])->name('notifications.test');
    Route::get('/preferences', [PushNotificationController::class, 'getPreferences'])->name('notifications.preferences');
    Route::post('/preferences', [PushNotificationController::class, 'updatePreferences'])->name('notifications.preferences.update');
});

// Admin Web Routes
use App\Http\Controllers\Admin\SponsorController;
use App\Http\Controllers\Admin\SponsorshipTierController;
use App\Http\Controllers\Admin\EventSponsorController;

Route::prefix('admin')->name('admin.')->group(function () {
    // Admin login page
    Route::get('/login', function () {
        return view('admin.login');
    })->name('login');

    // Protected admin routes (require authentication)
    Route::middleware(['auth:sanctum'])->group(function () {
        Route::get('/dashboard', function () {
            return view('admin.dashboard');
        })->name('dashboard');

        Route::get('/events', function () {
            return view('admin.events.index');
        })->name('events.index');

        Route::get('/events/create', function () {
            return view('admin.events.create');
        })->name('events.create');

        Route::get('/events/{id}', function ($id) {
            return view('admin.events.show', compact('id'));
        })->name('events.show');

        Route::get('/events/{id}/edit', function ($id) {
            return view('admin.events.edit', compact('id'));
        })->name('events.edit');

        Route::get('/bookings', function () {
            return view('admin.bookings.index');
        })->name('bookings.index');

        Route::get('/bookings/create', function () {
            return view('admin.bookings.create');
        })->name('bookings.create');

        Route::get('/bookings/{id}', function ($id) {
            return view('admin.bookings.show', compact('id'));
        })->name('bookings.show');

        Route::get('/users', function () {
            return view('admin.users.index');
        })->name('users.index');

        Route::get('/users/create', function () {
            return view('admin.users.create');
        })->name('users.create');

        Route::get('/users/{id}', function ($id) {
            return view('admin.users.show', compact('id'));
        })->name('users.show');

        Route::get('/users/{id}/edit', function ($id) {
            return view('admin.users.edit', compact('id'));
        })->name('users.edit');

        Route::get('/profile', function () {
            return view('admin.profile');
        })->name('profile');

        Route::get('/settings', function () {
            return view('admin.settings');
        })->name('settings');

        Route::get('/analytics', function () {
            return view('admin.analytics');
        })->name('analytics');

        // Sponsors Management
        Route::resource('sponsors', SponsorController::class);
        Route::resource('sponsorship-tiers', SponsorshipTierController::class);
        Route::resource('event-sponsors', EventSponsorController::class);

        // Sponsor Analytics
        use App\Http\Controllers\Admin\SponsorAnalyticsController;
        Route::get('/sponsor-analytics', [SponsorAnalyticsController::class, 'index'])->name('sponsor-analytics.index');
        Route::get('/sponsor-analytics/sponsor/{sponsor}', [SponsorAnalyticsController::class, 'sponsorReport'])->name('sponsor-analytics.sponsor-report');
        Route::get('/sponsor-analytics/export', [SponsorAnalyticsController::class, 'export'])->name('sponsor-analytics.export');
        Route::get('/sponsor-analytics/chart-data', [SponsorAnalyticsController::class, 'chartData'])->name('sponsor-analytics.chart-data');

        Route::post('/logout', function () {
            auth()->logout();
            return redirect()->route('admin.login');
        })->name('logout');
    });
});
