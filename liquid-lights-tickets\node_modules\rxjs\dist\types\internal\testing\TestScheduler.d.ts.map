{"version": 3, "file": "TestScheduler.d.ts", "sourceRoot": "", "sources": ["../../../../src/internal/testing/TestScheduler.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,UAAU,EAAE,MAAM,eAAe,CAAC;AAC3C,OAAO,EAAE,cAAc,EAAE,MAAM,kBAAkB,CAAC;AAClD,OAAO,EAAE,aAAa,EAAE,MAAM,iBAAiB,CAAC;AAChD,OAAO,EAAE,WAAW,EAAE,MAAM,eAAe,CAAC;AAC5C,OAAO,EAAE,eAAe,EAAE,MAAM,mBAAmB,CAAC;AAEpD,OAAO,EAAE,oBAAoB,EAAiB,MAAM,mCAAmC,CAAC;AAaxF,MAAM,WAAW,UAAU;IACzB,IAAI,EAAE,OAAO,aAAa,CAAC,SAAS,CAAC,oBAAoB,CAAC;IAC1D,GAAG,EAAE,OAAO,aAAa,CAAC,SAAS,CAAC,mBAAmB,CAAC;IACxD,KAAK,EAAE,OAAO,aAAa,CAAC,SAAS,CAAC,KAAK,CAAC;IAC5C,IAAI,EAAE,OAAO,aAAa,CAAC,SAAS,CAAC,UAAU,CAAC;IAChD,gBAAgB,EAAE,OAAO,aAAa,CAAC,SAAS,CAAC,gBAAgB,CAAC;IAClE,mBAAmB,EAAE,OAAO,aAAa,CAAC,SAAS,CAAC,mBAAmB,CAAC;IACxE,OAAO,EAAE,CAAC,OAAO,EAAE,MAAM,KAAK,IAAI,CAAC;CACpC;AAQD,oBAAY,gBAAgB,GAAG,CAAC,OAAO,EAAE,MAAM,EAAE,MAAM,CAAC,EAAE,GAAG,EAAE,UAAU,CAAC,EAAE,GAAG,KAAK,IAAI,CAAC;AACzF,oBAAY,sBAAsB,GAAG,CAAC,OAAO,EAAE,MAAM,GAAG,MAAM,EAAE,KAAK,IAAI,CAAC;AAE1E,qBAAa,aAAc,SAAQ,oBAAoB;IAiClC,eAAe,EAAE,CAAC,MAAM,EAAE,GAAG,EAAE,QAAQ,EAAE,GAAG,KAAK,OAAO,GAAG,IAAI;IAhClF;;;;OAIG;IACH,MAAM,CAAC,eAAe,SAAM;IAE5B;;OAEG;IACH,SAAgB,cAAc,EAAE,aAAa,CAAC,GAAG,CAAC,EAAE,CAAM;IAE1D;;OAEG;IACH,SAAgB,eAAe,EAAE,cAAc,CAAC,GAAG,CAAC,EAAE,CAAM;IAE5D;;OAEG;IACH,OAAO,CAAC,UAAU,CAAuB;IAEzC;;;OAGG;IACH,OAAO,CAAC,OAAO,CAAS;IAExB;;;OAGG;gBACgB,eAAe,EAAE,CAAC,MAAM,EAAE,GAAG,EAAE,QAAQ,EAAE,GAAG,KAAK,OAAO,GAAG,IAAI;IAIlF,UAAU,CAAC,OAAO,EAAE,MAAM,GAAG,MAAM;IAQnC;;;;OAIG;IACH,oBAAoB,CAAC,CAAC,GAAG,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,CAAC,EAAE;QAAE,CAAC,MAAM,EAAE,MAAM,GAAG,CAAC,CAAA;KAAE,EAAE,KAAK,CAAC,EAAE,GAAG,GAAG,cAAc,CAAC,CAAC,CAAC;IAanH;;;;OAIG;IACH,mBAAmB,CAAC,CAAC,GAAG,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,CAAC,EAAE;QAAE,CAAC,MAAM,EAAE,MAAM,GAAG,CAAC,CAAA;KAAE,EAAE,KAAK,CAAC,EAAE,GAAG,GAAG,aAAa,CAAC,CAAC,CAAC;IAUjH,OAAO,CAAC,0BAA0B;IAgBlC,gBAAgB,CAAC,CAAC,EAAE,UAAU,EAAE,UAAU,CAAC,CAAC,CAAC,EAAE,mBAAmB,GAAE,MAAM,GAAG,IAAW;sBAgCtE,MAAM,WAAW,GAAG,eAAe,GAAG;yBAInC,WAAW,CAAC,CAAC;;IAsBlC,mBAAmB,CAAC,sBAAsB,EAAE,eAAe,EAAE,GAAG;QAAE,IAAI,EAAE,sBAAsB,CAAA;KAAE;IAehG,KAAK;IAiBL,MAAM,CAAC,2BAA2B,CAAC,OAAO,EAAE,MAAM,GAAG,IAAI,EAAE,OAAO,UAAQ,GAAG,eAAe;IAiG5F,MAAM,CAAC,YAAY,CACjB,OAAO,EAAE,MAAM,EACf,MAAM,CAAC,EAAE,GAAG,EACZ,UAAU,CAAC,EAAE,GAAG,EAChB,2BAA2B,GAAE,OAAe,EAC5C,OAAO,UAAQ,GACd,WAAW,EAAE;IA4GhB,OAAO,CAAC,cAAc;IA+DtB,OAAO,CAAC,eAAe;IA8IvB;;;;;;;OAOG;IACH,GAAG,CAAC,CAAC,EAAE,QAAQ,EAAE,CAAC,OAAO,EAAE,UAAU,KAAK,CAAC,GAAG,CAAC;CA2ChD"}