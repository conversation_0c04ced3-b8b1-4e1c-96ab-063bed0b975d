<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Payment extends Model
{
    use HasFactory;

    protected $fillable = [
        'payment_id',
        'payment_method',
        'amount',
        'currency',
        'status',
        'booking_ids',
        'customer_info',
        'gateway_order_id',
        'gateway_payment_id',
        'gateway_response',
        'failure_reason',
        'completed_at',
        'failed_at'
    ];

    protected $casts = [
        'booking_ids' => 'array',
        'customer_info' => 'array',
        'gateway_response' => 'array',
        'completed_at' => 'datetime',
        'failed_at' => 'datetime'
    ];

    /**
     * Get the bookings associated with this payment
     */
    public function bookings()
    {
        return $this->hasMany(Booking::class, 'payment_id', 'payment_id');
    }

    /**
     * Check if payment is completed
     */
    public function isCompleted()
    {
        return $this->status === 'completed';
    }

    /**
     * Check if payment is pending
     */
    public function isPending()
    {
        return $this->status === 'pending';
    }

    /**
     * Check if payment failed
     */
    public function isFailed()
    {
        return $this->status === 'failed';
    }

    /**
     * Get formatted amount
     */
    public function getFormattedAmountAttribute()
    {
        return '₹' . number_format($this->amount, 2);
    }
}
