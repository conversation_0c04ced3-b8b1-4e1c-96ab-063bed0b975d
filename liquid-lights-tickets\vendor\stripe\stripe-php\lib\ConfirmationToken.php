<?php

// File generated from our OpenAPI spec

namespace Stripe;

/**
 * ConfirmationTokens help transport client side data collected by <PERSON>e JS over
 * to your server for confirming a PaymentIntent or SetupIntent. If the confirmation
 * is successful, values present on the ConfirmationToken are written onto the Intent.
 *
 * To learn more about how to use ConfirmationToken, visit the related guides:
 * - <a href="https://stripe.com/docs/payments/finalize-payments-on-the-server">Finalize payments on the server</a>
 * - <a href="https://stripe.com/docs/payments/build-a-two-step-confirmation">Build two-step confirmation</a>.
 *
 * @property string $id Unique identifier for the object.
 * @property string $object String representing the object's type. Objects of the same type share the same value.
 * @property int $created Time at which the object was created. Measured in seconds since the Unix epoch.
 * @property null|int $expires_at Time at which this ConfirmationToken expires and can no longer be used to confirm a PaymentIntent or SetupIntent.
 * @property bool $livemode Has the value <code>true</code> if the object exists in live mode or the value <code>false</code> if the object exists in test mode.
 * @property null|(object{customer_acceptance: (object{online: null|(object{ip_address: null|string, user_agent: null|string}&StripeObject), type: string}&StripeObject)}&StripeObject) $mandate_data Data used for generating a Mandate.
 * @property null|string $payment_intent ID of the PaymentIntent that this ConfirmationToken was used to confirm, or null if this ConfirmationToken has not yet been used.
 * @property null|(object{card: null|(object{cvc_token: null|string, installments?: (object{plan?: (object{count: null|int, interval: null|string, type: string}&StripeObject)}&StripeObject)}&StripeObject)}&StripeObject) $payment_method_options Payment-method-specific configuration for this ConfirmationToken.
 * @property null|(object{acss_debit?: (object{bank_name: null|string, fingerprint: null|string, institution_number: null|string, last4: null|string, transit_number: null|string}&StripeObject), affirm?: (object{}&StripeObject), afterpay_clearpay?: (object{}&StripeObject), alipay?: (object{}&StripeObject), allow_redisplay?: string, alma?: (object{}&StripeObject), amazon_pay?: (object{}&StripeObject), au_becs_debit?: (object{bsb_number: null|string, fingerprint: null|string, last4: null|string}&StripeObject), bacs_debit?: (object{fingerprint: null|string, last4: null|string, sort_code: null|string}&StripeObject), bancontact?: (object{}&StripeObject), billie?: (object{}&StripeObject), billing_details: (object{address: null|(object{city: null|string, country: null|string, line1: null|string, line2: null|string, postal_code: null|string, state: null|string}&StripeObject), email: null|string, name: null|string, phone: null|string, tax_id: null|string}&StripeObject), blik?: (object{}&StripeObject), boleto?: (object{tax_id: string}&StripeObject), card?: (object{brand: string, checks: null|(object{address_line1_check: null|string, address_postal_code_check: null|string, cvc_check: null|string}&StripeObject), country: null|string, description?: null|string, display_brand: null|string, exp_month: int, exp_year: int, fingerprint?: null|string, funding: string, generated_from: null|(object{charge: null|string, payment_method_details: null|(object{card_present?: (object{amount_authorized: null|int, brand: null|string, brand_product: null|string, capture_before?: int, cardholder_name: null|string, country: null|string, description?: null|string, emv_auth_data: null|string, exp_month: int, exp_year: int, fingerprint: null|string, funding: null|string, generated_card: null|string, iin?: null|string, incremental_authorization_supported: bool, issuer?: null|string, last4: null|string, network: null|string, network_transaction_id: null|string, offline: null|(object{stored_at: null|int, type: null|string}&StripeObject), overcapture_supported: bool, preferred_locales: null|string[], read_method: null|string, receipt: null|(object{account_type?: string, application_cryptogram: null|string, application_preferred_name: null|string, authorization_code: null|string, authorization_response_code: null|string, cardholder_verification_method: null|string, dedicated_file_name: null|string, terminal_verification_results: null|string, transaction_status_information: null|string}&StripeObject), wallet?: (object{type: string}&StripeObject)}&StripeObject), type: string}&StripeObject), setup_attempt: null|SetupAttempt|string}&StripeObject), iin?: null|string, issuer?: null|string, last4: string, networks: null|(object{available: string[], preferred: null|string}&StripeObject), regulated_status: null|string, three_d_secure_usage: null|(object{supported: bool}&StripeObject), wallet: null|(object{amex_express_checkout?: (object{}&StripeObject), apple_pay?: (object{}&StripeObject), dynamic_last4: null|string, google_pay?: (object{}&StripeObject), link?: (object{}&StripeObject), masterpass?: (object{billing_address: null|(object{city: null|string, country: null|string, line1: null|string, line2: null|string, postal_code: null|string, state: null|string}&StripeObject), email: null|string, name: null|string, shipping_address: null|(object{city: null|string, country: null|string, line1: null|string, line2: null|string, postal_code: null|string, state: null|string}&StripeObject)}&StripeObject), samsung_pay?: (object{}&StripeObject), type: string, visa_checkout?: (object{billing_address: null|(object{city: null|string, country: null|string, line1: null|string, line2: null|string, postal_code: null|string, state: null|string}&StripeObject), email: null|string, name: null|string, shipping_address: null|(object{city: null|string, country: null|string, line1: null|string, line2: null|string, postal_code: null|string, state: null|string}&StripeObject)}&StripeObject)}&StripeObject)}&StripeObject), card_present?: (object{brand: null|string, brand_product: null|string, cardholder_name: null|string, country: null|string, description?: null|string, exp_month: int, exp_year: int, fingerprint: null|string, funding: null|string, iin?: null|string, issuer?: null|string, last4: null|string, networks: null|(object{available: string[], preferred: null|string}&StripeObject), offline: null|(object{stored_at: null|int, type: null|string}&StripeObject), preferred_locales: null|string[], read_method: null|string, wallet?: (object{type: string}&StripeObject)}&StripeObject), cashapp?: (object{buyer_id: null|string, cashtag: null|string}&StripeObject), crypto?: (object{}&StripeObject), customer: null|Customer|string, customer_balance?: (object{}&StripeObject), eps?: (object{bank: null|string}&StripeObject), fpx?: (object{account_holder_type: null|string, bank: string}&StripeObject), giropay?: (object{}&StripeObject), grabpay?: (object{}&StripeObject), ideal?: (object{bank: null|string, bic: null|string}&StripeObject), interac_present?: (object{brand: null|string, cardholder_name: null|string, country: null|string, description?: null|string, exp_month: int, exp_year: int, fingerprint: null|string, funding: null|string, iin?: null|string, issuer?: null|string, last4: null|string, networks: null|(object{available: string[], preferred: null|string}&StripeObject), preferred_locales: null|string[], read_method: null|string}&StripeObject), kakao_pay?: (object{}&StripeObject), klarna?: (object{dob?: null|(object{day: null|int, month: null|int, year: null|int}&StripeObject)}&StripeObject), konbini?: (object{}&StripeObject), kr_card?: (object{brand: null|string, last4: null|string}&StripeObject), link?: (object{email: null|string, persistent_token?: string}&StripeObject), mobilepay?: (object{}&StripeObject), multibanco?: (object{}&StripeObject), naver_pay?: (object{buyer_id: null|string, funding: string}&StripeObject), nz_bank_account?: (object{account_holder_name: null|string, bank_code: string, bank_name: string, branch_code: string, last4: string, suffix: null|string}&StripeObject), oxxo?: (object{}&StripeObject), p24?: (object{bank: null|string}&StripeObject), pay_by_bank?: (object{}&StripeObject), payco?: (object{}&StripeObject), paynow?: (object{}&StripeObject), paypal?: (object{country: null|string, payer_email: null|string, payer_id: null|string}&StripeObject), pix?: (object{}&StripeObject), promptpay?: (object{}&StripeObject), revolut_pay?: (object{}&StripeObject), samsung_pay?: (object{}&StripeObject), satispay?: (object{}&StripeObject), sepa_debit?: (object{bank_code: null|string, branch_code: null|string, country: null|string, fingerprint: null|string, generated_from: null|(object{charge: null|Charge|string, setup_attempt: null|SetupAttempt|string}&StripeObject), last4: null|string}&StripeObject), sofort?: (object{country: null|string}&StripeObject), swish?: (object{}&StripeObject), twint?: (object{}&StripeObject), type: string, us_bank_account?: (object{account_holder_type: null|string, account_type: null|string, bank_name: null|string, financial_connections_account: null|string, fingerprint: null|string, last4: null|string, networks: null|(object{preferred: null|string, supported: string[]}&StripeObject), routing_number: null|string, status_details: null|(object{blocked?: (object{network_code: null|string, reason: null|string}&StripeObject)}&StripeObject)}&StripeObject), wechat_pay?: (object{}&StripeObject), zip?: (object{}&StripeObject)}&StripeObject) $payment_method_preview Payment details collected by the Payment Element, used to create a PaymentMethod when a PaymentIntent or SetupIntent is confirmed with this ConfirmationToken.
 * @property null|string $return_url Return URL used to confirm the Intent.
 * @property null|string $setup_future_usage <p>Indicates that you intend to make future payments with this ConfirmationToken's payment method.</p><p>The presence of this property will <a href="https://stripe.com/docs/payments/save-during-payment">attach the payment method</a> to the PaymentIntent's Customer, if present, after the PaymentIntent is confirmed and any required actions from the user are complete.</p>
 * @property null|string $setup_intent ID of the SetupIntent that this ConfirmationToken was used to confirm, or null if this ConfirmationToken has not yet been used.
 * @property null|(object{address: (object{city: null|string, country: null|string, line1: null|string, line2: null|string, postal_code: null|string, state: null|string}&StripeObject), name: string, phone: null|string}&StripeObject) $shipping Shipping information collected on this ConfirmationToken.
 * @property bool $use_stripe_sdk Indicates whether the Stripe SDK is used to handle confirmation flow. Defaults to <code>true</code> on ConfirmationToken.
 */
class ConfirmationToken extends ApiResource
{
    const OBJECT_NAME = 'confirmation_token';

    const SETUP_FUTURE_USAGE_OFF_SESSION = 'off_session';
    const SETUP_FUTURE_USAGE_ON_SESSION = 'on_session';

    /**
     * Retrieves an existing ConfirmationToken object.
     *
     * @param array|string $id the ID of the API resource to retrieve, or an options array containing an `id` key
     * @param null|array|string $opts
     *
     * @return ConfirmationToken
     *
     * @throws Exception\ApiErrorException if the request fails
     */
    public static function retrieve($id, $opts = null)
    {
        $opts = Util\RequestOptions::parse($opts);
        $instance = new static($id, $opts);
        $instance->refresh();

        return $instance;
    }
}
