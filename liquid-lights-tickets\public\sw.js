// Service Worker for Liquid Lights Tickets PWA
const CACHE_NAME = 'liquid-lights-v1.0.0';
const OFFLINE_URL = '/offline';

// Assets to cache for offline functionality
const STATIC_CACHE_URLS = [
    '/',
    '/offline',
    '/events',
    '/user/dashboard',
    '/user/bookings',
    '/css/app.css',
    '/js/app.js',
    '/js/public.js',
    '/images/logo.png',
    '/images/hero-bg.jpg',
    '/manifest.json'
];

// API endpoints to cache
const API_CACHE_URLS = [
    '/api/events',
    '/api/user/bookings',
    '/api/user/profile'
];

// Install event - cache static assets
self.addEventListener('install', event => {
    console.log('Service Worker installing...');
    
    event.waitUntil(
        caches.open(CACHE_NAME)
            .then(cache => {
                console.log('Caching static assets...');
                return cache.addAll(STATIC_CACHE_URLS);
            })
            .then(() => {
                console.log('Static assets cached successfully');
                return self.skipWaiting();
            })
            .catch(error => {
                console.error('Failed to cache static assets:', error);
            })
    );
});

// Activate event - clean up old caches
self.addEventListener('activate', event => {
    console.log('Service Worker activating...');
    
    event.waitUntil(
        caches.keys()
            .then(cacheNames => {
                return Promise.all(
                    cacheNames.map(cacheName => {
                        if (cacheName !== CACHE_NAME) {
                            console.log('Deleting old cache:', cacheName);
                            return caches.delete(cacheName);
                        }
                    })
                );
            })
            .then(() => {
                console.log('Service Worker activated');
                return self.clients.claim();
            })
    );
});

// Fetch event - handle network requests
self.addEventListener('fetch', event => {
    const { request } = event;
    const url = new URL(request.url);

    // Skip non-GET requests
    if (request.method !== 'GET') {
        return;
    }

    // Skip Chrome extension requests
    if (url.protocol === 'chrome-extension:') {
        return;
    }

    // Handle different types of requests
    if (url.pathname.startsWith('/api/')) {
        // API requests - network first, cache fallback
        event.respondWith(handleApiRequest(request));
    } else if (url.pathname.startsWith('/user/')) {
        // User pages - network first, cache fallback
        event.respondWith(handleUserPageRequest(request));
    } else if (isStaticAsset(url.pathname)) {
        // Static assets - cache first, network fallback
        event.respondWith(handleStaticAssetRequest(request));
    } else {
        // Navigation requests - network first, cache fallback
        event.respondWith(handleNavigationRequest(request));
    }
});

// Handle API requests with network-first strategy
async function handleApiRequest(request) {
    try {
        // Try network first
        const networkResponse = await fetch(request);
        
        if (networkResponse.ok) {
            // Cache successful responses
            const cache = await caches.open(CACHE_NAME);
            cache.put(request, networkResponse.clone());
        }
        
        return networkResponse;
    } catch (error) {
        console.log('Network failed for API request, trying cache:', request.url);
        
        // Fallback to cache
        const cachedResponse = await caches.match(request);
        if (cachedResponse) {
            return cachedResponse;
        }
        
        // Return offline response for API requests
        return new Response(
            JSON.stringify({
                error: 'Offline',
                message: 'This content is not available offline'
            }),
            {
                status: 503,
                headers: { 'Content-Type': 'application/json' }
            }
        );
    }
}

// Handle user page requests
async function handleUserPageRequest(request) {
    try {
        const networkResponse = await fetch(request);
        
        if (networkResponse.ok) {
            const cache = await caches.open(CACHE_NAME);
            cache.put(request, networkResponse.clone());
        }
        
        return networkResponse;
    } catch (error) {
        console.log('Network failed for user page, trying cache:', request.url);
        
        const cachedResponse = await caches.match(request);
        if (cachedResponse) {
            return cachedResponse;
        }
        
        // Fallback to offline page
        return caches.match(OFFLINE_URL);
    }
}

// Handle static asset requests with cache-first strategy
async function handleStaticAssetRequest(request) {
    const cachedResponse = await caches.match(request);
    
    if (cachedResponse) {
        return cachedResponse;
    }
    
    try {
        const networkResponse = await fetch(request);
        
        if (networkResponse.ok) {
            const cache = await caches.open(CACHE_NAME);
            cache.put(request, networkResponse.clone());
        }
        
        return networkResponse;
    } catch (error) {
        console.log('Failed to fetch static asset:', request.url);
        
        // Return a fallback for images
        if (request.destination === 'image') {
            return new Response(
                '<svg xmlns="http://www.w3.org/2000/svg" width="200" height="200" viewBox="0 0 200 200"><rect width="200" height="200" fill="#f3f4f6"/><text x="100" y="100" text-anchor="middle" fill="#9ca3af">Image Unavailable</text></svg>',
                { headers: { 'Content-Type': 'image/svg+xml' } }
            );
        }
        
        throw error;
    }
}

// Handle navigation requests
async function handleNavigationRequest(request) {
    try {
        const networkResponse = await fetch(request);
        
        if (networkResponse.ok) {
            const cache = await caches.open(CACHE_NAME);
            cache.put(request, networkResponse.clone());
        }
        
        return networkResponse;
    } catch (error) {
        console.log('Network failed for navigation, trying cache:', request.url);
        
        const cachedResponse = await caches.match(request);
        if (cachedResponse) {
            return cachedResponse;
        }
        
        // Fallback to offline page
        return caches.match(OFFLINE_URL);
    }
}

// Check if URL is a static asset
function isStaticAsset(pathname) {
    const staticExtensions = ['.css', '.js', '.png', '.jpg', '.jpeg', '.gif', '.svg', '.ico', '.woff', '.woff2', '.ttf'];
    return staticExtensions.some(ext => pathname.endsWith(ext));
}

// Handle background sync for offline actions
self.addEventListener('sync', event => {
    console.log('Background sync triggered:', event.tag);
    
    if (event.tag === 'background-sync-bookings') {
        event.waitUntil(syncOfflineBookings());
    }
});

// Sync offline bookings when connection is restored
async function syncOfflineBookings() {
    try {
        const offlineBookings = await getOfflineBookings();
        
        for (const booking of offlineBookings) {
            try {
                const response = await fetch('/api/booking/sync', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': booking.csrfToken
                    },
                    body: JSON.stringify(booking.data)
                });
                
                if (response.ok) {
                    await removeOfflineBooking(booking.id);
                    console.log('Synced offline booking:', booking.id);
                }
            } catch (error) {
                console.error('Failed to sync booking:', booking.id, error);
            }
        }
    } catch (error) {
        console.error('Background sync failed:', error);
    }
}

// Get offline bookings from IndexedDB
async function getOfflineBookings() {
    // This would integrate with IndexedDB to get stored offline bookings
    return [];
}

// Remove synced offline booking
async function removeOfflineBooking(bookingId) {
    // This would remove the booking from IndexedDB after successful sync
    console.log('Removing synced booking:', bookingId);
}

// Handle push notifications
self.addEventListener('push', event => {
    console.log('Push notification received:', event);
    
    const options = {
        body: 'You have a new notification from Liquid Lights Tickets',
        icon: '/images/icons/icon-192x192.png',
        badge: '/images/icons/badge-72x72.png',
        vibrate: [100, 50, 100],
        data: {
            dateOfArrival: Date.now(),
            primaryKey: 1
        },
        actions: [
            {
                action: 'explore',
                title: 'View Details',
                icon: '/images/icons/action-view.png'
            },
            {
                action: 'close',
                title: 'Close',
                icon: '/images/icons/action-close.png'
            }
        ]
    };
    
    if (event.data) {
        const data = event.data.json();
        options.body = data.body || options.body;
        options.title = data.title || 'Liquid Lights Tickets';
        options.data = { ...options.data, ...data };
    }
    
    event.waitUntil(
        self.registration.showNotification('Liquid Lights Tickets', options)
    );
});

// Handle notification clicks
self.addEventListener('notificationclick', event => {
    console.log('Notification clicked:', event);
    
    event.notification.close();
    
    if (event.action === 'explore') {
        event.waitUntil(
            clients.openWindow('/user/dashboard')
        );
    } else if (event.action === 'close') {
        // Just close the notification
        return;
    } else {
        // Default action - open the app
        event.waitUntil(
            clients.matchAll().then(clientList => {
                if (clientList.length > 0) {
                    return clientList[0].focus();
                }
                return clients.openWindow('/');
            })
        );
    }
});

// Handle notification close
self.addEventListener('notificationclose', event => {
    console.log('Notification closed:', event);
    
    // Track notification dismissal
    event.waitUntil(
        fetch('/api/notifications/dismissed', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                notificationId: event.notification.data.primaryKey,
                dismissedAt: Date.now()
            })
        }).catch(error => {
            console.log('Failed to track notification dismissal:', error);
        })
    );
});

console.log('Service Worker loaded successfully');
