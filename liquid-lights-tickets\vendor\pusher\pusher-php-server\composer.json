{"name": "pusher/pusher-php-server", "description": "Library for interacting with the Pusher REST API", "keywords": ["php-pusher-server", "pusher", "rest", "realtime", "real-time", "real time", "messaging", "push", "trigger", "publish", "events"], "license": "MIT", "require": {"php": "^7.3|^8.0", "ext-curl": "*", "ext-json": "*", "guzzlehttp/guzzle": "^7.2", "psr/log": "^1.0|^2.0|^3.0", "paragonie/sodium_compat": "^1.6|^2.0"}, "require-dev": {"phpunit/phpunit": "^9.3", "overtrue/phplint": "^2.3"}, "autoload": {"psr-4": {"Pusher\\": "src/"}}, "autoload-dev": {"psr-4": {"": "tests/"}}, "config": {"preferred-install": "dist"}, "extra": {"branch-alias": {"dev-master": "5.0-dev"}}, "minimum-stability": "dev", "prefer-stable": true}