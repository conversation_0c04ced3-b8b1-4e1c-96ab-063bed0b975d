#!/bin/bash

echo "🌟 Starting Liquid Lights Tickets Server 🌟"
echo "============================================"

# Kill any existing PHP processes on port 8000
echo "🔄 Stopping existing servers..."
pkill -f "php.*artisan serve" 2>/dev/null || true
sleep 2

# Clear Laravel caches
echo "🧹 Clearing caches..."
php -c php-server.ini artisan config:clear
php -c php-server.ini artisan route:clear
php -c php-server.ini artisan view:clear

# Test database connection
echo "💾 Testing database connection..."
php -c php-server.ini artisan tinker --execute="DB::connection()->getPdo(); echo 'Database connected successfully!';"

# Start the server with custom PHP configuration
echo "🚀 Starting Liquid Lights server..."
echo "📍 URL: http://127.0.0.1:8000"
echo "⚡ Admin: http://127.0.0.1:8000/admin/login"
echo "🔐 OTPless: http://127.0.0.1:8000/otpless-login"
echo ""
echo "Press Ctrl+C to stop the server"
echo "================================"

php -c php-server.ini artisan serve --host=127.0.0.1 --port=8000
