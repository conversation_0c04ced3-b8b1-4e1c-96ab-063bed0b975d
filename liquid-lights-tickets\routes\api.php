<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Auth\AuthController;
use App\Http\Controllers\Admin\AdminAuthController;
use App\Http\Controllers\PushNotificationController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

// Public Authentication Routes
Route::prefix('auth')->group(function () {
    Route::post('/whatsapp/send', [AuthController::class, 'sendWhatsAppLink']);
    Route::post('/email/send', [AuthController::class, 'sendEmailLink']);
    Route::get('/callback', [AuthController::class, 'handleCallback']);
});

// Public Push Notification Routes
Route::get('/notifications/vapid-key', [PushNotificationController::class, 'getVapidPublicKey']);

// Protected User Routes
Route::middleware(['auth:sanctum'])->group(function () {
    Route::prefix('auth')->group(function () {
        Route::get('/profile', [AuthController::class, 'profile']);
        Route::put('/profile', [AuthController::class, 'updateProfile']);
        Route::post('/logout', [AuthController::class, 'logout']);
    });

    // Push Notifications
    Route::prefix('notifications')->group(function () {
        Route::post('/subscribe', [PushNotificationController::class, 'subscribe']);
        Route::post('/unsubscribe', [PushNotificationController::class, 'unsubscribe']);
        Route::get('/preferences', [PushNotificationController::class, 'getPreferences']);
        Route::post('/preferences', [PushNotificationController::class, 'updatePreferences']);
        Route::post('/test', [PushNotificationController::class, 'sendTestNotification']);
    });
});

// Admin Authentication Routes
Route::prefix('admin/auth')->group(function () {
    Route::post('/login', [AdminAuthController::class, 'login']);
});

// Protected Admin Routes
Route::middleware(['auth:sanctum'])->prefix('admin')->group(function () {
    Route::prefix('auth')->group(function () {
        Route::get('/profile', [AdminAuthController::class, 'profile']);
        Route::post('/change-password', [AdminAuthController::class, 'changePassword']);
        Route::post('/logout', [AdminAuthController::class, 'logout']);
        Route::post('/logout-all', [AdminAuthController::class, 'logoutAll']);
    });
});

// Test route to verify API is working
Route::get('/test', function () {
    return response()->json([
        'success' => true,
        'message' => 'Liquid Lights API is working!',
        'timestamp' => now(),
    ]);
});
