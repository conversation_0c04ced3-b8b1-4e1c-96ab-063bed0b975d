// App Installation Manager for Liquid Lights Tickets PWA
class AppInstallationManager {
    constructor() {
        this.deferredPrompt = null;
        this.isInstalled = false;
        this.isStandalone = false;
        this.installButton = null;
        this.installBanner = null;
        
        this.init();
    }

    init() {
        this.checkInstallationStatus();
        this.setupEventListeners();
        this.createInstallPrompts();
        this.trackInstallationMetrics();
        
        console.log('App installation manager initialized');
    }

    checkInstallationStatus() {
        // Check if app is running in standalone mode
        this.isStandalone = window.matchMedia('(display-mode: standalone)').matches ||
                           window.navigator.standalone ||
                           document.referrer.includes('android-app://');

        // Check if app is installed
        this.isInstalled = this.isStandalone || 
                          localStorage.getItem('pwa-installed') === 'true';

        console.log('Installation status:', {
            isStandalone: this.isStandalone,
            isInstalled: this.isInstalled
        });
    }

    setupEventListeners() {
        // Listen for beforeinstallprompt event
        window.addEventListener('beforeinstallprompt', (e) => {
            console.log('PWA install prompt available');
            e.preventDefault();
            this.deferredPrompt = e;
            this.showInstallPrompts();
        });

        // Listen for appinstalled event
        window.addEventListener('appinstalled', (evt) => {
            console.log('PWA was installed');
            this.handleAppInstalled();
        });

        // Listen for standalone mode changes
        window.matchMedia('(display-mode: standalone)').addEventListener('change', (e) => {
            this.isStandalone = e.matches;
            if (this.isStandalone) {
                this.handleAppInstalled();
            }
        });

        // Handle install button clicks
        document.addEventListener('click', (e) => {
            if (e.target.closest('.install-app-btn')) {
                e.preventDefault();
                this.promptInstall();
            }
            
            if (e.target.closest('.install-banner-close')) {
                e.preventDefault();
                this.hideInstallBanner();
            }
        });
    }

    createInstallPrompts() {
        this.createInstallButton();
        this.createInstallBanner();
        
        // Show prompts if app is not installed
        if (!this.isInstalled && this.shouldShowInstallPrompt()) {
            setTimeout(() => {
                this.showInstallPrompts();
            }, 3000); // Show after 3 seconds
        }
    }

    createInstallButton() {
        // Create floating install button
        this.installButton = document.createElement('button');
        this.installButton.className = 'install-app-btn fixed bottom-20 right-4 bg-blue-600 hover:bg-blue-700 text-white p-3 rounded-full shadow-lg transition-all duration-300 z-40 hidden';
        this.installButton.innerHTML = `
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
            </svg>
        `;
        this.installButton.title = 'Install App';
        
        document.body.appendChild(this.installButton);
    }

    createInstallBanner() {
        // Create install banner
        this.installBanner = document.createElement('div');
        this.installBanner.className = 'install-banner fixed top-0 left-0 right-0 bg-blue-600 text-white p-4 z-50 transform -translate-y-full transition-transform duration-300';
        this.installBanner.innerHTML = `
            <div class="flex items-center justify-between max-w-7xl mx-auto">
                <div class="flex items-center space-x-3">
                    <div class="bg-white bg-opacity-20 p-2 rounded-lg">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z"></path>
                        </svg>
                    </div>
                    <div>
                        <div class="font-semibold">Install Liquid Lights Tickets</div>
                        <div class="text-sm opacity-90">Get the full app experience with offline access</div>
                    </div>
                </div>
                <div class="flex items-center space-x-2">
                    <button class="install-app-btn bg-white text-blue-600 px-4 py-2 rounded-lg font-medium hover:bg-gray-100 transition-colors">
                        Install
                    </button>
                    <button class="install-banner-close text-white hover:text-gray-200 p-1">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
            </div>
        `;
        
        document.body.appendChild(this.installBanner);
    }

    shouldShowInstallPrompt() {
        // Don't show if user has dismissed recently
        const dismissedAt = localStorage.getItem('install-prompt-dismissed');
        if (dismissedAt) {
            const daysSinceDismissed = (Date.now() - parseInt(dismissedAt)) / (1000 * 60 * 60 * 24);
            if (daysSinceDismissed < 7) { // Don't show for 7 days after dismissal
                return false;
            }
        }

        // Don't show if user has visited less than 3 times
        const visitCount = parseInt(localStorage.getItem('visit-count') || '0');
        if (visitCount < 3) {
            return false;
        }

        // Don't show on mobile browsers that don't support PWA
        if (this.isMobileSafari() && !this.isIOS13Plus()) {
            return false;
        }

        return true;
    }

    showInstallPrompts() {
        if (this.isInstalled || !this.deferredPrompt) {
            return;
        }

        // Show install button
        if (this.installButton) {
            this.installButton.classList.remove('hidden');
        }

        // Show install banner on desktop or after user interaction
        if (window.innerWidth > 768 || this.hasUserInteracted()) {
            this.showInstallBanner();
        }
    }

    showInstallBanner() {
        if (this.installBanner) {
            this.installBanner.classList.remove('-translate-y-full');
            
            // Auto-hide after 10 seconds
            setTimeout(() => {
                this.hideInstallBanner();
            }, 10000);
        }
    }

    hideInstallBanner() {
        if (this.installBanner) {
            this.installBanner.classList.add('-translate-y-full');
            
            // Track dismissal
            localStorage.setItem('install-prompt-dismissed', Date.now().toString());
        }
    }

    hideInstallPrompts() {
        if (this.installButton) {
            this.installButton.classList.add('hidden');
        }
        this.hideInstallBanner();
    }

    async promptInstall() {
        if (!this.deferredPrompt) {
            this.showManualInstallInstructions();
            return;
        }

        try {
            // Show the install prompt
            this.deferredPrompt.prompt();
            
            // Wait for the user to respond
            const { outcome } = await this.deferredPrompt.userChoice;
            
            console.log(`User response to install prompt: ${outcome}`);
            
            if (outcome === 'accepted') {
                this.trackInstallEvent('prompt_accepted');
            } else {
                this.trackInstallEvent('prompt_dismissed');
                localStorage.setItem('install-prompt-dismissed', Date.now().toString());
            }
            
            // Clear the deferred prompt
            this.deferredPrompt = null;
            this.hideInstallPrompts();
            
        } catch (error) {
            console.error('Error showing install prompt:', error);
            this.showManualInstallInstructions();
        }
    }

    showManualInstallInstructions() {
        const instructions = this.getManualInstallInstructions();
        
        // Create modal with instructions
        const modal = document.createElement('div');
        modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4';
        modal.innerHTML = `
            <div class="bg-white rounded-lg max-w-md w-full p-6">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-semibold">Install App</h3>
                    <button class="close-modal text-gray-400 hover:text-gray-600">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
                <div class="space-y-4">
                    ${instructions}
                </div>
                <div class="mt-6 flex justify-end">
                    <button class="close-modal bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700">
                        Got it
                    </button>
                </div>
            </div>
        `;
        
        // Handle modal close
        modal.addEventListener('click', (e) => {
            if (e.target.classList.contains('close-modal') || e.target === modal) {
                modal.remove();
            }
        });
        
        document.body.appendChild(modal);
    }

    getManualInstallInstructions() {
        if (this.isIOS()) {
            return `
                <div class="text-center">
                    <div class="mb-4">
                        <svg class="w-12 h-12 mx-auto text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z"></path>
                        </svg>
                    </div>
                    <p class="text-gray-600 mb-4">To install this app on your iPhone:</p>
                    <ol class="text-left space-y-2 text-sm">
                        <li class="flex items-start">
                            <span class="bg-blue-100 text-blue-600 rounded-full w-6 h-6 flex items-center justify-center text-xs font-bold mr-3 mt-0.5">1</span>
                            Tap the Share button in Safari
                        </li>
                        <li class="flex items-start">
                            <span class="bg-blue-100 text-blue-600 rounded-full w-6 h-6 flex items-center justify-center text-xs font-bold mr-3 mt-0.5">2</span>
                            Scroll down and tap "Add to Home Screen"
                        </li>
                        <li class="flex items-start">
                            <span class="bg-blue-100 text-blue-600 rounded-full w-6 h-6 flex items-center justify-center text-xs font-bold mr-3 mt-0.5">3</span>
                            Tap "Add" to install the app
                        </li>
                    </ol>
                </div>
            `;
        } else if (this.isAndroid()) {
            return `
                <div class="text-center">
                    <div class="mb-4">
                        <svg class="w-12 h-12 mx-auto text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z"></path>
                        </svg>
                    </div>
                    <p class="text-gray-600 mb-4">To install this app on your Android device:</p>
                    <ol class="text-left space-y-2 text-sm">
                        <li class="flex items-start">
                            <span class="bg-green-100 text-green-600 rounded-full w-6 h-6 flex items-center justify-center text-xs font-bold mr-3 mt-0.5">1</span>
                            Tap the menu button (⋮) in Chrome
                        </li>
                        <li class="flex items-start">
                            <span class="bg-green-100 text-green-600 rounded-full w-6 h-6 flex items-center justify-center text-xs font-bold mr-3 mt-0.5">2</span>
                            Tap "Add to Home screen"
                        </li>
                        <li class="flex items-start">
                            <span class="bg-green-100 text-green-600 rounded-full w-6 h-6 flex items-center justify-center text-xs font-bold mr-3 mt-0.5">3</span>
                            Tap "Add" to install the app
                        </li>
                    </ol>
                </div>
            `;
        } else {
            return `
                <div class="text-center">
                    <div class="mb-4">
                        <svg class="w-12 h-12 mx-auto text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                        </svg>
                    </div>
                    <p class="text-gray-600 mb-4">To install this app on your computer:</p>
                    <ol class="text-left space-y-2 text-sm">
                        <li class="flex items-start">
                            <span class="bg-blue-100 text-blue-600 rounded-full w-6 h-6 flex items-center justify-center text-xs font-bold mr-3 mt-0.5">1</span>
                            Look for the install icon in your browser's address bar
                        </li>
                        <li class="flex items-start">
                            <span class="bg-blue-100 text-blue-600 rounded-full w-6 h-6 flex items-center justify-center text-xs font-bold mr-3 mt-0.5">2</span>
                            Click the install button when it appears
                        </li>
                        <li class="flex items-start">
                            <span class="bg-blue-100 text-blue-600 rounded-full w-6 h-6 flex items-center justify-center text-xs font-bold mr-3 mt-0.5">3</span>
                            Follow the prompts to install the app
                        </li>
                    </ol>
                </div>
            `;
        }
    }

    handleAppInstalled() {
        this.isInstalled = true;
        localStorage.setItem('pwa-installed', 'true');
        this.hideInstallPrompts();
        this.trackInstallEvent('app_installed');
        
        // Show success message
        this.showInstallSuccessMessage();
        
        // Setup app-specific features
        this.setupInstalledAppFeatures();
    }

    showInstallSuccessMessage() {
        const message = document.createElement('div');
        message.className = 'fixed top-4 right-4 bg-green-600 text-white p-4 rounded-lg shadow-lg z-50 max-w-sm';
        message.innerHTML = `
            <div class="flex items-center">
                <svg class="w-6 h-6 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
                <div>
                    <div class="font-semibold">App Installed!</div>
                    <div class="text-sm opacity-90">You can now access Liquid Lights Tickets from your home screen.</div>
                </div>
            </div>
        `;
        
        document.body.appendChild(message);
        
        // Auto-remove after 5 seconds
        setTimeout(() => {
            message.remove();
        }, 5000);
    }

    setupInstalledAppFeatures() {
        // Enable additional features for installed app
        document.body.classList.add('app-installed');
        
        // Setup app shortcuts
        this.setupAppShortcuts();
        
        // Enable enhanced notifications
        if (window.pushManager) {
            window.pushManager.init();
        }
    }

    setupAppShortcuts() {
        // This would be handled by the manifest.json shortcuts
        console.log('App shortcuts available via manifest');
    }

    trackInstallationMetrics() {
        // Track visit count
        const visitCount = parseInt(localStorage.getItem('visit-count') || '0') + 1;
        localStorage.setItem('visit-count', visitCount.toString());
        
        // Track installation funnel
        this.trackInstallEvent('page_visit', { visit_count: visitCount });
    }

    trackInstallEvent(event, data = {}) {
        // Track with analytics service
        if (typeof gtag !== 'undefined') {
            gtag('event', event, {
                event_category: 'pwa_install',
                event_label: navigator.userAgent,
                ...data
            });
        }
        
        console.log('Install event tracked:', event, data);
    }

    // Utility methods
    isIOS() {
        return /iPad|iPhone|iPod/.test(navigator.userAgent);
    }

    isAndroid() {
        return /Android/.test(navigator.userAgent);
    }

    isMobileSafari() {
        return this.isIOS() && /Safari/.test(navigator.userAgent) && !/CriOS|FxiOS/.test(navigator.userAgent);
    }

    isIOS13Plus() {
        const match = navigator.userAgent.match(/OS (\d+)_/);
        return match && parseInt(match[1]) >= 13;
    }

    hasUserInteracted() {
        return localStorage.getItem('user-interacted') === 'true';
    }

    // Public API
    getInstallationStatus() {
        return {
            isInstalled: this.isInstalled,
            isStandalone: this.isStandalone,
            canInstall: !!this.deferredPrompt,
            platform: this.isIOS() ? 'ios' : this.isAndroid() ? 'android' : 'desktop'
        };
    }

    forceShowInstallPrompt() {
        this.showInstallPrompts();
    }

    hideInstallUI() {
        this.hideInstallPrompts();
        localStorage.setItem('install-prompt-dismissed', Date.now().toString());
    }
}

// Initialize app installation manager
let installManager;

document.addEventListener('DOMContentLoaded', () => {
    installManager = new AppInstallationManager();
    
    // Track user interaction
    document.addEventListener('click', () => {
        localStorage.setItem('user-interacted', 'true');
    }, { once: true });
});

// Export for global access
window.installManager = installManager;
