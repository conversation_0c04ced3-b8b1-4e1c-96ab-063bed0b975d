<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use App\Models\Artist;

class ArtistController extends Controller
{
    /**
     * Display a listing of artists
     */
    public function index(Request $request)
    {
        $query = Artist::query();

        // Search functionality
        if ($request->has('search')) {
            $search = $request->search;
            $query->where('name', 'like', "%{$search}%");
        }

        // Filter by status
        if ($request->has('status')) {
            $query->where('is_active', $request->status === 'active');
        }

        // Sorting
        $sortBy = $request->get('sort_by', 'name');
        $sortOrder = $request->get('sort_order', 'asc');
        $query->orderBy($sortBy, $sortOrder);

        // Pagination
        $perPage = $request->get('per_page', 15);
        $artists = $query->paginate($perPage);

        return response()->json([
            'success' => true,
            'data' => $artists
        ]);
    }

    /**
     * Store a newly created artist
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'bio' => 'nullable|string',
            'image' => 'nullable|string',
            'instagram_handle' => 'nullable|string|max:255',
            'spotify_link' => 'nullable|url',
            'is_active' => 'boolean',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $artist = Artist::create($request->all());

        return response()->json([
            'success' => true,
            'message' => 'Artist created successfully',
            'data' => $artist
        ], 201);
    }

    /**
     * Display the specified artist
     */
    public function show($id)
    {
        $artist = Artist::with(['events'])->findOrFail($id);

        return response()->json([
            'success' => true,
            'data' => $artist
        ]);
    }

    /**
     * Update the specified artist
     */
    public function update(Request $request, $id)
    {
        $artist = Artist::findOrFail($id);

        $validator = Validator::make($request->all(), [
            'name' => 'sometimes|string|max:255',
            'bio' => 'nullable|string',
            'image' => 'nullable|string',
            'instagram_handle' => 'nullable|string|max:255',
            'spotify_link' => 'nullable|url',
            'is_active' => 'boolean',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $artist->update($request->all());

        return response()->json([
            'success' => true,
            'message' => 'Artist updated successfully',
            'data' => $artist
        ]);
    }

    /**
     * Remove the specified artist
     */
    public function destroy($id)
    {
        $artist = Artist::findOrFail($id);

        // Check if artist has events
        if ($artist->events()->exists()) {
            return response()->json([
                'success' => false,
                'message' => 'Cannot delete artist with associated events'
            ], 400);
        }

        $artist->delete();

        return response()->json([
            'success' => true,
            'message' => 'Artist deleted successfully'
        ]);
    }

    /**
     * Get all active artists for dropdown/selection
     */
    public function active()
    {
        $artists = Artist::active()
            ->select('id', 'name', 'image')
            ->orderBy('name')
            ->get();

        return response()->json([
            'success' => true,
            'data' => $artists
        ]);
    }
}
