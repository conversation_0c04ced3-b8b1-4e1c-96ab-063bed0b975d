<?php

require_once 'vendor/autoload.php';

use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;
use Illuminate\Support\Facades\Hash;

// Bootstrap Laravel
$app = Application::configure(basePath: __DIR__)
    ->withRouting(
        web: __DIR__.'/routes/web.php',
        api: __DIR__.'/routes/api.php',
        commands: __DIR__.'/routes/console.php',
        health: '/up',
    )
    ->withMiddleware(function (Middleware $middleware): void {
        $middleware->alias([
            'role' => \App\Http\Middleware\RoleMiddleware::class,
            'admin.auth' => \App\Http\Middleware\AdminAuth::class,
        ]);
    })
    ->withExceptions(function (Exceptions $exceptions): void {
        //
    })->create();

$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

echo "🔐 Admin Login Test\n";
echo "==================\n\n";

// Test admin user exists
try {
    $admin = App\Models\User::where('email', '<EMAIL>')->first();
    if ($admin) {
        echo "✅ Admin User Found: {$admin->email}\n";
        echo "   - Role: {$admin->role}\n";
        echo "   - Active: " . ($admin->is_active ? 'Yes' : 'No') . "\n";
        
        // Test password
        if (Hash::check('admin123', $admin->password)) {
            echo "✅ Password Verification: SUCCESS\n";
        } else {
            echo "❌ Password Verification: FAILED\n";
        }
        
        // Test role check
        if (in_array($admin->role, ['admin', 'manager', 'scanner', 'booker'])) {
            echo "✅ Role Check: SUCCESS (has admin privileges)\n";
        } else {
            echo "❌ Role Check: FAILED (no admin privileges)\n";
        }
        
    } else {
        echo "❌ Admin User: NOT FOUND\n";
    }
} catch (Exception $e) {
    echo "❌ Admin User Test: FAILED - " . $e->getMessage() . "\n";
}

echo "\n🌐 URLs to Test:\n";
echo "================\n";
echo "Admin Login: http://127.0.0.1:8000/admin/login\n";
echo "Admin Dashboard: http://127.0.0.1:8000/admin/dashboard\n";
echo "Homepage: http://127.0.0.1:8000\n";

echo "\n📋 Login Instructions:\n";
echo "======================\n";
echo "1. Go to: http://127.0.0.1:8000/admin/login\n";
echo "2. Email: <EMAIL>\n";
echo "3. Password: admin123\n";
echo "4. Click 'Sign In to Dashboard'\n";

echo "\n🔧 If login fails, check:\n";
echo "=========================\n";
echo "1. Database connection\n";
echo "2. User exists and is active\n";
echo "3. Password is correct\n";
echo "4. User has admin role\n";
echo "5. Session configuration\n";

echo "\n✅ Test Complete!\n";
