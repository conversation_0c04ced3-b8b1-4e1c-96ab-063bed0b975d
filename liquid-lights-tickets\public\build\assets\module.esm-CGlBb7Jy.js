var Z=["input","select","textarea","a[href]","button","[tabindex]:not(slot)","audio[controls]","video[controls]",'[contenteditable]:not([contenteditable="false"])',"details>summary:first-of-type","details"],I=Z.join(","),Q=typeof Element>"u",N=Q?function(){}:Element.prototype.matches||Element.prototype.msMatchesSelector||Element.prototype.webkitMatchesSelector,_=!Q&&Element.prototype.getRootNode?function(i){return i.getRootNode()}:function(i){return i.ownerDocument},X=function(e,t,r){var n=Array.prototype.slice.apply(e.querySelectorAll(I));return t&&N.call(e,I)&&n.unshift(e),n=n.filter(r),n},J=function i(e,t,r){for(var n=[],a=Array.from(e);a.length;){var s=a.shift();if(s.tagName==="SLOT"){var l=s.assignedElements(),g=l.length?l:s.children,b=i(g,!0,r);r.flatten?n.push.apply(n,b):n.push({scope:s,candidates:b})}else{var d=N.call(s,I);d&&r.filter(s)&&(t||!e.includes(s))&&n.push(s);var f=s.shadowRoot||typeof r.getShadowRoot=="function"&&r.getShadowRoot(s),y=!r.shadowRootFilter||r.shadowRootFilter(s);if(f&&y){var w=i(f===!0?s.children:f.children,!0,r);r.flatten?n.push.apply(n,w):n.push({scope:s,candidates:w})}else a.unshift.apply(a,s.children)}}return n},ee=function(e,t){return e.tabIndex<0&&(t||/^(AUDIO|VIDEO|DETAILS)$/.test(e.tagName)||e.isContentEditable)&&isNaN(parseInt(e.getAttribute("tabindex"),10))?0:e.tabIndex},oe=function(e,t){return e.tabIndex===t.tabIndex?e.documentOrder-t.documentOrder:e.tabIndex-t.tabIndex},te=function(e){return e.tagName==="INPUT"},ue=function(e){return te(e)&&e.type==="hidden"},se=function(e){var t=e.tagName==="DETAILS"&&Array.prototype.slice.apply(e.children).some(function(r){return r.tagName==="SUMMARY"});return t},ce=function(e,t){for(var r=0;r<e.length;r++)if(e[r].checked&&e[r].form===t)return e[r]},le=function(e){if(!e.name)return!0;var t=e.form||_(e),r=function(l){return t.querySelectorAll('input[type="radio"][name="'+l+'"]')},n;if(typeof window<"u"&&typeof window.CSS<"u"&&typeof window.CSS.escape=="function")n=r(window.CSS.escape(e.name));else try{n=r(e.name)}catch(s){return console.error("Looks like you have a radio button with a name attribute containing invalid CSS selector characters and need the CSS.escape polyfill: %s",s.message),!1}var a=ce(n,e.form);return!a||a===e},fe=function(e){return te(e)&&e.type==="radio"},de=function(e){return fe(e)&&!le(e)},G=function(e){var t=e.getBoundingClientRect(),r=t.width,n=t.height;return r===0&&n===0},ve=function(e,t){var r=t.displayCheck,n=t.getShadowRoot;if(getComputedStyle(e).visibility==="hidden")return!0;var a=N.call(e,"details>summary:first-of-type"),s=a?e.parentElement:e;if(N.call(s,"details:not([open]) *"))return!0;var l=_(e).host,g=(l==null?void 0:l.ownerDocument.contains(l))||e.ownerDocument.contains(e);if(!r||r==="full"){if(typeof n=="function"){for(var b=e;e;){var d=e.parentElement,f=_(e);if(d&&!d.shadowRoot&&n(d)===!0)return G(e);e.assignedSlot?e=e.assignedSlot:!d&&f!==e.ownerDocument?e=f.host:e=d}e=b}if(g)return!e.getClientRects().length}else if(r==="non-zero-area")return G(e);return!1},be=function(e){if(/^(INPUT|BUTTON|SELECT|TEXTAREA)$/.test(e.tagName))for(var t=e.parentElement;t;){if(t.tagName==="FIELDSET"&&t.disabled){for(var r=0;r<t.children.length;r++){var n=t.children.item(r);if(n.tagName==="LEGEND")return N.call(t,"fieldset[disabled] *")?!0:!n.contains(e)}return!0}t=t.parentElement}return!1},k=function(e,t){return!(t.disabled||ue(t)||ve(t,e)||se(t)||be(t))},B=function(e,t){return!(de(t)||ee(t)<0||!k(e,t))},he=function(e){var t=parseInt(e.getAttribute("tabindex"),10);return!!(isNaN(t)||t>=0)},pe=function i(e){var t=[],r=[];return e.forEach(function(n,a){var s=!!n.scope,l=s?n.scope:n,g=ee(l,s),b=s?i(n.candidates):l;g===0?s?t.push.apply(t,b):t.push(l):r.push({documentOrder:a,tabIndex:g,item:n,isScope:s,content:b})}),r.sort(oe).reduce(function(n,a){return a.isScope?n.push.apply(n,a.content):n.push(a.content),n},[]).concat(t)},ge=function(e,t){t=t||{};var r;return t.getShadowRoot?r=J([e],t.includeContainer,{filter:B.bind(null,t),flatten:!1,getShadowRoot:t.getShadowRoot,shadowRootFilter:he}):r=X(e,t.includeContainer,B.bind(null,t)),pe(r)},re=function(e,t){t=t||{};var r;return t.getShadowRoot?r=J([e],t.includeContainer,{filter:k.bind(null,t),flatten:!0,getShadowRoot:t.getShadowRoot}):r=X(e,t.includeContainer,k.bind(null,t)),r},D=function(e,t){if(t=t||{},!e)throw new Error("No node provided");return N.call(e,I)===!1?!1:B(t,e)},me=Z.concat("iframe").join(","),A=function(e,t){if(t=t||{},!e)throw new Error("No node provided");return N.call(e,me)===!1?!1:k(t,e)};function q(i,e){var t=Object.keys(i);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(i);e&&(r=r.filter(function(n){return Object.getOwnPropertyDescriptor(i,n).enumerable})),t.push.apply(t,r)}return t}function U(i){for(var e=1;e<arguments.length;e++){var t=arguments[e]!=null?arguments[e]:{};e%2?q(Object(t),!0).forEach(function(r){ye(i,r,t[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(i,Object.getOwnPropertyDescriptors(t)):q(Object(t)).forEach(function(r){Object.defineProperty(i,r,Object.getOwnPropertyDescriptor(t,r))})}return i}function ye(i,e,t){return e in i?Object.defineProperty(i,e,{value:t,enumerable:!0,configurable:!0,writable:!0}):i[e]=t,i}var V=function(){var i=[];return{activateTrap:function(t){if(i.length>0){var r=i[i.length-1];r!==t&&r.pause()}var n=i.indexOf(t);n===-1||i.splice(n,1),i.push(t)},deactivateTrap:function(t){var r=i.indexOf(t);r!==-1&&i.splice(r,1),i.length>0&&i[i.length-1].unpause()}}}(),we=function(e){return e.tagName&&e.tagName.toLowerCase()==="input"&&typeof e.select=="function"},Se=function(e){return e.key==="Escape"||e.key==="Esc"||e.keyCode===27},Fe=function(e){return e.key==="Tab"||e.keyCode===9},W=function(e){return setTimeout(e,0)},K=function(e,t){var r=-1;return e.every(function(n,a){return t(n)?(r=a,!1):!0}),r},C=function(e){for(var t=arguments.length,r=new Array(t>1?t-1:0),n=1;n<t;n++)r[n-1]=arguments[n];return typeof e=="function"?e.apply(void 0,r):e},O=function(e){return e.target.shadowRoot&&typeof e.composedPath=="function"?e.composedPath()[0]:e.target},Te=function(e,t){var r=(t==null?void 0:t.document)||document,n=U({returnFocusOnDeactivate:!0,escapeDeactivates:!0,delayInitialFocus:!0},t),a={containers:[],containerGroups:[],tabbableGroups:[],nodeFocusedBeforeActivation:null,mostRecentlyFocusedNode:null,active:!1,paused:!1,delayInitialFocusTimer:void 0},s,l=function(o,u,c){return o&&o[u]!==void 0?o[u]:n[c||u]},g=function(o){return a.containerGroups.findIndex(function(u){var c=u.container,p=u.tabbableNodes;return c.contains(o)||p.find(function(v){return v===o})})},b=function(o){var u=n[o];if(typeof u=="function"){for(var c=arguments.length,p=new Array(c>1?c-1:0),v=1;v<c;v++)p[v-1]=arguments[v];u=u.apply(void 0,p)}if(u===!0&&(u=void 0),!u){if(u===void 0||u===!1)return u;throw new Error("`".concat(o,"` was specified but was not a node, or did not return a node"))}var m=u;if(typeof u=="string"&&(m=r.querySelector(u),!m))throw new Error("`".concat(o,"` as selector refers to no known node"));return m},d=function(){var o=b("initialFocus");if(o===!1)return!1;if(o===void 0)if(g(r.activeElement)>=0)o=r.activeElement;else{var u=a.tabbableGroups[0],c=u&&u.firstTabbableNode;o=c||b("fallbackFocus")}if(!o)throw new Error("Your focus-trap needs to have at least one focusable element");return o},f=function(){if(a.containerGroups=a.containers.map(function(o){var u=ge(o,n.tabbableOptions),c=re(o,n.tabbableOptions);return{container:o,tabbableNodes:u,focusableNodes:c,firstTabbableNode:u.length>0?u[0]:null,lastTabbableNode:u.length>0?u[u.length-1]:null,nextTabbableNode:function(v){var m=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!0,F=c.findIndex(function(E){return E===v});if(!(F<0))return m?c.slice(F+1).find(function(E){return D(E,n.tabbableOptions)}):c.slice(0,F).reverse().find(function(E){return D(E,n.tabbableOptions)})}}}),a.tabbableGroups=a.containerGroups.filter(function(o){return o.tabbableNodes.length>0}),a.tabbableGroups.length<=0&&!b("fallbackFocus"))throw new Error("Your focus-trap must have at least one container with at least one tabbable node in it at all times")},y=function h(o){if(o!==!1&&o!==r.activeElement){if(!o||!o.focus){h(d());return}o.focus({preventScroll:!!n.preventScroll}),a.mostRecentlyFocusedNode=o,we(o)&&o.select()}},w=function(o){var u=b("setReturnFocus",o);return u||(u===!1?!1:o)},T=function(o){var u=O(o);if(!(g(u)>=0)){if(C(n.clickOutsideDeactivates,o)){s.deactivate({returnFocus:n.returnFocusOnDeactivate&&!A(u,n.tabbableOptions)});return}C(n.allowOutsideClick,o)||o.preventDefault()}},R=function(o){var u=O(o),c=g(u)>=0;c||u instanceof Document?c&&(a.mostRecentlyFocusedNode=u):(o.stopImmediatePropagation(),y(a.mostRecentlyFocusedNode||d()))},S=function(o){var u=O(o);f();var c=null;if(a.tabbableGroups.length>0){var p=g(u),v=p>=0?a.containerGroups[p]:void 0;if(p<0)o.shiftKey?c=a.tabbableGroups[a.tabbableGroups.length-1].lastTabbableNode:c=a.tabbableGroups[0].firstTabbableNode;else if(o.shiftKey){var m=K(a.tabbableGroups,function(P){var L=P.firstTabbableNode;return u===L});if(m<0&&(v.container===u||A(u,n.tabbableOptions)&&!D(u,n.tabbableOptions)&&!v.nextTabbableNode(u,!1))&&(m=p),m>=0){var F=m===0?a.tabbableGroups.length-1:m-1,E=a.tabbableGroups[F];c=E.lastTabbableNode}}else{var x=K(a.tabbableGroups,function(P){var L=P.lastTabbableNode;return u===L});if(x<0&&(v.container===u||A(u,n.tabbableOptions)&&!D(u,n.tabbableOptions)&&!v.nextTabbableNode(u))&&(x=p),x>=0){var ne=x===a.tabbableGroups.length-1?0:x+1,ie=a.tabbableGroups[ne];c=ie.firstTabbableNode}}}else c=b("fallbackFocus");c&&(o.preventDefault(),y(c))},j=function(o){if(Se(o)&&C(n.escapeDeactivates,o)!==!1){o.preventDefault(),s.deactivate();return}if(Fe(o)){S(o);return}},H=function(o){var u=O(o);g(u)>=0||C(n.clickOutsideDeactivates,o)||C(n.allowOutsideClick,o)||(o.preventDefault(),o.stopImmediatePropagation())},M=function(){if(a.active)return V.activateTrap(s),a.delayInitialFocusTimer=n.delayInitialFocus?W(function(){y(d())}):y(d()),r.addEventListener("focusin",R,!0),r.addEventListener("mousedown",T,{capture:!0,passive:!1}),r.addEventListener("touchstart",T,{capture:!0,passive:!1}),r.addEventListener("click",H,{capture:!0,passive:!1}),r.addEventListener("keydown",j,{capture:!0,passive:!1}),s},$=function(){if(a.active)return r.removeEventListener("focusin",R,!0),r.removeEventListener("mousedown",T,!0),r.removeEventListener("touchstart",T,!0),r.removeEventListener("click",H,!0),r.removeEventListener("keydown",j,!0),s};return s={get active(){return a.active},get paused(){return a.paused},activate:function(o){if(a.active)return this;var u=l(o,"onActivate"),c=l(o,"onPostActivate"),p=l(o,"checkCanFocusTrap");p||f(),a.active=!0,a.paused=!1,a.nodeFocusedBeforeActivation=r.activeElement,u&&u();var v=function(){p&&f(),M(),c&&c()};return p?(p(a.containers.concat()).then(v,v),this):(v(),this)},deactivate:function(o){if(!a.active)return this;var u=U({onDeactivate:n.onDeactivate,onPostDeactivate:n.onPostDeactivate,checkCanReturnFocus:n.checkCanReturnFocus},o);clearTimeout(a.delayInitialFocusTimer),a.delayInitialFocusTimer=void 0,$(),a.active=!1,a.paused=!1,V.deactivateTrap(s);var c=l(u,"onDeactivate"),p=l(u,"onPostDeactivate"),v=l(u,"checkCanReturnFocus"),m=l(u,"returnFocus","returnFocusOnDeactivate");c&&c();var F=function(){W(function(){m&&y(w(a.nodeFocusedBeforeActivation)),p&&p()})};return m&&v?(v(w(a.nodeFocusedBeforeActivation)).then(F,F),this):(F(),this)},pause:function(){return a.paused||!a.active?this:(a.paused=!0,$(),this)},unpause:function(){return!a.paused||!a.active?this:(a.paused=!1,f(),M(),this)},updateContainerElements:function(o){var u=[].concat(o).filter(Boolean);return a.containers=u.map(function(c){return typeof c=="string"?r.querySelector(c):c}),a.active&&f(),this}},s.updateContainerElements(e),s};function Ee(i){let e,t;window.addEventListener("focusin",()=>{e=t,t=document.activeElement}),i.magic("focus",r=>{let n=r;return{__noscroll:!1,__wrapAround:!1,within(a){return n=a,this},withoutScrolling(){return this.__noscroll=!0,this},noscroll(){return this.__noscroll=!0,this},withWrapAround(){return this.__wrapAround=!0,this},wrap(){return this.withWrapAround()},focusable(a){return A(a)},previouslyFocused(){return e},lastFocused(){return e},focused(){return t},focusables(){return Array.isArray(n)?n:re(n,{displayCheck:"none"})},all(){return this.focusables()},isFirst(a){let s=this.all();return s[0]&&s[0].isSameNode(a)},isLast(a){let s=this.all();return s.length&&s.slice(-1)[0].isSameNode(a)},getFirst(){return this.all()[0]},getLast(){return this.all().slice(-1)[0]},getNext(){let a=this.all(),s=document.activeElement;if(a.indexOf(s)!==-1)return this.__wrapAround&&a.indexOf(s)===a.length-1?a[0]:a[a.indexOf(s)+1]},getPrevious(){let a=this.all(),s=document.activeElement;if(a.indexOf(s)!==-1)return this.__wrapAround&&a.indexOf(s)===0?a.slice(-1)[0]:a[a.indexOf(s)-1]},first(){this.focus(this.getFirst())},last(){this.focus(this.getLast())},next(){this.focus(this.getNext())},previous(){this.focus(this.getPrevious())},prev(){return this.previous()},focus(a){a&&setTimeout(()=>{a.hasAttribute("tabindex")||a.setAttribute("tabindex","0"),a.focus({preventScroll:this.__noscroll})})}}}),i.directive("trap",i.skipDuringClone((r,{expression:n,modifiers:a},{effect:s,evaluateLater:l,cleanup:g})=>{let b=l(n),d=!1,f={escapeDeactivates:!1,allowOutsideClick:!0,fallbackFocus:()=>r};if(a.includes("noautofocus"))f.initialFocus=!1;else{let S=r.querySelector("[autofocus]");S&&(f.initialFocus=S)}let y=Te(r,f),w=()=>{},T=()=>{};const R=()=>{w(),w=()=>{},T(),T=()=>{},y.deactivate({returnFocus:!a.includes("noreturn")})};s(()=>b(S=>{d!==S&&(S&&!d&&(a.includes("noscroll")&&(T=Ne()),a.includes("inert")&&(w=Y(r)),setTimeout(()=>{y.activate()},15)),!S&&d&&R(),d=!!S)})),g(R)},(r,{expression:n,modifiers:a},{evaluate:s})=>{a.includes("inert")&&s(n)&&Y(r)}))}function Y(i){let e=[];return ae(i,t=>{let r=t.hasAttribute("aria-hidden");t.setAttribute("aria-hidden","true"),e.push(()=>r||t.removeAttribute("aria-hidden"))}),()=>{for(;e.length;)e.pop()()}}function ae(i,e){i.isSameNode(document.body)||!i.parentNode||Array.from(i.parentNode.children).forEach(t=>{t.isSameNode(i)?ae(i.parentNode,e):e(t)})}function Ne(){let i=document.documentElement.style.overflow,e=document.documentElement.style.paddingRight,t=window.innerWidth-document.documentElement.clientWidth;return document.documentElement.style.overflow="hidden",document.documentElement.style.paddingRight=`${t}px`,()=>{document.documentElement.style.overflow=i,document.documentElement.style.paddingRight=e}}var xe=Ee;/*! Bundled license information:

tabbable/dist/index.esm.js:
  (*!
  * tabbable 5.3.3
  * @license MIT, https://github.com/focus-trap/tabbable/blob/master/LICENSE
  *)

focus-trap/dist/focus-trap.esm.js:
  (*!
  * focus-trap 6.9.4
  * @license MIT, https://github.com/focus-trap/focus-trap/blob/master/LICENSE
  *)
*/function Re(i){i.directive("collapse",e),e.inline=(t,{modifiers:r})=>{r.includes("min")&&(t._x_doShow=()=>{},t._x_doHide=()=>{})};function e(t,{modifiers:r}){let n=z(r,"duration",250)/1e3,a=z(r,"min",0),s=!r.includes("min");t._x_isShown||(t.style.height=`${a}px`),!t._x_isShown&&s&&(t.hidden=!0),t._x_isShown||(t.style.overflow="hidden");let l=(b,d)=>{let f=i.setStyles(b,d);return d.height?()=>{}:f},g={transitionProperty:"height",transitionDuration:`${n}s`,transitionTimingFunction:"cubic-bezier(0.4, 0.0, 0.2, 1)"};t._x_transition={in(b=()=>{},d=()=>{}){s&&(t.hidden=!1),s&&(t.style.display=null);let f=t.getBoundingClientRect().height;t.style.height="auto";let y=t.getBoundingClientRect().height;f===y&&(f=a),i.transition(t,i.setStyles,{during:g,start:{height:f+"px"},end:{height:y+"px"}},()=>t._x_isShown=!0,()=>{Math.abs(t.getBoundingClientRect().height-y)<1&&(t.style.overflow=null)})},out(b=()=>{},d=()=>{}){let f=t.getBoundingClientRect().height;i.transition(t,l,{during:g,start:{height:f+"px"},end:{height:a+"px"}},()=>t.style.overflow="hidden",()=>{t._x_isShown=!1,t.style.height==`${a}px`&&s&&(t.style.display="none",t.hidden=!0)})}}}}function z(i,e,t){if(i.indexOf(e)===-1)return t;const r=i[i.indexOf(e)+1];if(!r)return t;if(e==="duration"){let n=r.match(/([0-9]+)ms/);if(n)return n[1]}if(e==="min"){let n=r.match(/([0-9]+)px/);if(n)return n[1]}return r}var Ce=Re;export{Ce as a,xe as m};
