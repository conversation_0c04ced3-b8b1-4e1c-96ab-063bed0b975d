<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Support\Str;

class Sponsor extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'slug',
        'description',
        'logo',
        'website_url',
        'contact_email',
        'contact_phone',
        'contact_person',
        'address',
        'status',
        'social_links',
        'notes'
    ];

    protected $casts = [
        'social_links' => 'array'
    ];

    /**
     * Boot the model
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($sponsor) {
            if (empty($sponsor->slug)) {
                $sponsor->slug = Str::slug($sponsor->name);
            }
        });

        static::updating(function ($sponsor) {
            if ($sponsor->isDirty('name') && empty($sponsor->slug)) {
                $sponsor->slug = Str::slug($sponsor->name);
            }
        });
    }

    /**
     * Get the events this sponsor sponsors
     */
    public function events()
    {
        return $this->belongsToMany(Event::class, 'event_sponsors')
            ->withPivot(['sponsorship_tier_id', 'amount', 'start_date', 'end_date', 'status', 'custom_benefits', 'notes', 'display_order'])
            ->withTimestamps();
    }

    /**
     * Get the event sponsors pivot records
     */
    public function eventSponsors()
    {
        return $this->hasMany(EventSponsor::class);
    }

    /**
     * Get active sponsorships
     */
    public function activeSponsors()
    {
        return $this->eventSponsors()->where('status', 'active');
    }

    /**
     * Scope for active sponsors
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * Scope for search
     */
    public function scopeSearch($query, $search)
    {
        return $query->where(function ($q) use ($search) {
            $q->where('name', 'like', "%{$search}%")
              ->orWhere('description', 'like', "%{$search}%")
              ->orWhere('contact_person', 'like', "%{$search}%");
        });
    }

    /**
     * Get logo URL
     */
    public function getLogoUrlAttribute()
    {
        if ($this->logo) {
            return asset('storage/' . $this->logo);
        }

        // Return default sponsor logo
        return asset('images/default-sponsor-logo.svg');
    }

    /**
     * Get formatted website URL
     */
    public function getFormattedWebsiteUrlAttribute()
    {
        if (!$this->website_url) {
            return null;
        }

        if (!str_starts_with($this->website_url, 'http')) {
            return 'https://' . $this->website_url;
        }

        return $this->website_url;
    }

    /**
     * Get social media links
     */
    public function getSocialLinksAttribute($value)
    {
        $links = json_decode($value, true) ?: [];

        // Ensure we have default structure
        return array_merge([
            'facebook' => '',
            'twitter' => '',
            'instagram' => '',
            'linkedin' => '',
            'youtube' => ''
        ], $links);
    }

    /**
     * Get total sponsorship amount
     */
    public function getTotalSponsorshipAmount()
    {
        return $this->eventSponsors()
            ->where('status', 'active')
            ->sum('amount');
    }

    /**
     * Get active events count
     */
    public function getActiveEventsCount()
    {
        return $this->eventSponsors()
            ->where('status', 'active')
            ->whereHas('event', function ($query) {
                $query->where('status', 'published')
                      ->where('event_date', '>=', now());
            })
            ->count();
    }

    /**
     * Check if sponsor is sponsoring a specific event
     */
    public function isSponsoringEvent($eventId)
    {
        return $this->eventSponsors()
            ->where('event_id', $eventId)
            ->where('status', 'active')
            ->exists();
    }

    /**
     * Get sponsorship for specific event
     */
    public function getSponsorshipForEvent($eventId)
    {
        return $this->eventSponsors()
            ->where('event_id', $eventId)
            ->where('status', 'active')
            ->with('sponsorshipTier')
            ->first();
    }
}
