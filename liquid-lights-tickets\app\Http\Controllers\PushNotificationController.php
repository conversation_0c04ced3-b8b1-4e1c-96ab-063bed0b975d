<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use App\Services\PushNotificationService;
use App\Models\PushSubscription;

class PushNotificationController extends Controller
{
    protected $pushService;

    public function __construct(PushNotificationService $pushService)
    {
        $this->pushService = $pushService;
        $this->middleware('auth');
    }

    /**
     * Get VAPID public key for client-side subscription
     */
    public function getVapidPublicKey()
    {
        return response()->json([
            'success' => true,
            'public_key' => $this->pushService->getVapidPublicKey()
        ]);
    }

    /**
     * Subscribe user to push notifications
     */
    public function subscribe(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'endpoint' => 'required|url',
            'keys.p256dh' => 'required|string',
            'keys.auth' => 'required|string'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Invalid subscription data',
                'errors' => $validator->errors()
            ], 422);
        }

        $user = Auth::user();
        $result = $this->pushService->subscribe($user, $request->all());

        if ($result['success']) {
            return response()->json([
                'success' => true,
                'message' => 'Successfully subscribed to push notifications',
                'subscription_id' => $result['subscription_id']
            ]);
        }

        return response()->json([
            'success' => false,
            'message' => 'Failed to subscribe to push notifications',
            'error' => $result['error']
        ], 500);
    }

    /**
     * Unsubscribe user from push notifications
     */
    public function unsubscribe(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'endpoint' => 'nullable|url'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Invalid request data',
                'errors' => $validator->errors()
            ], 422);
        }

        $user = Auth::user();
        $result = $this->pushService->unsubscribe($user, $request->endpoint);

        return response()->json([
            'success' => $result['success'],
            'message' => $result['success']
                ? 'Successfully unsubscribed from push notifications'
                : 'Failed to unsubscribe from push notifications',
            'deleted_count' => $result['deleted_count'] ?? 0
        ]);
    }

    /**
     * Get user's push subscription status
     */
    public function getSubscriptionStatus()
    {
        $user = Auth::user();
        $subscriptions = $user->pushSubscriptions()
            ->where('is_active', true)
            ->get(['id', 'endpoint', 'subscribed_at']);

        return response()->json([
            'success' => true,
            'subscribed' => $subscriptions->isNotEmpty(),
            'subscription_count' => $subscriptions->count(),
            'subscriptions' => $subscriptions
        ]);
    }

    /**
     * Send test notification
     */
    public function sendTestNotification(Request $request)
    {
        $user = Auth::user();

        $payload = [
            'title' => 'Test Notification 🧪',
            'body' => 'This is a test notification from Liquid Lights Tickets!',
            'icon' => '/images/icons/icon-192x192.png',
            'badge' => '/images/icons/badge-72x72.png',
            'data' => [
                'type' => 'test',
                'timestamp' => now()->toISOString(),
                'url' => route('user.dashboard')
            ],
            'actions' => [
                [
                    'action' => 'open_dashboard',
                    'title' => 'Open Dashboard',
                    'icon' => '/images/icons/action-dashboard.png'
                ]
            ],
            'vibrate' => [100, 50, 100]
        ];

        $result = $this->pushService->sendToUser($user, $payload);

        return response()->json([
            'success' => $result['success'],
            'message' => $result['success']
                ? 'Test notification sent successfully'
                : 'Failed to send test notification',
            'total_sent' => $result['total_sent'] ?? 0,
            'total_failed' => $result['total_failed'] ?? 0
        ]);
    }

    /**
     * Update notification preferences
     */
    public function updatePreferences(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'booking_confirmations' => 'boolean',
            'event_reminders' => 'boolean',
            'payment_updates' => 'boolean',
            'event_updates' => 'boolean',
            'promotional_offers' => 'boolean'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Invalid preference data',
                'errors' => $validator->errors()
            ], 422);
        }

        $user = Auth::user();

        // Store preferences in user meta or separate table
        $preferences = $request->only([
            'booking_confirmations',
            'event_reminders',
            'payment_updates',
            'event_updates',
            'promotional_offers'
        ]);

        // For now, store in user's additional data field
        $user->update([
            'notification_preferences' => $preferences
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Notification preferences updated successfully',
            'preferences' => $preferences
        ]);
    }

    /**
     * Get notification preferences
     */
    public function getPreferences()
    {
        $user = Auth::user();

        $defaultPreferences = [
            'booking_confirmations' => true,
            'event_reminders' => true,
            'payment_updates' => true,
            'event_updates' => true,
            'promotional_offers' => false
        ];

        $preferences = $user->notification_preferences ?? $defaultPreferences;

        return response()->json([
            'success' => true,
            'preferences' => $preferences
        ]);
    }
}
