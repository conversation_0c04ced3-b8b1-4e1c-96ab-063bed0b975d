<?php $__env->startSection('title', 'My Bookings - Liquid Lights Tickets'); ?>

<?php $__env->startSection('content'); ?>
<div class="min-h-screen bg-gray-50 py-8">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Page Header -->
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-gray-900">My Bookings</h1>
            <p class="mt-2 text-gray-600">View and manage all your event bookings</p>
        </div>

        <!-- Filters -->
        <div class="bg-white rounded-lg shadow-sm p-6 mb-6">
            <form method="GET" action="<?php echo e(route('user.bookings')); ?>" class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div>
                    <label for="search" class="block text-sm font-medium text-gray-700 mb-1">Search</label>
                    <input type="text" 
                           name="search" 
                           id="search"
                           value="<?php echo e(request('search')); ?>"
                           placeholder="Search bookings..."
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500">
                </div>

                <div>
                    <label for="status" class="block text-sm font-medium text-gray-700 mb-1">Status</label>
                    <select name="status" 
                            id="status"
                            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500">
                        <option value="">All Status</option>
                        <option value="pending" <?php echo e(request('status') === 'pending' ? 'selected' : ''); ?>>Pending</option>
                        <option value="paid" <?php echo e(request('status') === 'paid' ? 'selected' : ''); ?>>Paid</option>
                        <option value="failed" <?php echo e(request('status') === 'failed' ? 'selected' : ''); ?>>Failed</option>
                        <option value="refunded" <?php echo e(request('status') === 'refunded' ? 'selected' : ''); ?>>Refunded</option>
                    </select>
                </div>

                <div>
                    <label for="sort" class="block text-sm font-medium text-gray-700 mb-1">Sort By</label>
                    <select name="sort" 
                            id="sort"
                            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500">
                        <option value="created_at" <?php echo e(request('sort') === 'created_at' ? 'selected' : ''); ?>>Booking Date</option>
                        <option value="event_date" <?php echo e(request('sort') === 'event_date' ? 'selected' : ''); ?>>Event Date</option>
                        <option value="total_amount" <?php echo e(request('sort') === 'total_amount' ? 'selected' : ''); ?>>Amount</option>
                    </select>
                </div>

                <div class="flex items-end">
                    <button type="submit" 
                            class="w-full bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md font-medium transition-colors">
                        Apply Filters
                    </button>
                </div>
            </form>
        </div>

        <!-- Bookings List -->
        <?php if($bookings->count() > 0): ?>
        <div class="space-y-6">
            <?php $__currentLoopData = $bookings; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $booking): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <div class="bg-white rounded-lg shadow-sm overflow-hidden">
                <div class="p-6">
                    <div class="flex items-start justify-between">
                        <div class="flex-1">
                            <div class="flex items-center space-x-4">
                                <div class="flex-shrink-0">
                                    <img src="<?php echo e($booking->event->banner_image ?: '/images/event-placeholder.jpg'); ?>" 
                                         alt="<?php echo e($booking->event->title); ?>" 
                                         class="w-16 h-16 rounded-lg object-cover">
                                </div>
                                <div class="flex-1">
                                    <h3 class="text-lg font-semibold text-gray-900"><?php echo e($booking->event->title); ?></h3>
                                    <p class="text-gray-600"><?php echo e($booking->ticketType->name); ?> × <?php echo e($booking->quantity); ?></p>
                                    <p class="text-sm text-gray-500 mt-1">
                                        Booking Reference: <span class="font-medium"><?php echo e($booking->booking_reference); ?></span>
                                    </p>
                                </div>
                            </div>
                        </div>
                        
                        <div class="text-right">
                            <p class="text-2xl font-bold text-gray-900">₹<?php echo e(number_format($booking->total_amount)); ?></p>
                            <span class="inline-flex px-3 py-1 text-sm font-semibold rounded-full mt-2
                                <?php echo e($booking->payment_status === 'paid' ? 'bg-green-100 text-green-800' : 
                                   ($booking->payment_status === 'pending' ? 'bg-yellow-100 text-yellow-800' : 
                                   ($booking->payment_status === 'refunded' ? 'bg-gray-100 text-gray-800' : 'bg-red-100 text-red-800'))); ?>">
                                <?php echo e(ucfirst($booking->payment_status)); ?>

                            </span>
                        </div>
                    </div>
                    
                    <!-- Event Details -->
                    <div class="mt-4 grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-gray-600">
                        <div class="flex items-center">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                            </svg>
                            <?php echo e(\Carbon\Carbon::parse($booking->event->event_date)->format('M d, Y')); ?>

                        </div>
                        
                        <div class="flex items-center">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            <?php echo e(\Carbon\Carbon::parse($booking->event->event_time)->format('g:i A')); ?>

                        </div>
                        
                        <div class="flex items-center">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                            </svg>
                            <?php echo e($booking->event->venue_name); ?>

                        </div>
                    </div>
                    
                    <!-- Additional Info -->
                    <div class="mt-4 flex items-center justify-between">
                        <div class="flex items-center space-x-4 text-sm text-gray-500">
                            <span>Booked on <?php echo e($booking->created_at->format('M d, Y')); ?></span>
                            <?php if($booking->is_checked_in): ?>
                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">
                                Checked In
                            </span>
                            <?php endif; ?>
                        </div>
                        
                        <div class="flex space-x-3">
                            <a href="<?php echo e(route('user.booking', $booking->id)); ?>" 
                               class="text-blue-600 hover:text-blue-700 font-medium text-sm">
                                View Details
                            </a>
                            
                            <?php if($booking->payment_status === 'paid' && !$booking->is_checked_in && $booking->event->event_date > now()): ?>
                            <button onclick="downloadTicket(<?php echo e($booking->id); ?>)" 
                                    class="text-green-600 hover:text-green-700 font-medium text-sm">
                                Download Ticket
                            </button>
                            <?php endif; ?>
                            
                            <?php if($booking->payment_status === 'paid' && $booking->event->event_date > now()->addHours(24)): ?>
                            <button onclick="cancelBooking(<?php echo e($booking->id); ?>)" 
                                    class="text-red-600 hover:text-red-700 font-medium text-sm">
                                Cancel
                            </button>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>

        <!-- Pagination -->
        <div class="mt-8">
            <?php echo e($bookings->appends(request()->query())->links()); ?>

        </div>
        <?php else: ?>
        <!-- Empty State -->
        <div class="text-center py-16">
            <svg class="mx-auto h-24 w-24 text-gray-400 mb-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
            </svg>
            <h3 class="text-2xl font-bold text-gray-900 mb-2">No Bookings Found</h3>
            <p class="text-gray-600 mb-8 max-w-md mx-auto">
                <?php if(request()->hasAny(['search', 'status', 'sort'])): ?>
                    No bookings match your current filters. Try adjusting your search criteria.
                <?php else: ?>
                    You haven't made any bookings yet. Start by browsing our amazing events.
                <?php endif; ?>
            </p>
            <div class="flex flex-col sm:flex-row gap-4 justify-center">
                <?php if(request()->hasAny(['search', 'status', 'sort'])): ?>
                <a href="<?php echo e(route('user.bookings')); ?>" 
                   class="inline-flex items-center px-6 py-3 border border-gray-300 text-base font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                    Clear Filters
                </a>
                <?php endif; ?>
                <a href="<?php echo e(route('public.events')); ?>" 
                   class="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700">
                    Browse Events
                    <svg class="ml-2 -mr-1 w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
                    </svg>
                </a>
            </div>
        </div>
        <?php endif; ?>
    </div>
</div>

<?php $__env->startPush('scripts'); ?>
<script>
async function downloadTicket(bookingId) {
    try {
        const response = await fetch(`/user/bookings/${bookingId}/download`, {
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        });
        
        const data = await response.json();
        
        if (data.success) {
            alert(data.message);
        } else {
            alert('Failed to download ticket. Please try again.');
        }
    } catch (error) {
        console.error('Download error:', error);
        alert('An error occurred. Please try again.');
    }
}

async function cancelBooking(bookingId) {
    if (!confirm('Are you sure you want to cancel this booking? This action cannot be undone.')) {
        return;
    }
    
    try {
        const response = await fetch(`/api/booking/${bookingId}/cancel`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        });
        
        const data = await response.json();
        
        if (data.success) {
            alert(data.message);
            window.location.reload();
        } else {
            alert(data.message || 'Failed to cancel booking. Please try again.');
        }
    } catch (error) {
        console.error('Cancel error:', error);
        alert('An error occurred. Please try again.');
    }
}
</script>
<?php $__env->stopPush(); ?>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('public.layout', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Documents\augment-projects\LLe\liquid-lights-tickets\resources\views/user/bookings.blade.php ENDPATH**/ ?>