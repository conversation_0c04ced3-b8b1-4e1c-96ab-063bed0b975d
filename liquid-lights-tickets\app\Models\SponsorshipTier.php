<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class SponsorshipTier extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'slug',
        'description',
        'price',
        'benefits',
        'color',
        'max_sponsors_per_event',
        'display_order',
        'is_active'
    ];

    protected $casts = [
        'benefits' => 'array',
        'price' => 'decimal:2',
        'is_active' => 'boolean',
        'max_sponsors_per_event' => 'integer',
        'display_order' => 'integer'
    ];

    /**
     * Get the event sponsors for this tier
     */
    public function eventSponsors()
    {
        return $this->hasMany(EventSponsor::class);
    }

    /**
     * Get sponsors through event sponsors
     */
    public function sponsors()
    {
        return $this->hasManyThrough(Sponsor::class, EventSponsor::class);
    }

    /**
     * Scope for active tiers
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope for ordered display
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('display_order')->orderBy('name');
    }

    /**
     * Get formatted price
     */
    public function getFormattedPriceAttribute()
    {
        return $this->price ? '₹' . number_format($this->price) : 'Contact for pricing';
    }

    /**
     * Get benefits as formatted list
     */
    public function getBenefitsListAttribute()
    {
        return $this->benefits ? implode(', ', $this->benefits) : '';
    }

    /**
     * Check if tier has available slots for an event
     */
    public function hasAvailableSlots($eventId)
    {
        $currentSponsors = EventSponsor::where('event_id', $eventId)
            ->where('sponsorship_tier_id', $this->id)
            ->where('status', 'active')
            ->count();

        return $currentSponsors < $this->max_sponsors_per_event;
    }

    /**
     * Get available slots for an event
     */
    public function getAvailableSlots($eventId)
    {
        $currentSponsors = EventSponsor::where('event_id', $eventId)
            ->where('sponsorship_tier_id', $this->id)
            ->where('status', 'active')
            ->count();

        return max(0, $this->max_sponsors_per_event - $currentSponsors);
    }
}
