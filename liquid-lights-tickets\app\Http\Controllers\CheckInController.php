<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use App\Services\TicketService;
use App\Models\Booking;
use App\Models\Event;
use Exception;

class CheckInController extends Controller
{
    protected $ticketService;

    public function __construct(TicketService $ticketService)
    {
        $this->ticketService = $ticketService;
    }

    /**
     * Show check-in scanner page
     */
    public function scanner($eventId = null)
    {
        $events = Event::where('status', 'published')
            ->where('event_date', '>=', now()->subHours(6))
            ->orderBy('event_date')
            ->get();

        $selectedEvent = null;
        if ($eventId) {
            $selectedEvent = Event::findOrFail($eventId);
        }

        return view('admin.checkin.scanner', compact('events', 'selectedEvent'));
    }

    /**
     * Validate QR code
     */
    public function validateQR(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'qr_data' => 'required|string',
            'event_id' => 'nullable|exists:events,id'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Invalid request data',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $validation = $this->ticketService->validateQRCode($request->qr_data);

            if (!$validation['valid']) {
                return response()->json([
                    'success' => false,
                    'message' => $validation['message']
                ]);
            }

            $booking = $validation['booking'];

            // Additional event validation if event_id is provided
            if ($request->event_id && $booking->event_id != $request->event_id) {
                return response()->json([
                    'success' => false,
                    'message' => 'Ticket is not valid for this event'
                ]);
            }

            return response()->json([
                'success' => true,
                'valid' => true,
                'booking' => [
                    'id' => $booking->id,
                    'booking_reference' => $booking->booking_reference,
                    'quantity' => $booking->quantity,
                    'is_checked_in' => $booking->is_checked_in,
                    'checked_in_at' => $booking->checked_in_at,
                    'event' => [
                        'id' => $booking->event->id,
                        'title' => $booking->event->title,
                        'event_date' => $booking->event->event_date,
                        'event_time' => $booking->event->event_time,
                        'venue_name' => $booking->event->venue_name
                    ],
                    'ticket_type' => [
                        'name' => $booking->ticketType->name,
                        'price' => $booking->ticketType->price
                    ],
                    'user' => [
                        'name' => $booking->user->name,
                        'email' => $booking->user->email,
                        'phone' => $booking->user->phone
                    ]
                ]
            ]);

        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'QR code validation failed: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Check in booking
     */
    public function checkIn(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'qr_data' => 'required|string',
            'event_id' => 'nullable|exists:events,id'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Invalid request data',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $result = $this->ticketService->checkInBooking($request->qr_data, Auth::id());

            if (!$result['success']) {
                return response()->json([
                    'success' => false,
                    'message' => $result['message']
                ]);
            }

            $booking = $result['booking'];

            // Additional event validation if event_id is provided
            if ($request->event_id && $booking->event_id != $request->event_id) {
                return response()->json([
                    'success' => false,
                    'message' => 'Ticket is not valid for this event'
                ]);
            }

            return response()->json([
                'success' => true,
                'message' => 'Successfully checked in',
                'booking' => [
                    'id' => $booking->id,
                    'booking_reference' => $booking->booking_reference,
                    'quantity' => $booking->quantity,
                    'checked_in_at' => $booking->checked_in_at,
                    'event' => [
                        'title' => $booking->event->title,
                        'venue_name' => $booking->event->venue_name
                    ],
                    'user' => [
                        'name' => $booking->user->name,
                        'email' => $booking->user->email
                    ]
                ]
            ]);

        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Check-in failed: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get check-in statistics for event
     */
    public function getEventStats($eventId)
    {
        try {
            $event = Event::findOrFail($eventId);

            $stats = [
                'total_bookings' => $event->bookings()->where('payment_status', 'paid')->count(),
                'total_tickets' => $event->bookings()->where('payment_status', 'paid')->sum('quantity'),
                'checked_in_bookings' => $event->bookings()->where('is_checked_in', true)->count(),
                'checked_in_tickets' => $event->bookings()->where('is_checked_in', true)->sum('quantity'),
                'pending_checkin' => $event->bookings()
                    ->where('payment_status', 'paid')
                    ->where('is_checked_in', false)
                    ->count()
            ];

            $stats['checkin_percentage'] = $stats['total_tickets'] > 0
                ? round(($stats['checked_in_tickets'] / $stats['total_tickets']) * 100, 1)
                : 0;

            return response()->json([
                'success' => true,
                'event' => [
                    'id' => $event->id,
                    'title' => $event->title,
                    'event_date' => $event->event_date,
                    'venue_name' => $event->venue_name
                ],
                'stats' => $stats
            ]);

        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to get event stats: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get recent check-ins for event
     */
    public function getRecentCheckIns($eventId)
    {
        try {
            $recentCheckIns = Booking::where('event_id', $eventId)
                ->where('is_checked_in', true)
                ->with(['user', 'ticketType'])
                ->orderBy('checked_in_at', 'desc')
                ->limit(20)
                ->get();

            return response()->json([
                'success' => true,
                'checkins' => $recentCheckIns->map(function($booking) {
                    return [
                        'id' => $booking->id,
                        'booking_reference' => $booking->booking_reference,
                        'quantity' => $booking->quantity,
                        'checked_in_at' => $booking->checked_in_at,
                        'user' => [
                            'name' => $booking->user->name,
                            'email' => $booking->user->email
                        ],
                        'ticket_type' => [
                            'name' => $booking->ticketType->name
                        ]
                    ];
                })
            ]);

        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to get recent check-ins: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Manual check-in by booking reference
     */
    public function manualCheckIn(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'booking_reference' => 'required|string',
            'event_id' => 'nullable|exists:events,id'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Invalid request data',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $booking = Booking::where('booking_reference', $request->booking_reference)
                ->with(['event', 'user', 'ticketType'])
                ->first();

            if (!$booking) {
                return response()->json([
                    'success' => false,
                    'message' => 'Booking not found'
                ]);
            }

            // Validate booking
            if ($booking->payment_status !== 'paid') {
                return response()->json([
                    'success' => false,
                    'message' => 'Booking is not paid'
                ]);
            }

            if ($booking->is_checked_in) {
                return response()->json([
                    'success' => false,
                    'message' => 'Already checked in'
                ]);
            }

            // Additional event validation if event_id is provided
            if ($request->event_id && $booking->event_id != $request->event_id) {
                return response()->json([
                    'success' => false,
                    'message' => 'Booking is not for this event'
                ]);
            }

            // Check in the booking
            $booking->update([
                'is_checked_in' => true,
                'checked_in_at' => now(),
                'checked_in_by' => Auth::id()
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Successfully checked in manually',
                'booking' => [
                    'id' => $booking->id,
                    'booking_reference' => $booking->booking_reference,
                    'quantity' => $booking->quantity,
                    'checked_in_at' => $booking->checked_in_at,
                    'user' => [
                        'name' => $booking->user->name,
                        'email' => $booking->user->email
                    ]
                ]
            ]);

        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Manual check-in failed: ' . $e->getMessage()
            ], 500);
        }
    }
}
