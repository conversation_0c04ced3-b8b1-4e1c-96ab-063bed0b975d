// Push Notifications Manager for Liquid Lights Tickets
class PushNotificationManager {
    constructor() {
        this.vapidPublicKey = null;
        this.isSupported = 'serviceWorker' in navigator && 'PushManager' in window;
        this.isSubscribed = false;
        this.subscription = null;
        this.swRegistration = null;
        
        this.init();
    }

    async init() {
        if (!this.isSupported) {
            console.log('Push notifications are not supported');
            return;
        }

        try {
            // Wait for service worker to be ready
            this.swRegistration = await navigator.serviceWorker.ready;
            
            // Get VAPID public key
            await this.getVapidPublicKey();
            
            // Check current subscription status
            await this.checkSubscriptionStatus();
            
            // Update UI
            this.updateUI();
            
            console.log('Push notification manager initialized');
        } catch (error) {
            console.error('Failed to initialize push notifications:', error);
        }
    }

    async getVapidPublicKey() {
        try {
            const response = await fetch('/api/notifications/vapid-key');
            const data = await response.json();
            
            if (data.success) {
                this.vapidPublicKey = data.public_key;
            } else {
                throw new Error('Failed to get VAPID public key');
            }
        } catch (error) {
            console.error('Error getting VAPID key:', error);
            throw error;
        }
    }

    async checkSubscriptionStatus() {
        try {
            this.subscription = await this.swRegistration.pushManager.getSubscription();
            this.isSubscribed = this.subscription !== null;
            
            if (this.isSubscribed) {
                console.log('User is subscribed to push notifications');
            } else {
                console.log('User is not subscribed to push notifications');
            }
        } catch (error) {
            console.error('Error checking subscription status:', error);
        }
    }

    async subscribe() {
        if (!this.isSupported) {
            throw new Error('Push notifications are not supported');
        }

        if (!this.vapidPublicKey) {
            throw new Error('VAPID public key not available');
        }

        try {
            // Request notification permission
            const permission = await Notification.requestPermission();
            
            if (permission !== 'granted') {
                throw new Error('Notification permission denied');
            }

            // Subscribe to push notifications
            const subscription = await this.swRegistration.pushManager.subscribe({
                userVisibleOnly: true,
                applicationServerKey: this.urlBase64ToUint8Array(this.vapidPublicKey)
            });

            // Send subscription to server
            const response = await fetch('/api/notifications/subscribe', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: JSON.stringify(subscription.toJSON())
            });

            const data = await response.json();

            if (data.success) {
                this.subscription = subscription;
                this.isSubscribed = true;
                this.updateUI();
                this.showNotification('✅ Push notifications enabled!', 'You will now receive important updates and reminders.');
                return true;
            } else {
                throw new Error(data.message || 'Failed to subscribe');
            }

        } catch (error) {
            console.error('Error subscribing to push notifications:', error);
            this.showNotification('❌ Failed to enable notifications', error.message);
            throw error;
        }
    }

    async unsubscribe() {
        if (!this.subscription) {
            return true;
        }

        try {
            // Unsubscribe from push manager
            const successful = await this.subscription.unsubscribe();

            if (successful) {
                // Notify server
                await fetch('/api/notifications/unsubscribe', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    },
                    body: JSON.stringify({
                        endpoint: this.subscription.endpoint
                    })
                });

                this.subscription = null;
                this.isSubscribed = false;
                this.updateUI();
                this.showNotification('🔕 Push notifications disabled', 'You will no longer receive push notifications.');
                return true;
            }

            return false;

        } catch (error) {
            console.error('Error unsubscribing from push notifications:', error);
            throw error;
        }
    }

    async sendTestNotification() {
        try {
            const response = await fetch('/api/notifications/test', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                }
            });

            const data = await response.json();

            if (data.success) {
                this.showNotification('🧪 Test notification sent!', 'Check if you received the push notification.');
            } else {
                throw new Error(data.message || 'Failed to send test notification');
            }

        } catch (error) {
            console.error('Error sending test notification:', error);
            this.showNotification('❌ Test failed', error.message);
        }
    }

    updateUI() {
        // Update notification toggle button
        const toggleButton = document.getElementById('notification-toggle');
        if (toggleButton) {
            toggleButton.textContent = this.isSubscribed ? 'Disable Notifications' : 'Enable Notifications';
            toggleButton.className = this.isSubscribed 
                ? 'bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg font-medium transition-colors'
                : 'bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg font-medium transition-colors';
        }

        // Update status indicator
        const statusIndicator = document.getElementById('notification-status');
        if (statusIndicator) {
            statusIndicator.innerHTML = this.isSubscribed
                ? '<span class="text-green-600">✅ Enabled</span>'
                : '<span class="text-gray-500">🔕 Disabled</span>';
        }

        // Update test button
        const testButton = document.getElementById('test-notification');
        if (testButton) {
            testButton.disabled = !this.isSubscribed;
            testButton.className = this.isSubscribed
                ? 'bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors'
                : 'bg-gray-400 text-gray-700 px-4 py-2 rounded-lg font-medium cursor-not-allowed';
        }

        // Show/hide notification settings
        const notificationSettings = document.getElementById('notification-settings');
        if (notificationSettings) {
            notificationSettings.style.display = this.isSubscribed ? 'block' : 'none';
        }
    }

    showNotification(title, message, type = 'info') {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `fixed top-4 right-4 max-w-sm bg-white border border-gray-200 rounded-lg shadow-lg z-50 transform transition-all duration-300 translate-x-full`;
        
        const bgColor = {
            'success': 'bg-green-50 border-green-200',
            'error': 'bg-red-50 border-red-200',
            'warning': 'bg-yellow-50 border-yellow-200',
            'info': 'bg-blue-50 border-blue-200'
        }[type] || 'bg-gray-50 border-gray-200';

        notification.className = notification.className.replace('bg-white border-gray-200', bgColor);

        notification.innerHTML = `
            <div class="p-4">
                <div class="flex items-start">
                    <div class="flex-1">
                        <h3 class="text-sm font-medium text-gray-900">${title}</h3>
                        <p class="mt-1 text-sm text-gray-600">${message}</p>
                    </div>
                    <button onclick="this.closest('.fixed').remove()" class="ml-4 text-gray-400 hover:text-gray-600">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
            </div>
        `;

        document.body.appendChild(notification);

        // Animate in
        setTimeout(() => {
            notification.classList.remove('translate-x-full');
        }, 100);

        // Auto-remove after 5 seconds
        setTimeout(() => {
            notification.classList.add('translate-x-full');
            setTimeout(() => {
                if (notification.parentElement) {
                    notification.remove();
                }
            }, 300);
        }, 5000);
    }

    urlBase64ToUint8Array(base64String) {
        const padding = '='.repeat((4 - base64String.length % 4) % 4);
        const base64 = (base64String + padding)
            .replace(/-/g, '+')
            .replace(/_/g, '/');

        const rawData = window.atob(base64);
        const outputArray = new Uint8Array(rawData.length);

        for (let i = 0; i < rawData.length; ++i) {
            outputArray[i] = rawData.charCodeAt(i);
        }
        return outputArray;
    }

    // Public methods for external use
    async toggle() {
        if (this.isSubscribed) {
            return await this.unsubscribe();
        } else {
            return await this.subscribe();
        }
    }

    getStatus() {
        return {
            isSupported: this.isSupported,
            isSubscribed: this.isSubscribed,
            permission: Notification.permission
        };
    }
}

// Initialize push notification manager
let pushManager;

document.addEventListener('DOMContentLoaded', () => {
    pushManager = new PushNotificationManager();
    
    // Bind event listeners
    const toggleButton = document.getElementById('notification-toggle');
    if (toggleButton) {
        toggleButton.addEventListener('click', async () => {
            toggleButton.disabled = true;
            toggleButton.textContent = 'Processing...';
            
            try {
                await pushManager.toggle();
            } catch (error) {
                console.error('Toggle failed:', error);
            } finally {
                toggleButton.disabled = false;
            }
        });
    }

    const testButton = document.getElementById('test-notification');
    if (testButton) {
        testButton.addEventListener('click', async () => {
            testButton.disabled = true;
            testButton.textContent = 'Sending...';
            
            try {
                await pushManager.sendTestNotification();
            } catch (error) {
                console.error('Test failed:', error);
            } finally {
                testButton.disabled = false;
                testButton.textContent = 'Send Test';
            }
        });
    }
});

// Export for global access
window.pushManager = pushManager;
