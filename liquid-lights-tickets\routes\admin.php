<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Admin\AdminAuthController;
use App\Http\Controllers\Admin\DashboardController;
use App\Http\Controllers\Admin\EventController;
use App\Http\Middleware\RoleMiddleware;

/*
|--------------------------------------------------------------------------
| Admin Routes
|--------------------------------------------------------------------------
|
| These routes are for admin panel functionality and require admin
| authentication and appropriate role permissions.
|
*/

// Admin Authentication (no middleware required)
Route::prefix('auth')->group(function () {
    Route::post('/login', [AdminAuthController::class, 'login']);
});

// Protected Admin Routes
Route::middleware(['auth:sanctum'])->group(function () {
    
    // Admin Profile Management
    Route::prefix('auth')->group(function () {
        Route::get('/profile', [AdminAuthController::class, 'profile']);
        Route::post('/change-password', [AdminAuthController::class, 'changePassword']);
        Route::post('/logout', [AdminAuthController::class, 'logout']);
        Route::post('/logout-all', [AdminAuthController::class, 'logoutAll']);
    });

    // Super Admin Only Routes
    Route::middleware([RoleMiddleware::class . ':super-admin'])->group(function () {
        // User management routes will go here
        // Role and permission management routes will go here
    });

    // Admin and Manager Routes
    Route::middleware([RoleMiddleware::class . ':admin,manager'])->group(function () {
        // Event management routes will go here
        // Booking management routes will go here
    });

    // Scanner Routes
    Route::middleware([RoleMiddleware::class . ':scanner'])->group(function () {
        // Ticket scanning routes will go here
    });

    // Booker Routes
    Route::middleware([RoleMiddleware::class . ':booker'])->group(function () {
        // Manual booking routes will go here
    });

    // All Admin Roles Routes
    Route::middleware([RoleMiddleware::class . ':admin,manager,scanner,booker'])->group(function () {
        // Dashboard routes
        Route::get('/dashboard', [DashboardController::class, 'index']);
        Route::get('/dashboard/quick-stats', [DashboardController::class, 'quickStats']);
    });

    // Admin and Manager Routes (Event Management)
    Route::middleware([RoleMiddleware::class . ':admin,manager'])->group(function () {
        // Event management
        Route::apiResource('events', EventController::class);
        Route::post('events/{id}/publish', [EventController::class, 'publish']);
        Route::post('events/{id}/cancel', [EventController::class, 'cancel']);
    });
});
