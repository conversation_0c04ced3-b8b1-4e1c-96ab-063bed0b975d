<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Admin\AdminAuthController;
use App\Http\Controllers\Admin\DashboardController;
use App\Http\Controllers\Admin\EventController;
use App\Http\Controllers\Admin\ArtistController;
use App\Http\Controllers\Admin\TicketTypeController;
use App\Http\Controllers\Admin\BookingController;
use App\Http\Controllers\Admin\UserController;
use App\Http\Controllers\Admin\AnalyticsController;
use App\Http\Middleware\RoleMiddleware;

/*
|--------------------------------------------------------------------------
| Admin Routes
|--------------------------------------------------------------------------
|
| These routes are for admin panel functionality and require admin
| authentication and appropriate role permissions.
|
*/

// Admin Authentication (no middleware required)
Route::prefix('auth')->group(function () {
    Route::post('/login', [AdminAuthController::class, 'login']);
});

// Protected Admin Routes
Route::middleware(['auth:sanctum'])->group(function () {
    
    // Admin Profile Management
    Route::prefix('auth')->group(function () {
        Route::get('/profile', [AdminAuthController::class, 'profile']);
        Route::post('/change-password', [AdminAuthController::class, 'changePassword']);
        Route::post('/logout', [AdminAuthController::class, 'logout']);
        Route::post('/logout-all', [AdminAuthController::class, 'logoutAll']);
    });

    // Super Admin Only Routes
    Route::middleware([RoleMiddleware::class . ':super-admin'])->group(function () {
        // User management routes will go here
        // Role and permission management routes will go here
    });

    // Admin and Manager Routes
    Route::middleware([RoleMiddleware::class . ':admin,manager'])->group(function () {
        // Event management routes will go here
        // Booking management routes will go here
    });

    // Scanner Routes
    Route::middleware([RoleMiddleware::class . ':scanner'])->group(function () {
        // Ticket scanning routes will go here
    });

    // Booker Routes
    Route::middleware([RoleMiddleware::class . ':booker'])->group(function () {
        // Manual booking routes will go here
    });

    // All Admin Roles Routes
    Route::middleware([RoleMiddleware::class . ':admin,manager,scanner,booker'])->group(function () {
        // Dashboard routes
        Route::get('/dashboard', [DashboardController::class, 'index']);
        Route::get('/dashboard/quick-stats', [DashboardController::class, 'quickStats']);
    });

    // Admin and Manager Routes (Event Management)
    Route::middleware([RoleMiddleware::class . ':admin,manager'])->group(function () {
        // Event management
        Route::apiResource('events', EventController::class);
        Route::post('events/{id}/publish', [EventController::class, 'publish']);
        Route::post('events/{id}/cancel', [EventController::class, 'cancel']);

        // Artist management
        Route::apiResource('artists', ArtistController::class);
        Route::get('artists-active', [ArtistController::class, 'active']);

        // Ticket type management
        Route::apiResource('ticket-types', TicketTypeController::class);
        Route::post('ticket-types/{id}/toggle-status', [TicketTypeController::class, 'toggleStatus']);
        Route::get('events/{eventId}/ticket-types', [TicketTypeController::class, 'index']);

        // Booking management
        Route::apiResource('bookings', BookingController::class);
        Route::post('bookings/{id}/refund', [BookingController::class, 'refund']);
        Route::post('bookings/{id}/check-in', [BookingController::class, 'checkIn']);
        Route::get('bookings-stats', [BookingController::class, 'stats']);
        Route::get('bookings-export', [BookingController::class, 'export']);

        // User management
        Route::apiResource('users', UserController::class);
        Route::post('users/{id}/blacklist', [UserController::class, 'blacklist']);
        Route::post('users/{id}/unblacklist', [UserController::class, 'unblacklist']);
        Route::post('users/{id}/toggle-status', [UserController::class, 'toggleStatus']);
        Route::get('users/{id}/bookings', [UserController::class, 'bookings']);
        Route::get('users-export', [UserController::class, 'export']);
        Route::get('roles', [UserController::class, 'roles']);

        // Analytics
        Route::get('analytics/dashboard', [AnalyticsController::class, 'dashboard']);
        Route::get('analytics/realtime', [AnalyticsController::class, 'realtime']);
        Route::get('analytics/export', [AnalyticsController::class, 'export']);
    });
});
