<?php

namespace App\Services;

use App\Models\Payment;
use Stripe\Stripe;
use Stripe\PaymentIntent;
use Stripe\Refund;
use Stripe\Webhook;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Log;
use Exception;

class StripeService
{
    protected $secretKey;
    protected $publishableKey;

    public function __construct()
    {
        $this->secretKey = config('services.stripe.secret');
        $this->publishableKey = config('services.stripe.key');
        Stripe::setApiKey($this->secretKey);
    }

    /**
     * Create Stripe payment intent
     */
    public function createPaymentIntent(float $amount, array $bookings, array $customerInfo)
    {
        try {
            $paymentIntent = PaymentIntent::create([
                'amount' => $amount * 100, // Convert to cents
                'currency' => 'inr',
                'metadata' => [
                    'booking_count' => count($bookings),
                    'customer_email' => $customerInfo['email'],
                    'customer_phone' => $customerInfo['phone'] ?? ''
                ],
                'receipt_email' => $customerInfo['email'],
                'description' => 'Event Ticket Booking - ' . config('app.name')
            ]);

            // Create payment record
            $payment = Payment::create([
                'payment_id' => $paymentIntent->id,
                'payment_method' => 'stripe',
                'amount' => $amount,
                'currency' => 'INR',
                'status' => 'pending',
                'booking_ids' => json_encode(collect($bookings)->pluck('id')->toArray()),
                'customer_info' => json_encode($customerInfo),
                'gateway_order_id' => $paymentIntent->id,
                'created_at' => now()
            ]);

            return [
                'success' => true,
                'client_secret' => $paymentIntent->client_secret,
                'payment_intent_id' => $paymentIntent->id,
                'amount' => $amount,
                'currency' => 'inr',
                'publishable_key' => $this->publishableKey,
                'payment_id' => $payment->id
            ];

        } catch (Exception $e) {
            Log::error('Stripe payment intent creation failed', [
                'error' => $e->getMessage(),
                'amount' => $amount
            ]);

            throw new Exception('Failed to create payment intent: ' . $e->getMessage());
        }
    }

    /**
     * Verify Stripe payment
     */
    public function verifyPayment(array $paymentData)
    {
        try {
            $paymentIntent = PaymentIntent::retrieve($paymentData['payment_intent_id']);

            if ($paymentIntent->status === 'succeeded') {
                return [
                    'verified' => true,
                    'payment_details' => $paymentIntent->toArray()
                ];
            }

            return ['verified' => false];

        } catch (Exception $e) {
            Log::error('Stripe payment verification failed', [
                'error' => $e->getMessage(),
                'payment_data' => $paymentData
            ]);

            return ['verified' => false];
        }
    }

    /**
     * Process refund
     */
    public function processRefund(string $paymentId, float $amount)
    {
        try {
            $refund = Refund::create([
                'payment_intent' => $paymentId,
                'amount' => $amount * 100, // Convert to cents
                'metadata' => [
                    'reason' => 'Customer requested refund',
                    'processed_at' => now()->toISOString()
                ]
            ]);

            return [
                'success' => true,
                'refund_id' => $refund->id,
                'amount' => $amount,
                'status' => $refund->status
            ];

        } catch (Exception $e) {
            Log::error('Stripe refund failed', [
                'error' => $e->getMessage(),
                'payment_id' => $paymentId,
                'amount' => $amount
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Handle webhook
     */
    public function handleWebhook(string $payload, string $signature)
    {
        try {
            $webhookSecret = config('services.stripe.webhook_secret');
            $event = Webhook::constructEvent($payload, $signature, $webhookSecret);

            switch ($event->type) {
                case 'payment_intent.succeeded':
                    return $this->handlePaymentSucceeded($event->data->object);
                    
                case 'payment_intent.payment_failed':
                    return $this->handlePaymentFailed($event->data->object);
                    
                case 'charge.dispute.created':
                    return $this->handleChargeDispute($event->data->object);
                    
                default:
                    Log::info('Unhandled Stripe webhook event', ['event' => $event->type]);
                    return ['success' => true];
            }

        } catch (Exception $e) {
            Log::error('Stripe webhook handling failed', [
                'error' => $e->getMessage(),
                'payload' => $payload
            ]);

            throw $e;
        }
    }

    /**
     * Handle payment succeeded webhook
     */
    protected function handlePaymentSucceeded($paymentIntent)
    {
        $payment = Payment::where('gateway_order_id', $paymentIntent->id)->first();
        
        if ($payment && $payment->status === 'pending') {
            $payment->update([
                'status' => 'completed',
                'gateway_payment_id' => $paymentIntent->id,
                'gateway_response' => $paymentIntent->toArray(),
                'completed_at' => now()
            ]);

            // Update bookings
            $bookingIds = json_decode($payment->booking_ids, true);
            \App\Models\Booking::whereIn('id', $bookingIds)->update([
                'payment_status' => 'paid',
                'payment_id' => $paymentIntent->id
            ]);
        }

        return ['success' => true];
    }

    /**
     * Handle payment failed webhook
     */
    protected function handlePaymentFailed($paymentIntent)
    {
        $payment = Payment::where('gateway_order_id', $paymentIntent->id)->first();
        
        if ($payment && $payment->status === 'pending') {
            $payment->update([
                'status' => 'failed',
                'failure_reason' => $paymentIntent->last_payment_error->message ?? 'Payment failed',
                'gateway_response' => $paymentIntent->toArray(),
                'failed_at' => now()
            ]);

            // Update bookings
            $bookingIds = json_decode($payment->booking_ids, true);
            \App\Models\Booking::whereIn('id', $bookingIds)->update([
                'payment_status' => 'failed'
            ]);
        }

        return ['success' => true];
    }

    /**
     * Handle charge dispute
     */
    protected function handleChargeDispute($dispute)
    {
        // Log dispute for manual review
        Log::warning('Stripe charge dispute created', [
            'dispute_id' => $dispute->id,
            'charge_id' => $dispute->charge,
            'amount' => $dispute->amount,
            'reason' => $dispute->reason
        ]);

        return ['success' => true];
    }

    /**
     * Get payment details
     */
    public function getPaymentDetails(string $paymentIntentId)
    {
        try {
            $paymentIntent = PaymentIntent::retrieve($paymentIntentId);
            return $paymentIntent->toArray();
        } catch (Exception $e) {
            Log::error('Failed to fetch Stripe payment details', [
                'error' => $e->getMessage(),
                'payment_intent_id' => $paymentIntentId
            ]);
            
            throw $e;
        }
    }

    /**
     * Create customer
     */
    public function createCustomer(array $customerInfo)
    {
        try {
            $customer = \Stripe\Customer::create([
                'email' => $customerInfo['email'],
                'name' => $customerInfo['name'],
                'phone' => $customerInfo['phone'] ?? null,
                'metadata' => [
                    'source' => 'liquid_lights_tickets'
                ]
            ]);

            return [
                'success' => true,
                'customer_id' => $customer->id
            ];

        } catch (Exception $e) {
            Log::error('Stripe customer creation failed', [
                'error' => $e->getMessage(),
                'customer_info' => $customerInfo
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
}
