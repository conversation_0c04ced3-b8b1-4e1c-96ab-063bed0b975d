@echo off
echo 🌟 Starting Liquid Lights Tickets Server 🌟
echo ============================================

REM Kill any existing PHP processes on port 8000
echo 🔄 Stopping existing servers...
for /f "tokens=5" %%a in ('netstat -aon ^| find ":8000" ^| find "LISTENING"') do taskkill /f /pid %%a 2>nul
timeout /t 2 /nobreak >nul

REM Clear Laravel caches
echo 🧹 Clearing caches...
php -c php-server.ini artisan config:clear
php -c php-server.ini artisan route:clear
php -c php-server.ini artisan view:clear

REM Test database connection
echo 💾 Testing database connection...
php -c php-server.ini -r "require 'vendor/autoload.php'; $app = require 'bootstrap/app.php'; $app->make('Illuminate\Contracts\Console\Kernel')->bootstrap(); try { DB::connection()->getPdo(); echo 'Database connected successfully!' . PHP_EOL; } catch (Exception $e) { echo 'Database error: ' . $e->getMessage() . PHP_EOL; }"

REM Start the server with custom PHP configuration
echo 🚀 Starting Liquid Lights server...
echo 📍 URL: http://127.0.0.1:8000
echo ⚡ Admin: http://127.0.0.1:8000/admin/login
echo 🔐 OTPless: http://127.0.0.1:8000/otpless-login
echo.
echo Press Ctrl+C to stop the server
echo ================================

php -c php-server.ini artisan serve --host=127.0.0.1 --port=8000

pause
