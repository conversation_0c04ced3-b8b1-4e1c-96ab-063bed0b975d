<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('sponsorship_tiers', function (Blueprint $table) {
            $table->id();
            $table->string('name'); // e.g., "Title Sponsor", "Gold", "Silver", "Bronze"
            $table->string('slug')->unique();
            $table->text('description')->nullable();
            $table->decimal('price', 10, 2)->nullable(); // Base price for this tier
            $table->json('benefits')->nullable(); // JSON array of benefits
            $table->string('color', 7)->default('#3B82F6'); // Hex color for display
            $table->integer('max_sponsors_per_event')->default(1); // How many sponsors of this tier per event
            $table->integer('display_order')->default(0); // Order for display
            $table->boolean('is_active')->default(true);
            $table->timestamps();

            $table->index(['is_active', 'display_order']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('sponsorship_tiers');
    }
};
