<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>@yield('title', 'Experience the Night Like Never Before - Liquid Lights')</title>

    <!-- Modern Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&family=Playfair+Display:wght@400;500;600;700;800;900&display=swap" rel="stylesheet">

    <!-- PWA Meta Tags -->
    <meta name="theme-color" content="#3B82F6">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="LL Tickets">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="application-name" content="Liquid Lights Tickets">

    <!-- PWA Icons -->
    <link rel="apple-touch-icon" sizes="180x180" href="/images/icons/icon-192x192.png">
    <link rel="icon" type="image/png" sizes="32x32" href="/images/icons/icon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="/images/icons/icon-16x16.png">
    <link rel="mask-icon" href="/images/icons/safari-pinned-tab.svg" color="#3B82F6">
    <meta name="msapplication-TileColor" content="#3B82F6">
    <meta name="msapplication-config" content="/browserconfig.xml">

    <!-- PWA Manifest -->
    <link rel="manifest" href="/manifest.json">
    <meta name="description" content="@yield('description', 'Book tickets for the hottest events, concerts, and shows. Secure booking, instant confirmation, and premium customer service.')">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/favicon.ico">
    
    <!-- Styles -->
    @vite(['resources/css/app.css', 'resources/js/public.js'])
    <link rel="stylesheet" href="{{ asset('css/mobile-enhancements.css') }}">

    <!-- Liquid Lights Dark Theme with Neon Animations -->
    <style>
        :root {
            --vh: 1vh;

            /* Dark Theme Colors */
            --bg-primary: #0a0a0a;
            --bg-secondary: #111111;
            --bg-tertiary: #1a1a1a;

            /* Neon Colors */
            --neon-cyan: #00ffff;
            --neon-pink: #ff00ff;
            --neon-purple: #8a2be2;
            --neon-blue: #0080ff;
            --neon-green: #00ff80;

            /* Liquid Light Gradients */
            --liquid-gradient-1: linear-gradient(45deg, var(--neon-cyan), var(--neon-purple));
            --liquid-gradient-2: linear-gradient(135deg, var(--neon-pink), var(--neon-blue));
            --liquid-gradient-3: linear-gradient(90deg, var(--neon-purple), var(--neon-cyan), var(--neon-pink));

            /* Glass Effects */
            --glass-bg: rgba(255, 255, 255, 0.05);
            --glass-border: rgba(255, 255, 255, 0.1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            background: var(--bg-primary);
            color: #ffffff;
            line-height: 1.6;
            overflow-x: hidden;
        }

        .font-display {
            font-family: 'Playfair Display', serif;
        }

        /* Liquid Light Animations */
        @keyframes liquidFlow {
            0% { transform: translateX(-100%) rotate(0deg); }
            50% { transform: translateX(100vw) rotate(180deg); }
            100% { transform: translateX(-100%) rotate(360deg); }
        }

        @keyframes neonPulse {
            0%, 100% {
                text-shadow: 0 0 5px currentColor, 0 0 10px currentColor, 0 0 15px currentColor;
                filter: brightness(1);
            }
            50% {
                text-shadow: 0 0 10px currentColor, 0 0 20px currentColor, 0 0 30px currentColor;
                filter: brightness(1.2);
            }
        }

        @keyframes liquidGlow {
            0%, 100% {
                box-shadow: 0 0 20px rgba(0, 255, 255, 0.3), 0 0 40px rgba(255, 0, 255, 0.2);
            }
            33% {
                box-shadow: 0 0 20px rgba(255, 0, 255, 0.3), 0 0 40px rgba(138, 43, 226, 0.2);
            }
            66% {
                box-shadow: 0 0 20px rgba(138, 43, 226, 0.3), 0 0 40px rgba(0, 255, 255, 0.2);
            }
        }

        @keyframes floatingLights {
            0% { transform: translateY(0px) rotate(0deg); opacity: 0.7; }
            33% { transform: translateY(-20px) rotate(120deg); opacity: 1; }
            66% { transform: translateY(10px) rotate(240deg); opacity: 0.8; }
            100% { transform: translateY(0px) rotate(360deg); opacity: 0.7; }
        }

        /* Neon Text Effects */
        .neon-text {
            color: var(--neon-cyan);
            animation: neonPulse 2s ease-in-out infinite;
        }

        .neon-text-pink {
            color: var(--neon-pink);
            animation: neonPulse 2.5s ease-in-out infinite;
        }

        .neon-text-purple {
            color: var(--neon-purple);
            animation: neonPulse 3s ease-in-out infinite;
        }

        /* Liquid Background Effects */
        .liquid-bg {
            position: relative;
            overflow: hidden;
        }

        .liquid-bg::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: var(--liquid-gradient-3);
            opacity: 0.1;
            animation: liquidFlow 20s linear infinite;
            z-index: -1;
        }

        /* Glass Morphism */
        .glass-morph {
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border);
            border-radius: 20px;
            animation: liquidGlow 4s ease-in-out infinite;
        }

        /* Floating Light Orbs */
        .light-orb {
            position: absolute;
            width: 100px;
            height: 100px;
            border-radius: 50%;
            background: radial-gradient(circle, var(--neon-cyan) 0%, transparent 70%);
            opacity: 0.3;
            animation: floatingLights 6s ease-in-out infinite;
            pointer-events: none;
        }

        .light-orb:nth-child(2) {
            background: radial-gradient(circle, var(--neon-pink) 0%, transparent 70%);
            animation-delay: -2s;
            animation-duration: 8s;
        }

        .light-orb:nth-child(3) {
            background: radial-gradient(circle, var(--neon-purple) 0%, transparent 70%);
            animation-delay: -4s;
            animation-duration: 10s;
        }

        /* Liquid Button Effects */
        .liquid-btn {
            position: relative;
            background: var(--liquid-gradient-1);
            border: none;
            border-radius: 50px;
            padding: 15px 30px;
            color: #000;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 1px;
            overflow: hidden;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .liquid-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: var(--liquid-gradient-2);
            transition: left 0.5s ease;
            z-index: -1;
        }

        .liquid-btn:hover::before {
            left: 0;
        }

        .liquid-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 30px rgba(0, 255, 255, 0.3);
        }

        /* Smooth scrolling */
        html {
            scroll-behavior: smooth;
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .light-orb {
                width: 60px;
                height: 60px;
            }
        }
    </style>

    <!-- Additional head content -->
    @stack('head')
</head>
<body class="bg-gray-50 font-sans antialiased">
    <!-- Header -->
    <header class="glass-morph sticky top-0 z-50 border-b border-white/10" x-data="{ mobileMenuOpen: false }">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-20">
                <!-- Logo -->
                <div class="flex-shrink-0">
                    <a href="{{ route('public.index') }}" class="flex items-center group">
                        <div class="w-14 h-14 glass-morph flex items-center justify-center group-hover:scale-110 transition-all duration-300">
                            <svg class="w-8 h-8 neon-text" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                            </svg>
                        </div>
                        <div class="ml-4">
                            <span class="text-3xl font-bold font-display neon-text">Liquid Lights</span>
                            <div class="text-sm text-gray-400 font-medium">Your Night Our Vibe</div>
                        </div>
                    </a>
                </div>

                <!-- Desktop Navigation -->
                <nav class="hidden lg:flex space-x-2">
                    <a href="{{ route('public.index') }}"
                       class="relative px-6 py-3 text-sm font-semibold transition-all duration-300 rounded-full group
                              {{ request()->routeIs('public.index') ? 'neon-text glass-morph' : 'text-gray-300 hover:text-white hover:glass-morph' }}">
                        <span class="relative z-10">Home</span>
                        @if(request()->routeIs('public.index'))
                            <div class="absolute inset-0 bg-gradient-to-r from-cyan-500/20 to-purple-500/20 rounded-full"></div>
                        @endif
                    </a>
                    <a href="{{ route('public.events') }}"
                       class="relative px-6 py-3 text-sm font-semibold transition-all duration-300 rounded-full group
                              {{ request()->routeIs('public.events*') ? 'neon-text glass-morph' : 'text-gray-300 hover:text-white hover:glass-morph' }}">
                        <span class="relative z-10">Events</span>
                        @if(request()->routeIs('public.events*'))
                            <div class="absolute inset-0 bg-gradient-to-r from-cyan-500/20 to-purple-500/20 rounded-full"></div>
                        @endif
                    </a>
                    <a href="#experience"
                       class="relative px-6 py-3 text-sm font-semibold transition-all duration-300 rounded-full group text-gray-300 hover:text-white hover:glass-morph">
                        <span class="relative z-10">Experience</span>
                    </a>
                    <a href="#about"
                       class="relative px-6 py-3 text-sm font-semibold transition-all duration-300 rounded-full group text-gray-300 hover:text-white hover:glass-morph">
                        <span class="relative z-10">About</span>
                    </a>
                    <a href="#contact"
                       class="relative px-6 py-3 text-sm font-semibold transition-all duration-300 rounded-full group text-gray-300 hover:text-white hover:glass-morph">
                        <span class="relative z-10">Contact</span>
                    </a>
                </nav>

                <!-- Search Bar -->
                <div class="hidden lg:flex flex-1 max-w-md mx-8">
                    <form action="{{ route('public.search') }}" method="GET" class="w-full">
                        <div class="relative">
                            <input type="text" 
                                   name="q" 
                                   placeholder="Search events, artists, venues..." 
                                   value="{{ request('q') }}"
                                   class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                                </svg>
                            </div>
                        </div>
                    </form>
                </div>

                <!-- User Menu -->
                <div class="flex items-center space-x-4">
                    @auth
                        <!-- Cart Icon -->
                        <div class="relative">
                            <a href="{{ route('booking.cart') }}" class="p-2 text-gray-700 hover:text-blue-600 transition-colors">
                                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-1.5 6M7 13l-1.5 6m0 0h9M17 13v6a2 2 0 01-2 2H9a2 2 0 01-2-2v-6"></path>
                                </svg>
                                <span x-show="$store.cart.totalItems > 0"
                                      x-text="$store.cart.totalItems"
                                      class="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center"></span>
                            </a>
                        </div>

                        <!-- User Dropdown -->
                        <div class="relative" x-data="{ open: false }">
                            <button @click="open = !open" class="flex items-center space-x-2 text-gray-700 hover:text-blue-600 transition-colors">
                                <div class="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center">
                                    <span class="text-sm font-medium">{{ substr(auth()->user()->name, 0, 1) }}</span>
                                </div>
                                <span class="hidden md:block text-sm font-medium">{{ auth()->user()->name }}</span>
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                </svg>
                            </button>

                            <div x-show="open" 
                                 @click.away="open = false"
                                 x-transition:enter="transition ease-out duration-100"
                                 x-transition:enter-start="transform opacity-0 scale-95"
                                 x-transition:enter-end="transform opacity-100 scale-100"
                                 x-transition:leave="transition ease-in duration-75"
                                 x-transition:leave-start="transform opacity-100 scale-100"
                                 x-transition:leave-end="transform opacity-0 scale-95"
                                 class="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-50">
                                <a href="{{ route('user.dashboard') }}" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Dashboard</a>
                                <a href="{{ route('user.bookings') }}" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">My Bookings</a>
                                <a href="{{ route('user.profile') }}" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Profile</a>
                                <div class="border-t border-gray-100"></div>
                                <form action="{{ route('logout') }}" method="POST">
                                    @csrf
                                    <button type="submit" class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Sign Out</button>
                                </form>
                            </div>
                        </div>
                    @else
                        <a href="{{ route('login') }}" class="text-gray-700 hover:text-blue-600 text-sm font-medium transition-colors">
                            Sign In
                        </a>
                        <a href="{{ route('register') }}" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors">
                            Sign Up
                        </a>
                    @endauth

                    <!-- Mobile menu button -->
                    <button @click="mobileMenuOpen = !mobileMenuOpen" class="md:hidden p-2 text-gray-700 hover:text-blue-600">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path x-show="!mobileMenuOpen" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                            <path x-show="mobileMenuOpen" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
            </div>
        </div>

        <!-- Mobile Menu -->
        <div x-show="mobileMenuOpen" 
             x-transition:enter="transition ease-out duration-200"
             x-transition:enter-start="opacity-0 transform -translate-y-2"
             x-transition:enter-end="opacity-100 transform translate-y-0"
             x-transition:leave="transition ease-in duration-150"
             x-transition:leave-start="opacity-100 transform translate-y-0"
             x-transition:leave-end="opacity-0 transform -translate-y-2"
             class="md:hidden bg-white border-t border-gray-200">
            <div class="px-2 pt-2 pb-3 space-y-1">
                <!-- Mobile Search -->
                <div class="px-3 py-2">
                    <form action="{{ route('public.search') }}" method="GET">
                        <div class="relative">
                            <input type="text" 
                                   name="q" 
                                   placeholder="Search events..." 
                                   value="{{ request('q') }}"
                                   class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                                </svg>
                            </div>
                        </div>
                    </form>
                </div>

                <!-- Mobile Navigation Links -->
                <a href="{{ route('public.index') }}" class="block px-3 py-2 text-base font-medium text-gray-700 hover:text-blue-600 hover:bg-gray-50 rounded-md">Home</a>
                <a href="{{ route('public.events') }}" class="block px-3 py-2 text-base font-medium text-gray-700 hover:text-blue-600 hover:bg-gray-50 rounded-md">Events</a>
                <a href="{{ route('public.artists') }}" class="block px-3 py-2 text-base font-medium text-gray-700 hover:text-blue-600 hover:bg-gray-50 rounded-md">Artists</a>
                <a href="#" class="block px-3 py-2 text-base font-medium text-gray-700 hover:text-blue-600 hover:bg-gray-50 rounded-md">About</a>
                <a href="#" class="block px-3 py-2 text-base font-medium text-gray-700 hover:text-blue-600 hover:bg-gray-50 rounded-md">Contact</a>
                
                @guest
                    <div class="border-t border-gray-200 pt-4">
                        <a href="{{ route('login') }}" class="block px-3 py-2 text-base font-medium text-gray-700 hover:text-blue-600 hover:bg-gray-50 rounded-md">Sign In</a>
                        <a href="{{ route('register') }}" class="block px-3 py-2 text-base font-medium text-blue-600 hover:bg-blue-50 rounded-md">Sign Up</a>
                    </div>
                @endguest
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="min-h-screen">
        @yield('content')
    </main>

    <!-- Footer -->
    <footer class="bg-gray-900 text-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                <!-- Company Info -->
                <div class="col-span-1 md:col-span-2">
                    <div class="flex items-center mb-4">
                        <div class="w-12 h-12 bg-gradient-to-r from-purple-600 to-cyan-500 rounded-xl flex items-center justify-center shadow-lg">
                            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                            </svg>
                        </div>
                        <div class="ml-3">
                            <span class="text-2xl font-bold font-display bg-gradient-to-r from-purple-400 to-cyan-400 bg-clip-text text-transparent">Liquid Lights</span>
                            <div class="text-xs text-gray-400 font-medium">Your Night our Vibe</div>
                        </div>
                    </div>
                    <p class="text-gray-300 mb-6 max-w-md leading-relaxed">
                        Join us for unforgettable nightlife and energy. Experience the night like never before with music, lights, and luxury.
                    </p>

                    <!-- Contact Info -->
                    <div class="space-y-3 mb-6">
                        <div class="flex items-center text-gray-300">
                            <svg class="w-5 h-5 mr-3 text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                            </svg>
                            <a href="mailto:<EMAIL>" class="hover:text-white transition-colors"><EMAIL></a>
                        </div>
                        <div class="flex items-center text-gray-300">
                            <svg class="w-5 h-5 mr-3 text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                            </svg>
                            <a href="tel:+918886111981" class="hover:text-white transition-colors">+91 - 888 6 111 981</a>
                        </div>
                        <div class="flex items-center text-gray-300">
                            <svg class="w-5 h-5 mr-3 text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                            </svg>
                            <span>Haailand, Mangalagiri, Guntur</span>
                        </div>
                    </div>
                    <div class="flex space-x-4">
                        <a href="https://www.facebook.com/" target="_blank" class="w-10 h-10 bg-gray-800 hover:bg-purple-600 rounded-lg flex items-center justify-center transition-colors group">
                            <svg class="w-5 h-5 text-gray-400 group-hover:text-white" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                            </svg>
                        </a>
                        <a href="https://www.instagram.com/" target="_blank" class="w-10 h-10 bg-gray-800 hover:bg-purple-600 rounded-lg flex items-center justify-center transition-colors group">
                            <svg class="w-5 h-5 text-gray-400 group-hover:text-white" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M12.017 0C5.396 0 .029 5.367.029 11.987c0 6.62 5.367 11.987 11.988 11.987 6.62 0 11.987-5.367 11.987-11.987C24.014 5.367 18.637.001 12.017.001zM8.449 16.988c-1.297 0-2.448-.49-3.323-1.297C4.198 14.895 3.708 13.744 3.708 12.447s.49-2.448 1.297-3.323C5.902 8.198 7.053 7.708 8.35 7.708s2.448.49 3.323 1.297c.897.875 1.387 2.026 1.387 3.323s-.49 2.448-1.297 3.323c-.875.897-2.026 1.387-3.323 1.387zm7.718 0c-1.297 0-2.448-.49-3.323-1.297-.897-.875-1.387-2.026-1.387-3.323s.49-2.448 1.297-3.323c.875-.897 2.026-1.387 3.323-1.387s2.448.49 3.323 1.297c.897.875 1.387 2.026 1.387 3.323s-.49 2.448-1.297 3.323c-.875.897-2.026 1.387-3.323 1.387z"/>
                            </svg>
                        </a>
                        <a href="#" class="text-gray-400 hover:text-white transition-colors">
                            <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M12.017 0C5.396 0 .029 5.367.029 11.987c0 5.079 3.158 9.417 7.618 11.174-.105-.949-.199-2.403.041-3.439.219-.937 1.406-5.957 1.406-5.957s-.359-.72-.359-1.781c0-1.663.967-2.911 2.168-2.911 1.024 0 1.518.769 1.518 1.688 0 1.029-.653 2.567-.992 3.992-.285 1.193.6 2.165 1.775 2.165 2.128 0 3.768-2.245 3.768-5.487 0-2.861-2.063-4.869-5.008-4.869-3.41 0-5.409 2.562-5.409 5.199 0 1.033.394 2.143.889 2.741.099.12.112.225.085.345-.09.375-.293 1.199-.334 1.363-.053.225-.172.271-.402.165-1.495-.69-2.433-2.878-2.433-4.646 0-3.776 2.748-7.252 7.92-7.252 4.158 0 7.392 2.967 7.392 6.923 0 4.135-2.607 7.462-6.233 7.462-1.214 0-2.357-.629-2.75-1.378l-.748 2.853c-.271 1.043-1.002 2.35-1.492 3.146C9.57 23.812 10.763 24.009 12.017 24.009c6.624 0 11.99-5.367 11.99-11.988C24.007 5.367 18.641.001.012.001z"/>
                            </svg>
                        </a>
                        <a href="#" class="text-gray-400 hover:text-white transition-colors">
                            <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M12.5.75C6.146.75 1 5.896 1 12.25c0 5.089 3.292 9.387 7.863 10.91-.11-.937-.227-2.482.025-3.566.217-.932 1.405-5.956 1.405-5.956s-.359-.719-.359-1.782c0-1.668.967-2.914 2.171-2.914 1.023 0 1.518.769 1.518 1.69 0 1.029-.653 2.567-.992 3.992-.285 1.193.6 2.165 1.775 2.165 2.128 0 3.768-2.245 3.768-5.487 0-2.861-2.063-4.869-5.008-4.869-3.41 0-5.409 2.562-5.409 5.199 0 1.033.394 2.143.889 2.741.099.12.112.225.085.345-.09.375-.293 1.199-.334 1.363-.053.225-.172.271-.402.165-1.495-.69-2.433-2.878-2.433-4.646 0-3.776 2.748-7.252 7.92-7.252 4.158 0 7.392 2.967 7.392 6.923 0 4.135-2.607 7.462-6.233 7.462-1.214 0-2.357-.629-2.75-1.378l-.748 2.853c-.271 1.043-1.002 2.35-1.492 3.146 1.124.347 2.317.544 3.571.544 6.624 0 11.99-5.367 11.99-11.988C24.5 5.896 19.354.75 12.5.75z"/>
                            </svg>
                        </a>
                    </div>
                </div>

                <!-- Quick Links -->
                <div>
                    <h3 class="text-lg font-semibold mb-4">Quick Links</h3>
                    <ul class="space-y-2">
                        <li><a href="{{ route('public.events') }}" class="text-gray-300 hover:text-white transition-colors">Browse Events</a></li>
                        <li><a href="{{ route('public.artists') }}" class="text-gray-300 hover:text-white transition-colors">Featured Artists</a></li>
                        <li><a href="#" class="text-gray-300 hover:text-white transition-colors">Venues</a></li>
                        <li><a href="#" class="text-gray-300 hover:text-white transition-colors">Gift Cards</a></li>
                        <li><a href="#" class="text-gray-300 hover:text-white transition-colors">Group Bookings</a></li>
                    </ul>
                </div>

                <!-- Support -->
                <div>
                    <h3 class="text-lg font-semibold mb-4">Support</h3>
                    <ul class="space-y-2">
                        <li><a href="#" class="text-gray-300 hover:text-white transition-colors">Help Center</a></li>
                        <li><a href="#" class="text-gray-300 hover:text-white transition-colors">Contact Us</a></li>
                        <li><a href="#" class="text-gray-300 hover:text-white transition-colors">Refund Policy</a></li>
                        <li><a href="#" class="text-gray-300 hover:text-white transition-colors">Terms of Service</a></li>
                        <li><a href="#" class="text-gray-300 hover:text-white transition-colors">Privacy Policy</a></li>
                    </ul>
                </div>
            </div>

            <div class="border-t border-gray-800 mt-8 pt-8 flex flex-col md:flex-row justify-between items-center">
                <p class="text-gray-400 text-sm">
                    © {{ date('Y') }} Liquid Lights Tickets. All rights reserved.
                </p>
                <div class="flex space-x-6 mt-4 md:mt-0">
                    <a href="#" class="text-gray-400 hover:text-white text-sm transition-colors">Privacy</a>
                    <a href="#" class="text-gray-400 hover:text-white text-sm transition-colors">Terms</a>
                    <a href="#" class="text-gray-400 hover:text-white text-sm transition-colors">Cookies</a>
                </div>
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    @stack('scripts')

    <!-- Mobile Interactions -->
    <script src="{{ asset('js/mobile-interactions.js') }}"></script>
    <script src="{{ asset('js/offline-manager.js') }}"></script>
    <script src="{{ asset('js/push-notifications.js') }}"></script>
    <script src="{{ asset('js/app-installation.js') }}"></script>

    <!-- PWA Service Worker Registration -->
    <script>
        // Service Worker Registration
        if ('serviceWorker' in navigator) {
            window.addEventListener('load', () => {
                navigator.serviceWorker.register('/sw.js')
                    .then(registration => {
                        console.log('SW registered: ', registration);

                        // Check for updates
                        registration.addEventListener('updatefound', () => {
                            const newWorker = registration.installing;
                            newWorker.addEventListener('statechange', () => {
                                if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
                                    // New content is available, show update notification
                                    showUpdateNotification();
                                }
                            });
                        });
                    })
                    .catch(registrationError => {
                        console.log('SW registration failed: ', registrationError);
                    });
            });
        }

        // PWA Install Prompt
        let deferredPrompt;
        let installButton = null;

        window.addEventListener('beforeinstallprompt', (e) => {
            console.log('PWA install prompt triggered');
            e.preventDefault();
            deferredPrompt = e;
            showInstallButton();
        });

        window.addEventListener('appinstalled', (evt) => {
            console.log('PWA was installed');
            hideInstallButton();

            // Track installation
            if (typeof gtag !== 'undefined') {
                gtag('event', 'pwa_install', {
                    event_category: 'engagement',
                    event_label: 'PWA Installation'
                });
            }
        });

        function showInstallButton() {
            // Create install button if it doesn't exist
            if (!installButton) {
                installButton = document.createElement('button');
                installButton.innerHTML = `
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                    </svg>
                    Install App
                `;
                installButton.className = 'fixed bottom-4 right-4 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg shadow-lg flex items-center text-sm font-medium transition-all duration-300 z-50';
                installButton.onclick = installPWA;
                document.body.appendChild(installButton);
            }

            installButton.style.display = 'flex';
        }

        function hideInstallButton() {
            if (installButton) {
                installButton.style.display = 'none';
            }
        }

        function installPWA() {
            if (deferredPrompt) {
                deferredPrompt.prompt();
                deferredPrompt.userChoice.then((result) => {
                    console.log('User choice: ', result);
                    if (result.outcome === 'accepted') {
                        console.log('User accepted the install prompt');
                    } else {
                        console.log('User dismissed the install prompt');
                    }
                    deferredPrompt = null;
                    hideInstallButton();
                });
            }
        }

        function showUpdateNotification() {
            // Create update notification
            const notification = document.createElement('div');
            notification.className = 'fixed top-4 right-4 bg-green-600 text-white px-6 py-3 rounded-lg shadow-lg z-50 max-w-sm';
            notification.innerHTML = `
                <div class="flex items-center justify-between">
                    <div>
                        <p class="font-medium">Update Available</p>
                        <p class="text-sm opacity-90">A new version is ready to install</p>
                    </div>
                    <button onclick="this.parentElement.parentElement.remove()" class="ml-4 text-white hover:text-gray-200">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
                <button onclick="window.location.reload()" class="mt-2 bg-white text-green-600 px-3 py-1 rounded text-sm font-medium hover:bg-gray-100">
                    Update Now
                </button>
            `;
            document.body.appendChild(notification);

            // Auto-remove after 10 seconds
            setTimeout(() => {
                if (notification.parentElement) {
                    notification.remove();
                }
            }, 10000);
        }

        // Network status monitoring
        function updateNetworkStatus() {
            const isOnline = navigator.onLine;
            const statusIndicator = document.getElementById('network-status');

            if (statusIndicator) {
                if (isOnline) {
                    statusIndicator.className = 'hidden';
                } else {
                    statusIndicator.className = 'fixed top-0 left-0 right-0 bg-red-600 text-white text-center py-2 text-sm z-50';
                    statusIndicator.textContent = '📡 You are offline. Some features may be limited.';
                }
            }
        }

        window.addEventListener('online', updateNetworkStatus);
        window.addEventListener('offline', updateNetworkStatus);

        // Add network status indicator to page
        document.addEventListener('DOMContentLoaded', () => {
            const statusIndicator = document.createElement('div');
            statusIndicator.id = 'network-status';
            statusIndicator.className = 'hidden';
            document.body.appendChild(statusIndicator);
            updateNetworkStatus();
        });
    </script>
</body>
</html>
