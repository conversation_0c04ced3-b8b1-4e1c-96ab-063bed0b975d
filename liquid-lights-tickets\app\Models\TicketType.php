<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class TicketType extends Model
{
    use HasFactory;

    protected $fillable = [
        'event_id',
        'name',
        'price',
        'quantity_available',
        'quantity_sold',
        'sale_start_date',
        'sale_end_date',
        'is_active',
    ];

    protected function casts(): array
    {
        return [
            'price' => 'decimal:2',
            'sale_start_date' => 'datetime',
            'sale_end_date' => 'datetime',
            'is_active' => 'boolean',
        ];
    }

    /**
     * Relationships
     */
    public function event()
    {
        return $this->belongsTo(Event::class);
    }

    public function bookings()
    {
        return $this->hasMany(Booking::class);
    }

    /**
     * Accessors & Mutators
     */
    public function getAvailableQuantityAttribute()
    {
        return $this->quantity_available - $this->quantity_sold;
    }

    public function getIsSoldOutAttribute()
    {
        return $this->available_quantity <= 0;
    }

    public function getIsOnSaleAttribute()
    {
        $now = now();
        return $this->is_active &&
               $now->greaterThanOrEqualTo($this->sale_start_date) &&
               $now->lessThanOrEqualTo($this->sale_end_date);
    }
}
