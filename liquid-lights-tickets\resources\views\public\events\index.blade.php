@extends('public.layout')

@section('title', 'Events - Liquid Lights Tickets')
@section('description', 'Browse and book tickets for upcoming events, concerts, shows, and festivals. Find your next amazing experience.')

@section('content')
<div x-data="eventFilters">
    <!-- Page Header -->
    <section class="bg-gradient-to-r from-blue-600 to-purple-600 text-white py-16">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center">
                <h1 class="text-4xl md:text-5xl font-bold mb-4">Discover Events</h1>
                <p class="text-xl text-blue-100 max-w-2xl mx-auto">
                    Find and book tickets for the most exciting events happening around you
                </p>
            </div>
        </div>
    </section>

    <!-- Filters Section -->
    <section class="bg-white border-b border-gray-200 sticky top-16 z-40">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-4">
                <!-- Search -->
                <div class="lg:col-span-2">
                    <label for="search" class="block text-sm font-medium text-gray-700 mb-1">Search</label>
                    <input x-model="filters.search" 
                           type="text" 
                           id="search"
                           placeholder="Search events, artists, venues..."
                           class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                </div>

                <!-- Date From -->
                <div>
                    <label for="date_from" class="block text-sm font-medium text-gray-700 mb-1">From Date</label>
                    <input x-model="filters.date_from" 
                           type="date" 
                           id="date_from"
                           :min="new Date().toISOString().split('T')[0]"
                           class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                </div>

                <!-- Date To -->
                <div>
                    <label for="date_to" class="block text-sm font-medium text-gray-700 mb-1">To Date</label>
                    <input x-model="filters.date_to" 
                           type="date" 
                           id="date_to"
                           :min="filters.date_from || new Date().toISOString().split('T')[0]"
                           class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                </div>

                <!-- Price Range -->
                <div>
                    <label for="price_min" class="block text-sm font-medium text-gray-700 mb-1">Min Price</label>
                    <input x-model="filters.price_min" 
                           type="number" 
                           id="price_min"
                           placeholder="₹0"
                           min="0"
                           class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                </div>

                <!-- Sort -->
                <div>
                    <label for="sort" class="block text-sm font-medium text-gray-700 mb-1">Sort By</label>
                    <select x-model="filters.sort" 
                            id="sort"
                            class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        <option value="date">Date (Earliest)</option>
                        <option value="price_low">Price (Low to High)</option>
                        <option value="price_high">Price (High to Low)</option>
                        <option value="popularity">Popularity</option>
                    </select>
                </div>
            </div>

            <!-- Filter Actions -->
            <div class="flex flex-col sm:flex-row gap-4 mt-6">
                <button @click="applyFilters()" 
                        class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg font-semibold transition-colors">
                    Apply Filters
                </button>
                <button @click="clearFilters()" 
                        class="border border-gray-300 text-gray-700 hover:bg-gray-50 px-6 py-2 rounded-lg font-semibold transition-colors">
                    Clear All
                </button>
                <div class="flex-1"></div>
                <div class="text-sm text-gray-600 flex items-center">
                    Showing {{ $events->count() }} of {{ $events->total() }} events
                </div>
            </div>
        </div>
    </section>

    <!-- Events Grid -->
    <section class="py-12 bg-gray-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            @if($events->count() > 0)
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                    @foreach($events as $event)
                    <div class="bg-white rounded-xl shadow-md overflow-hidden hover:shadow-lg transition-shadow duration-300 group">
                        <div class="relative">
                            <img src="{{ $event->banner_image ?: '/images/event-placeholder.jpg' }}" 
                                 alt="{{ $event->title }}" 
                                 class="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300">
                            
                            <!-- Event Date Badge -->
                            <div class="absolute top-4 left-4">
                                <div class="bg-white bg-opacity-95 rounded-lg px-3 py-2 text-center">
                                    <div class="text-sm font-bold text-gray-900">
                                        {{ \Carbon\Carbon::parse($event->event_date)->format('M') }}
                                    </div>
                                    <div class="text-lg font-bold text-blue-600">
                                        {{ \Carbon\Carbon::parse($event->event_date)->format('d') }}
                                    </div>
                                </div>
                            </div>

                            <!-- Featured Badge -->
                            @if($event->is_featured)
                            <div class="absolute top-4 right-4">
                                <span class="bg-yellow-500 text-gray-900 px-3 py-1 rounded-full text-sm font-semibold">
                                    Featured
                                </span>
                            </div>
                            @endif

                            <!-- Quick Actions -->
                            <div class="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-300 flex items-center justify-center opacity-0 group-hover:opacity-100">
                                <a href="{{ route('public.event', $event->id) }}" 
                                   class="bg-white text-gray-900 px-6 py-2 rounded-lg font-semibold hover:bg-gray-100 transition-colors">
                                    View Details
                                </a>
                            </div>
                        </div>
                        
                        <div class="p-6">
                            <h3 class="text-lg font-bold text-gray-900 mb-2 group-hover:text-blue-600 transition-colors line-clamp-2">
                                {{ $event->title }}
                            </h3>
                            
                            <p class="text-gray-600 text-sm mb-4 line-clamp-2">
                                {{ $event->short_description }}
                            </p>
                            
                            <!-- Event Details -->
                            <div class="space-y-2 mb-4">
                                <div class="flex items-center text-gray-500 text-sm">
                                    <svg class="w-4 h-4 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                    </svg>
                                    {{ \Carbon\Carbon::parse($event->event_date)->format('l, M d, Y') }}
                                </div>
                                
                                <div class="flex items-center text-gray-500 text-sm">
                                    <svg class="w-4 h-4 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                    {{ \Carbon\Carbon::parse($event->event_time)->format('g:i A') }}
                                </div>
                                
                                <div class="flex items-center text-gray-500 text-sm">
                                    <svg class="w-4 h-4 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                    </svg>
                                    {{ Str::limit($event->venue_name, 25) }}
                                </div>
                            </div>
                            
                            <!-- Artists -->
                            @if($event->artists->count() > 0)
                            <div class="mb-4">
                                <div class="flex flex-wrap gap-1">
                                    @foreach($event->artists->take(2) as $artist)
                                    <span class="bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs font-medium">
                                        {{ $artist->name }}
                                    </span>
                                    @endforeach
                                    @if($event->artists->count() > 2)
                                    <span class="text-gray-500 text-xs py-1">
                                        +{{ $event->artists->count() - 2 }} more
                                    </span>
                                    @endif
                                </div>
                            </div>
                            @endif
                            
                            <!-- Price and Book Button -->
                            <div class="flex justify-between items-center pt-4 border-t border-gray-100">
                                <div>
                                    <span class="text-2xl font-bold text-blue-600">
                                        ₹{{ number_format($event->ticketTypes->min('price')) }}
                                    </span>
                                    @if($event->ticketTypes->min('price') != $event->ticketTypes->max('price'))
                                    <span class="text-gray-500 text-sm">onwards</span>
                                    @endif
                                </div>
                                <a href="{{ route('public.event', $event->id) }}" 
                                   class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-semibold transition-colors">
                                    Book Now
                                </a>
                            </div>
                        </div>
                    </div>
                    @endforeach
                </div>

                <!-- Pagination -->
                <div class="mt-12">
                    {{ $events->appends(request()->query())->links() }}
                </div>
            @else
                <!-- Empty State -->
                <div class="text-center py-16">
                    <svg class="mx-auto h-24 w-24 text-gray-400 mb-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                    </svg>
                    <h3 class="text-2xl font-bold text-gray-900 mb-2">No Events Found</h3>
                    <p class="text-gray-600 mb-8 max-w-md mx-auto">
                        We couldn't find any events matching your criteria. Try adjusting your filters or check back later for new events.
                    </p>
                    <div class="flex flex-col sm:flex-row gap-4 justify-center">
                        <button @click="clearFilters()" 
                                class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-semibold transition-colors">
                            Clear Filters
                        </button>
                        <a href="{{ route('public.index') }}" 
                           class="border border-gray-300 text-gray-700 hover:bg-gray-50 px-6 py-3 rounded-lg font-semibold transition-colors">
                            Back to Home
                        </a>
                    </div>
                </div>
            @endif
        </div>
    </section>
</div>

@push('scripts')
<style>
.line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}
</style>
@endpush
@endsection
