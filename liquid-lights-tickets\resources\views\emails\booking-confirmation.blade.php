<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Booking Confirmation</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f4f4f4;
        }
        .container {
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            border-bottom: 2px solid #3B82F6;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        .logo {
            font-size: 24px;
            font-weight: bold;
            color: #3B82F6;
            margin-bottom: 10px;
        }
        .title {
            color: #1a1a1a;
            font-size: 28px;
            margin-bottom: 10px;
        }
        .subtitle {
            color: #666;
            font-size: 16px;
        }
        .booking-details {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .detail-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            padding: 8px 0;
            border-bottom: 1px solid #e9ecef;
        }
        .detail-row:last-child {
            border-bottom: none;
            font-weight: bold;
            font-size: 18px;
            color: #059669;
        }
        .detail-label {
            font-weight: bold;
            color: #555;
        }
        .detail-value {
            color: #333;
        }
        .event-info {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .event-title {
            font-size: 22px;
            font-weight: bold;
            margin-bottom: 15px;
        }
        .event-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
        }
        .event-detail {
            display: flex;
            align-items: center;
        }
        .event-detail svg {
            width: 16px;
            height: 16px;
            margin-right: 8px;
            opacity: 0.8;
        }
        .important-info {
            background-color: #fef3c7;
            border: 1px solid #f59e0b;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .important-info h3 {
            color: #92400e;
            margin-top: 0;
            margin-bottom: 10px;
        }
        .important-info ul {
            margin: 0;
            padding-left: 20px;
            color: #92400e;
        }
        .cta-button {
            display: inline-block;
            background-color: #3B82F6;
            color: white;
            padding: 12px 24px;
            text-decoration: none;
            border-radius: 6px;
            font-weight: bold;
            margin: 20px 0;
        }
        .footer {
            text-align: center;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #e9ecef;
            color: #666;
            font-size: 14px;
        }
        .social-links {
            margin: 20px 0;
        }
        .social-links a {
            display: inline-block;
            margin: 0 10px;
            color: #3B82F6;
            text-decoration: none;
        }
        @media (max-width: 600px) {
            .event-details {
                grid-template-columns: 1fr;
            }
            .detail-row {
                flex-direction: column;
            }
            .detail-label {
                margin-bottom: 5px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <div class="logo">🎫 LIQUID LIGHTS</div>
            <h1 class="title">Booking Confirmed!</h1>
            <p class="subtitle">Thank you for your purchase, {{ $user->name }}</p>
        </div>

        <!-- Event Information -->
        <div class="event-info">
            <div class="event-title">{{ $event->title }}</div>
            <div class="event-details">
                <div class="event-detail">
                    <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                    </svg>
                    {{ \Carbon\Carbon::parse($event->event_date)->format('l, F d, Y') }}
                </div>
                <div class="event-detail">
                    <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    {{ \Carbon\Carbon::parse($event->event_time)->format('g:i A') }}
                </div>
                <div class="event-detail">
                    <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                    </svg>
                    {{ $event->venue_name }}
                </div>
                <div class="event-detail">
                    <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                    </svg>
                    {{ $booking->booking_reference }}
                </div>
            </div>
        </div>

        <!-- Booking Details -->
        <div class="booking-details">
            <h3 style="margin-top: 0; color: #1a1a1a;">Booking Details</h3>
            
            <div class="detail-row">
                <span class="detail-label">Ticket Type:</span>
                <span class="detail-value">{{ $ticketType->name }}</span>
            </div>
            
            <div class="detail-row">
                <span class="detail-label">Quantity:</span>
                <span class="detail-value">{{ $booking->quantity }} {{ Str::plural('ticket', $booking->quantity) }}</span>
            </div>
            
            <div class="detail-row">
                <span class="detail-label">Price per ticket:</span>
                <span class="detail-value">₹{{ number_format($ticketType->price, 2) }}</span>
            </div>
            
            <div class="detail-row">
                <span class="detail-label">Payment Status:</span>
                <span class="detail-value">{{ ucfirst($booking->payment_status) }}</span>
            </div>
            
            <div class="detail-row">
                <span class="detail-label">Total Amount:</span>
                <span class="detail-value">₹{{ number_format($booking->total_amount, 2) }}</span>
            </div>
        </div>

        <!-- Important Information -->
        <div class="important-info">
            <h3>Important Information</h3>
            <ul>
                <li>Please arrive at the venue 30 minutes before the event start time</li>
                <li>Bring a valid government-issued photo ID for verification</li>
                <li>Your ticket will be available for download once payment is confirmed</li>
                <li>Screenshots of tickets will not be accepted - please present the original QR code</li>
                <li>This ticket is non-transferable and valid only for the person named above</li>
            </ul>
        </div>

        <!-- Call to Action -->
        <div style="text-align: center;">
            <a href="{{ route('user.booking', $booking->id) }}" class="cta-button">
                View Booking Details
            </a>
        </div>

        <!-- Venue Information -->
        @if($event->venue_address)
        <div style="margin: 20px 0; padding: 15px; background-color: #f1f5f9; border-radius: 8px;">
            <h3 style="margin-top: 0; color: #1a1a1a;">Venue Information</h3>
            <p style="margin: 0;"><strong>{{ $event->venue_name }}</strong></p>
            <p style="margin: 5px 0 0 0; color: #666;">{{ $event->venue_address }}</p>
        </div>
        @endif

        <!-- Footer -->
        <div class="footer">
            <div class="social-links">
                <a href="#">Facebook</a>
                <a href="#">Twitter</a>
                <a href="#">Instagram</a>
            </div>
            
            <p>
                Need help? Contact us at 
                <a href="mailto:<EMAIL>"><EMAIL></a> 
                or call +91-XXXXXXXXXX
            </p>
            
            <p style="margin-top: 20px; font-size: 12px; color: #999;">
                © {{ date('Y') }} Liquid Lights Tickets. All rights reserved.<br>
                This email was sent to {{ $user->email }}
            </p>
        </div>
    </div>
</body>
</html>
