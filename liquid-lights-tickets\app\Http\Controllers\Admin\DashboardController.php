<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Event;
use App\Models\Booking;
use App\Models\User;
use App\Models\TicketType;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class DashboardController extends Controller
{
    /**
     * Display admin dashboard with KPIs and analytics
     */
    public function index(Request $request)
    {
        // Get user from middleware
        $user = $request->attributes->get('admin_user');

        // Get date range (default to last 30 days)
        $startDate = $request->get('start_date', Carbon::now()->subDays(30));
        $endDate = $request->get('end_date', Carbon::now());

        try {
            $data = [
                'user' => $user,
                'kpis' => $this->getKPIs($startDate, $endDate),
                'charts' => $this->getChartData($startDate, $endDate),
                'recent_activities' => $this->getRecentActivities(),
                'upcoming_events' => $this->getUpcomingEvents(),
            ];

            return view('admin.dashboard', $data);
        } catch (\Exception $e) {
            // Fallback with empty data if there's an error
            $data = [
                'user' => $user,
                'kpis' => [
                    'total_revenue' => ['value' => 0, 'formatted' => '₹0.00', 'growth' => 0],
                    'total_bookings' => ['value' => 0, 'formatted' => '0'],
                    'total_tickets' => ['value' => 0, 'formatted' => '0'],
                    'active_events' => ['value' => 0, 'formatted' => '0'],
                    'new_users' => ['value' => 0, 'formatted' => '0'],
                    'avg_ticket_price' => ['value' => 0, 'formatted' => '₹0.00']
                ],
                'charts' => [
                    'daily_revenue' => [],
                    'event_popularity' => [],
                    'ticket_type_distribution' => []
                ],
                'recent_activities' => [],
                'upcoming_events' => [],
            ];

            return view('admin.dashboard', $data);
        }
    }

    /**
     * API endpoint for dashboard data (for AJAX requests)
     */
    public function apiData(Request $request)
    {
        $user = $request->user();

        // Check if user has admin privileges
        if (!in_array($user->role, ['admin', 'manager', 'scanner', 'booker'])) {
            return response()->json([
                'success' => false,
                'message' => 'Access denied'
            ], 403);
        }

        // Get date range (default to last 30 days)
        $startDate = $request->get('start_date', Carbon::now()->subDays(30));
        $endDate = $request->get('end_date', Carbon::now());

        $data = [
            'kpis' => $this->getKPIs($startDate, $endDate),
            'charts' => $this->getChartData($startDate, $endDate),
            'recent_activities' => $this->getRecentActivities(),
            'upcoming_events' => $this->getUpcomingEvents(),
        ];

        return response()->json([
            'success' => true,
            'data' => $data
        ]);
    }

    /**
     * Get Key Performance Indicators
     */
    private function getKPIs($startDate, $endDate)
    {
        // Total Revenue
        $totalRevenue = Booking::where('payment_status', 'paid')
            ->whereBetween('created_at', [$startDate, $endDate])
            ->sum('total_amount');

        // Total Bookings
        $totalBookings = Booking::whereBetween('created_at', [$startDate, $endDate])->count();

        // Total Tickets Sold
        $totalTickets = Booking::where('payment_status', 'paid')
            ->whereBetween('created_at', [$startDate, $endDate])
            ->sum('quantity');

        // Active Events
        $activeEvents = Event::where('status', 'published')
            ->where('event_date', '>=', Carbon::now())
            ->count();

        // New Users
        $newUsers = User::whereBetween('created_at', [$startDate, $endDate])->count();

        // Average Ticket Price
        $avgTicketPrice = $totalTickets > 0 ? $totalRevenue / $totalTickets : 0;

        // Previous period comparison
        $prevStartDate = Carbon::parse($startDate)->subDays(Carbon::parse($endDate)->diffInDays($startDate));
        $prevEndDate = $startDate;

        $prevRevenue = Booking::where('payment_status', 'paid')
            ->whereBetween('created_at', [$prevStartDate, $prevEndDate])
            ->sum('total_amount');

        $revenueGrowth = $prevRevenue > 0 ? (($totalRevenue - $prevRevenue) / $prevRevenue) * 100 : 0;

        return [
            'total_revenue' => [
                'value' => $totalRevenue,
                'formatted' => '₹' . number_format($totalRevenue, 2),
                'growth' => round($revenueGrowth, 1)
            ],
            'total_bookings' => [
                'value' => $totalBookings,
                'formatted' => number_format($totalBookings)
            ],
            'total_tickets' => [
                'value' => $totalTickets,
                'formatted' => number_format($totalTickets)
            ],
            'active_events' => [
                'value' => $activeEvents,
                'formatted' => number_format($activeEvents)
            ],
            'new_users' => [
                'value' => $newUsers,
                'formatted' => number_format($newUsers)
            ],
            'avg_ticket_price' => [
                'value' => $avgTicketPrice,
                'formatted' => '₹' . number_format($avgTicketPrice, 2)
            ]
        ];
    }

    /**
     * Get chart data for analytics
     */
    private function getChartData($startDate, $endDate)
    {
        // Daily revenue chart
        $dailyRevenue = Booking::select(
                DB::raw('DATE(created_at) as date'),
                DB::raw('SUM(total_amount) as revenue')
            )
            ->where('payment_status', 'paid')
            ->whereBetween('created_at', [$startDate, $endDate])
            ->groupBy('date')
            ->orderBy('date')
            ->get();

        // Event popularity (top 5 events by bookings)
        $eventPopularity = Event::select('events.title', DB::raw('COUNT(bookings.id) as booking_count'))
            ->leftJoin('bookings', 'events.id', '=', 'bookings.event_id')
            ->where('bookings.payment_status', 'paid')
            ->whereBetween('bookings.created_at', [$startDate, $endDate])
            ->groupBy('events.id', 'events.title')
            ->orderBy('booking_count', 'desc')
            ->limit(5)
            ->get();

        // Ticket type distribution
        $ticketTypeDistribution = TicketType::select('ticket_types.name', DB::raw('SUM(bookings.quantity) as tickets_sold'))
            ->leftJoin('bookings', 'ticket_types.id', '=', 'bookings.ticket_type_id')
            ->where('bookings.payment_status', 'paid')
            ->whereBetween('bookings.created_at', [$startDate, $endDate])
            ->groupBy('ticket_types.id', 'ticket_types.name')
            ->orderBy('tickets_sold', 'desc')
            ->get();

        return [
            'daily_revenue' => $dailyRevenue,
            'event_popularity' => $eventPopularity,
            'ticket_type_distribution' => $ticketTypeDistribution
        ];
    }

    /**
     * Get recent activities for dashboard
     */
    private function getRecentActivities()
    {
        $recentBookings = Booking::with(['user', 'event'])
            ->orderBy('created_at', 'desc')
            ->limit(10)
            ->get()
            ->map(function ($booking) {
                return [
                    'type' => 'booking',
                    'message' => $booking->user->name . ' booked ' . $booking->quantity . ' ticket(s) for ' . $booking->event->title,
                    'timestamp' => $booking->created_at,
                    'status' => $booking->payment_status
                ];
            });

        $recentEvents = Event::orderBy('created_at', 'desc')
            ->limit(5)
            ->get()
            ->map(function ($event) {
                return [
                    'type' => 'event',
                    'message' => 'New event created: ' . $event->title,
                    'timestamp' => $event->created_at,
                    'status' => $event->status
                ];
            });

        return $recentBookings->concat($recentEvents)
            ->sortByDesc('timestamp')
            ->take(15)
            ->values();
    }

    /**
     * Get upcoming events
     */
    private function getUpcomingEvents()
    {
        return Event::where('status', 'published')
            ->where('event_date', '>=', Carbon::now())
            ->orderBy('event_date')
            ->limit(5)
            ->get()
            ->map(function ($event) {
                $totalTickets = $event->ticketTypes->sum('quantity_available');
                $soldTickets = $event->bookings()->where('payment_status', 'paid')->sum('quantity');

                return [
                    'id' => $event->id,
                    'title' => $event->title,
                    'date' => $event->event_date,
                    'time' => $event->event_time,
                    'venue' => $event->venue_name,
                    'total_tickets' => $totalTickets,
                    'sold_tickets' => $soldTickets,
                    'available_tickets' => $totalTickets - $soldTickets,
                    'sales_percentage' => $totalTickets > 0 ? round(($soldTickets / $totalTickets) * 100, 1) : 0
                ];
            });
    }

    /**
     * Get quick stats for admin dashboard
     */
    public function quickStats(Request $request)
    {
        $today = Carbon::today();

        $stats = [
            'today_bookings' => Booking::whereDate('created_at', $today)->count(),
            'today_revenue' => Booking::where('payment_status', 'paid')
                ->whereDate('created_at', $today)
                ->sum('total_amount'),
            'pending_bookings' => Booking::where('payment_status', 'pending')->count(),
            'active_events' => Event::where('status', 'published')
                ->where('event_date', '>=', $today)
                ->count(),
        ];

        return response()->json([
            'success' => true,
            'stats' => $stats
        ]);
    }
}
