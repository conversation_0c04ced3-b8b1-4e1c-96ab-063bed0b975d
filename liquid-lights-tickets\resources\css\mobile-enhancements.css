/* Mobile-First UI Enhancements for Liquid Lights Tickets PWA */

/* Touch-friendly button sizes */
.btn-touch {
    min-height: 44px;
    min-width: 44px;
    padding: 12px 16px;
    font-size: 16px;
    line-height: 1.2;
}

/* Larger tap targets for mobile */
.tap-target {
    min-height: 44px;
    min-width: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Mobile-optimized form inputs */
.form-input-mobile {
    font-size: 16px; /* Prevents zoom on iOS */
    padding: 12px 16px;
    border-radius: 8px;
    border: 2px solid #e5e7eb;
    transition: border-color 0.2s ease;
}

.form-input-mobile:focus {
    border-color: #3b82f6;
    outline: none;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Swipe gestures for cards */
.swipeable-card {
    position: relative;
    transform: translateX(0);
    transition: transform 0.3s ease;
    touch-action: pan-x;
}

.swipeable-card.swiping {
    transition: none;
}

.swipeable-card.swipe-left {
    transform: translateX(-100px);
}

.swipeable-card.swipe-right {
    transform: translateX(100px);
}

/* Pull-to-refresh indicator */
.pull-to-refresh {
    position: relative;
    overflow: hidden;
}

.pull-to-refresh-indicator {
    position: absolute;
    top: -60px;
    left: 50%;
    transform: translateX(-50%);
    width: 40px;
    height: 40px;
    background: #3b82f6;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    transition: top 0.3s ease;
    z-index: 10;
}

.pull-to-refresh.pulling .pull-to-refresh-indicator {
    top: 20px;
}

.pull-to-refresh.refreshing .pull-to-refresh-indicator {
    animation: spin 1s linear infinite;
}

/* Mobile navigation enhancements */
.mobile-nav {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: white;
    border-top: 1px solid #e5e7eb;
    padding: 8px 0;
    z-index: 50;
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
}

.mobile-nav-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 8px 12px;
    text-decoration: none;
    color: #6b7280;
    transition: color 0.2s ease;
    min-height: 44px;
    justify-content: center;
}

.mobile-nav-item.active {
    color: #3b82f6;
}

.mobile-nav-item svg {
    width: 24px;
    height: 24px;
    margin-bottom: 4px;
}

.mobile-nav-item span {
    font-size: 12px;
    font-weight: 500;
}

/* Floating action button */
.fab {
    position: fixed;
    bottom: 80px;
    right: 20px;
    width: 56px;
    height: 56px;
    background: #3b82f6;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
    transition: all 0.3s ease;
    z-index: 40;
    border: none;
    cursor: pointer;
}

.fab:hover {
    transform: scale(1.1);
    box-shadow: 0 6px 16px rgba(59, 130, 246, 0.5);
}

.fab:active {
    transform: scale(0.95);
}

/* Mobile-optimized modals */
.mobile-modal {
    position: fixed;
    inset: 0;
    z-index: 50;
    display: flex;
    align-items: flex-end;
    justify-content: center;
    padding: 0;
}

.mobile-modal-content {
    background: white;
    border-radius: 16px 16px 0 0;
    width: 100%;
    max-height: 90vh;
    overflow-y: auto;
    transform: translateY(100%);
    transition: transform 0.3s ease;
}

.mobile-modal.show .mobile-modal-content {
    transform: translateY(0);
}

.mobile-modal-header {
    position: sticky;
    top: 0;
    background: white;
    padding: 16px 20px;
    border-bottom: 1px solid #e5e7eb;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.mobile-modal-handle {
    width: 40px;
    height: 4px;
    background: #d1d5db;
    border-radius: 2px;
    margin: 8px auto 16px;
}

/* Touch feedback */
.touch-feedback {
    position: relative;
    overflow: hidden;
}

.touch-feedback::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.3);
    transform: translate(-50%, -50%);
    transition: width 0.3s ease, height 0.3s ease;
    pointer-events: none;
}

.touch-feedback:active::before {
    width: 200px;
    height: 200px;
}

/* Haptic feedback class */
.haptic-light {
    /* Will be handled by JavaScript */
}

.haptic-medium {
    /* Will be handled by JavaScript */
}

.haptic-heavy {
    /* Will be handled by JavaScript */
}

/* Mobile-optimized cards */
.mobile-card {
    background: white;
    border-radius: 12px;
    padding: 16px;
    margin-bottom: 16px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.mobile-card:active {
    transform: scale(0.98);
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
}

/* Sticky headers */
.sticky-header {
    position: sticky;
    top: 0;
    background: white;
    z-index: 30;
    border-bottom: 1px solid #e5e7eb;
    padding: 16px 20px;
}

/* Mobile-optimized spacing */
.mobile-container {
    padding: 16px;
    padding-bottom: 100px; /* Account for mobile nav */
}

/* Responsive text sizes */
@media (max-width: 640px) {
    .text-responsive-xl {
        font-size: 1.5rem;
        line-height: 2rem;
    }
    
    .text-responsive-lg {
        font-size: 1.25rem;
        line-height: 1.75rem;
    }
    
    .text-responsive-base {
        font-size: 1rem;
        line-height: 1.5rem;
    }
    
    .text-responsive-sm {
        font-size: 0.875rem;
        line-height: 1.25rem;
    }
}

/* Loading states */
.loading-skeleton {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% {
        background-position: 200% 0;
    }
    100% {
        background-position: -200% 0;
    }
}

/* Safe area handling for notched devices */
.safe-area-top {
    padding-top: env(safe-area-inset-top);
}

.safe-area-bottom {
    padding-bottom: env(safe-area-inset-bottom);
}

.safe-area-left {
    padding-left: env(safe-area-inset-left);
}

.safe-area-right {
    padding-right: env(safe-area-inset-right);
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    .mobile-nav {
        background: #1f2937;
        border-top-color: #374151;
    }
    
    .mobile-modal-content {
        background: #1f2937;
        color: white;
    }
    
    .mobile-modal-header {
        background: #1f2937;
        border-bottom-color: #374151;
    }
    
    .mobile-card {
        background: #1f2937;
        color: white;
    }
    
    .sticky-header {
        background: #1f2937;
        border-bottom-color: #374151;
        color: white;
    }
}

/* Animations */
@keyframes spin {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideUp {
    from {
        transform: translateY(100%);
    }
    to {
        transform: translateY(0);
    }
}

/* Utility classes */
.no-scroll {
    overflow: hidden;
}

.touch-none {
    touch-action: none;
}

.touch-pan-x {
    touch-action: pan-x;
}

.touch-pan-y {
    touch-action: pan-y;
}

.user-select-none {
    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
}
