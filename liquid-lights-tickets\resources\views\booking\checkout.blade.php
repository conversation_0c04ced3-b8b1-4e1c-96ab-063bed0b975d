@extends('public.layout')

@section('title', 'Checkout - Liquid Lights Tickets')

@section('content')
<div x-data="checkout" class="min-h-screen bg-gray-50 py-12">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Page Header -->
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-gray-900">Checkout</h1>
            <p class="mt-2 text-gray-600">Complete your booking</p>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Checkout Form -->
            <div class="lg:col-span-2">
                <form @submit.prevent="processBooking()" class="space-y-8">
                    <!-- Customer Information -->
                    <div class="bg-white rounded-lg shadow-md p-6">
                        <h2 class="text-lg font-semibold text-gray-900 mb-6">Customer Information</h2>
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label for="name" class="block text-sm font-medium text-gray-700 mb-2">Full Name</label>
                                <input type="text" 
                                       id="name" 
                                       x-model="customerInfo.name"
                                       value="{{ auth()->user()->name ?? '' }}"
                                       class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                       required>
                            </div>
                            
                            <div>
                                <label for="email" class="block text-sm font-medium text-gray-700 mb-2">Email Address</label>
                                <input type="email" 
                                       id="email" 
                                       x-model="customerInfo.email"
                                       value="{{ auth()->user()->email ?? '' }}"
                                       class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                       required>
                            </div>
                            
                            <div>
                                <label for="phone" class="block text-sm font-medium text-gray-700 mb-2">Phone Number</label>
                                <input type="tel" 
                                       id="phone" 
                                       x-model="customerInfo.phone"
                                       value="{{ auth()->user()->phone ?? '' }}"
                                       placeholder="+919876543210"
                                       class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                       required>
                            </div>
                        </div>
                    </div>

                    <!-- Payment Method -->
                    <div class="bg-white rounded-lg shadow-md p-6">
                        <h2 class="text-lg font-semibold text-gray-900 mb-6">Payment Method</h2>
                        
                        <div class="space-y-4">
                            <div class="flex items-center">
                                <input id="razorpay" 
                                       name="payment_method" 
                                       type="radio" 
                                       value="razorpay"
                                       x-model="paymentMethod"
                                       class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300">
                                <label for="razorpay" class="ml-3 flex items-center">
                                    <span class="text-sm font-medium text-gray-900">Credit/Debit Card, UPI, Net Banking</span>
                                    <span class="ml-2 text-xs text-gray-500">(Powered by Razorpay)</span>
                                </label>
                            </div>
                            
                            <div class="flex items-center">
                                <input id="wallet" 
                                       name="payment_method" 
                                       type="radio" 
                                       value="wallet"
                                       x-model="paymentMethod"
                                       class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300">
                                <label for="wallet" class="ml-3">
                                    <span class="text-sm font-medium text-gray-900">Wallet</span>
                                    <span class="ml-2 text-xs text-gray-500">(Balance: ₹0)</span>
                                </label>
                            </div>
                        </div>
                    </div>

                    <!-- Terms and Conditions -->
                    <div class="bg-white rounded-lg shadow-md p-6">
                        <div class="flex items-start">
                            <input id="terms" 
                                   type="checkbox" 
                                   x-model="acceptTerms"
                                   class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded mt-1">
                            <label for="terms" class="ml-3 text-sm text-gray-700">
                                I agree to the 
                                <a href="#" class="text-blue-600 hover:text-blue-500">Terms and Conditions</a>, 
                                <a href="#" class="text-blue-600 hover:text-blue-500">Privacy Policy</a>, and 
                                <a href="#" class="text-blue-600 hover:text-blue-500">Cancellation Policy</a>
                            </label>
                        </div>
                    </div>

                    <!-- Submit Button -->
                    <button type="submit" 
                            :disabled="!canProceed || processing"
                            class="w-full bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed text-white py-3 px-4 rounded-lg font-semibold transition-colors">
                        <span x-show="!processing">Complete Booking</span>
                        <span x-show="processing" class="flex items-center justify-center">
                            <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" fill="none" viewBox="0 0 24 24">
                                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                            Processing...
                        </span>
                    </button>
                </form>
            </div>

            <!-- Order Summary -->
            <div class="lg:col-span-1">
                <div class="bg-white rounded-lg shadow-md p-6 sticky top-24">
                    <h2 class="text-lg font-semibold text-gray-900 mb-6">Order Summary</h2>
                    
                    <div class="space-y-4 mb-6">
                        <template x-for="item in cartItems" :key="`${item.eventId}-${item.ticketTypeId}`">
                            <div class="flex justify-between items-start">
                                <div class="flex-1">
                                    <h3 class="text-sm font-medium text-gray-900" x-text="item.eventTitle"></h3>
                                    <p class="text-xs text-gray-600" x-text="item.ticketTypeName"></p>
                                    <p class="text-xs text-gray-500" x-text="`Qty: ${item.quantity}`"></p>
                                </div>
                                <div class="text-sm font-medium text-gray-900" x-text="`₹${item.total.toLocaleString()}`"></div>
                            </div>
                        </template>
                    </div>
                    
                    <div class="border-t border-gray-200 pt-4 space-y-2">
                        <div class="flex justify-between text-sm">
                            <span class="text-gray-600">Subtotal:</span>
                            <span class="font-medium" x-text="`₹${totalAmount.toLocaleString()}`"></span>
                        </div>
                        
                        <div class="flex justify-between text-sm">
                            <span class="text-gray-600">Service Fee:</span>
                            <span class="font-medium">₹0</span>
                        </div>
                        
                        <div class="flex justify-between text-sm">
                            <span class="text-gray-600">Taxes:</span>
                            <span class="font-medium">₹0</span>
                        </div>
                        
                        <div class="border-t border-gray-200 pt-2">
                            <div class="flex justify-between text-lg font-bold">
                                <span>Total:</span>
                                <span x-text="`₹${totalAmount.toLocaleString()}`"></span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mt-6 text-xs text-gray-500">
                        <p>By completing this purchase, you agree to our terms and conditions.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
Alpine.data('checkout', () => ({
    cartItems: JSON.parse(localStorage.getItem('cart') || '[]'),
    customerInfo: {
        name: '{{ auth()->user()->name ?? '' }}',
        email: '{{ auth()->user()->email ?? '' }}',
        phone: '{{ auth()->user()->phone ?? '' }}'
    },
    paymentMethod: 'razorpay',
    acceptTerms: false,
    processing: false,
    
    get totalAmount() {
        return this.cartItems.reduce((sum, item) => sum + item.total, 0);
    },
    
    get canProceed() {
        return this.cartItems.length > 0 && 
               this.customerInfo.name && 
               this.customerInfo.email && 
               this.customerInfo.phone && 
               this.paymentMethod && 
               this.acceptTerms;
    },
    
    async processBooking() {
        if (!this.canProceed || this.processing) return;
        
        this.processing = true;
        
        try {
            const response = await fetch('/api/booking/process', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: JSON.stringify({
                    items: this.cartItems.map(item => ({
                        event_id: item.eventId,
                        ticket_type_id: item.ticketTypeId,
                        quantity: item.quantity
                    })),
                    payment_method: this.paymentMethod,
                    customer_info: this.customerInfo
                })
            });
            
            const data = await response.json();
            
            if (data.success) {
                // Clear cart
                localStorage.removeItem('cart');
                
                // Redirect to confirmation
                window.location.href = data.redirect_url || '/user/dashboard';
            } else {
                alert(data.message || 'Booking failed. Please try again.');
            }
        } catch (error) {
            console.error('Booking error:', error);
            alert('An error occurred. Please try again.');
        } finally {
            this.processing = false;
        }
    }
}));
</script>
@endpush
@endsection
