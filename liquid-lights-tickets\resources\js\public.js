import './bootstrap';
import Alpine from 'alpinejs';

// Cart Management
Alpine.data('cart', () => ({
    items: [],
    isOpen: false,
    loading: false,
    
    init() {
        this.loadCart();
    },
    
    loadCart() {
        const savedCart = localStorage.getItem('cart');
        if (savedCart) {
            this.items = JSON.parse(savedCart);
        }
    },
    
    saveCart() {
        localStorage.setItem('cart', JSON.stringify(this.items));
    },
    
    addItem(eventId, ticketTypeId, quantity = 1, price, eventTitle, ticketTypeName) {
        const existingItem = this.items.find(item => 
            item.eventId === eventId && item.ticketTypeId === ticketTypeId
        );
        
        if (existingItem) {
            existingItem.quantity += quantity;
        } else {
            this.items.push({
                eventId,
                ticketTypeId,
                quantity,
                price,
                eventTitle,
                ticketTypeName,
                total: price * quantity
            });
        }
        
        this.updateTotals();
        this.saveCart();
        this.showNotification('Item added to cart!');
    },
    
    removeItem(eventId, ticketTypeId) {
        this.items = this.items.filter(item => 
            !(item.eventId === eventId && item.ticketTypeId === ticketTypeId)
        );
        this.updateTotals();
        this.saveCart();
    },
    
    updateQuantity(eventId, ticketTypeId, quantity) {
        const item = this.items.find(item => 
            item.eventId === eventId && item.ticketTypeId === ticketTypeId
        );
        
        if (item) {
            item.quantity = Math.max(1, quantity);
            item.total = item.price * item.quantity;
            this.updateTotals();
            this.saveCart();
        }
    },
    
    updateTotals() {
        this.items.forEach(item => {
            item.total = item.price * item.quantity;
        });
    },
    
    get totalItems() {
        return this.items.reduce((sum, item) => sum + item.quantity, 0);
    },
    
    get totalAmount() {
        return this.items.reduce((sum, item) => sum + item.total, 0);
    },
    
    clearCart() {
        this.items = [];
        this.saveCart();
    },
    
    showNotification(message) {
        // Simple notification - can be enhanced with a proper notification system
        const notification = document.createElement('div');
        notification.className = 'fixed top-4 right-4 bg-green-500 text-white px-4 py-2 rounded-lg shadow-lg z-50';
        notification.textContent = message;
        document.body.appendChild(notification);
        
        setTimeout(() => {
            notification.remove();
        }, 3000);
    }
}));

// Event Filtering
Alpine.data('eventFilters', () => ({
    filters: {
        search: '',
        category: '',
        date_from: '',
        date_to: '',
        price_min: '',
        price_max: '',
        sort: 'date'
    },
    
    init() {
        // Load filters from URL parameters
        const urlParams = new URLSearchParams(window.location.search);
        Object.keys(this.filters).forEach(key => {
            if (urlParams.has(key)) {
                this.filters[key] = urlParams.get(key);
            }
        });
    },
    
    applyFilters() {
        const params = new URLSearchParams();
        
        Object.keys(this.filters).forEach(key => {
            if (this.filters[key]) {
                params.set(key, this.filters[key]);
            }
        });
        
        window.location.href = `${window.location.pathname}?${params.toString()}`;
    },
    
    clearFilters() {
        Object.keys(this.filters).forEach(key => {
            this.filters[key] = '';
        });
        window.location.href = window.location.pathname;
    }
}));

// Ticket Selection
Alpine.data('ticketSelector', (eventId, ticketTypes) => ({
    eventId,
    ticketTypes,
    selectedTickets: {},
    loading: false,
    
    init() {
        // Initialize selected tickets object
        this.ticketTypes.forEach(ticketType => {
            this.selectedTickets[ticketType.id] = 0;
        });
    },
    
    updateQuantity(ticketTypeId, quantity) {
        this.selectedTickets[ticketTypeId] = Math.max(0, Math.min(quantity, this.getAvailableQuantity(ticketTypeId)));
    },
    
    getAvailableQuantity(ticketTypeId) {
        const ticketType = this.ticketTypes.find(t => t.id === ticketTypeId);
        return ticketType ? ticketType.quantity_available - ticketType.quantity_sold : 0;
    },
    
    get totalTickets() {
        return Object.values(this.selectedTickets).reduce((sum, qty) => sum + qty, 0);
    },
    
    get totalAmount() {
        return Object.entries(this.selectedTickets).reduce((sum, [ticketTypeId, quantity]) => {
            const ticketType = this.ticketTypes.find(t => t.id == ticketTypeId);
            return sum + (ticketType ? ticketType.price * quantity : 0);
        }, 0);
    },
    
    addToCart() {
        const cart = Alpine.store('cart');
        
        Object.entries(this.selectedTickets).forEach(([ticketTypeId, quantity]) => {
            if (quantity > 0) {
                const ticketType = this.ticketTypes.find(t => t.id == ticketTypeId);
                if (ticketType) {
                    cart.addItem(
                        this.eventId,
                        parseInt(ticketTypeId),
                        quantity,
                        ticketType.price,
                        ticketType.event?.title || 'Event',
                        ticketType.name
                    );
                }
            }
        });
        
        // Reset selections
        Object.keys(this.selectedTickets).forEach(key => {
            this.selectedTickets[key] = 0;
        });
    }
}));

// Image Gallery
Alpine.data('imageGallery', (images) => ({
    images,
    currentIndex: 0,
    isOpen: false,
    
    openGallery(index = 0) {
        this.currentIndex = index;
        this.isOpen = true;
        document.body.style.overflow = 'hidden';
    },
    
    closeGallery() {
        this.isOpen = false;
        document.body.style.overflow = 'auto';
    },
    
    nextImage() {
        this.currentIndex = (this.currentIndex + 1) % this.images.length;
    },
    
    prevImage() {
        this.currentIndex = this.currentIndex === 0 ? this.images.length - 1 : this.currentIndex - 1;
    },
    
    get currentImage() {
        return this.images[this.currentIndex];
    }
}));

// Search Functionality
Alpine.data('search', () => ({
    query: '',
    results: [],
    loading: false,
    showResults: false,
    
    async performSearch() {
        if (this.query.length < 2) {
            this.results = [];
            this.showResults = false;
            return;
        }
        
        this.loading = true;
        
        try {
            const response = await fetch(`/api/search?q=${encodeURIComponent(this.query)}`);
            const data = await response.json();
            
            if (data.success) {
                this.results = data.data;
                this.showResults = true;
            }
        } catch (error) {
            console.error('Search error:', error);
        } finally {
            this.loading = false;
        }
    },
    
    selectResult(result) {
        window.location.href = `/events/${result.id}`;
    },
    
    hideResults() {
        setTimeout(() => {
            this.showResults = false;
        }, 200);
    }
}));

// Form Validation
Alpine.data('formValidator', (rules) => ({
    errors: {},
    
    validate(field, value) {
        const fieldRules = rules[field];
        if (!fieldRules) return true;
        
        this.errors[field] = [];
        
        fieldRules.forEach(rule => {
            if (rule.required && (!value || value.trim() === '')) {
                this.errors[field].push(rule.message || `${field} is required`);
            }
            
            if (rule.email && value && !this.isValidEmail(value)) {
                this.errors[field].push(rule.message || 'Please enter a valid email address');
            }
            
            if (rule.min && value && value.length < rule.min) {
                this.errors[field].push(rule.message || `${field} must be at least ${rule.min} characters`);
            }
            
            if (rule.max && value && value.length > rule.max) {
                this.errors[field].push(rule.message || `${field} must not exceed ${rule.max} characters`);
            }
        });
        
        return this.errors[field].length === 0;
    },
    
    isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    },
    
    hasError(field) {
        return this.errors[field] && this.errors[field].length > 0;
    },
    
    getError(field) {
        return this.hasError(field) ? this.errors[field][0] : '';
    }
}));

// Utility Functions
window.formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-IN', {
        style: 'currency',
        currency: 'INR'
    }).format(amount);
};

window.formatDate = (date) => {
    return new Date(date).toLocaleDateString('en-IN', {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    });
};

window.formatTime = (time) => {
    return new Date(`2000-01-01T${time}`).toLocaleTimeString('en-IN', {
        hour: '2-digit',
        minute: '2-digit',
        hour12: true
    });
};

// Start Alpine
window.Alpine = Alpine;
Alpine.start();
