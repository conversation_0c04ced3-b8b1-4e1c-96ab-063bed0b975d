<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Booking extends Model
{
    use HasFactory;

    protected $fillable = [
        'booking_reference',
        'user_id',
        'event_id',
        'ticket_type_id',
        'quantity',
        'total_amount',
        'payment_status',
        'payment_id',
        'payment_method',
        'qr_code_hash',
        'is_checked_in',
        'checked_in_at',
        'checked_in_by',
        'ticket_pdf_path',
        'refund_amount',
        'refund_reason',
        'refunded_at',
        'reminder_sent'
    ];

    protected function casts(): array
    {
        return [
            'total_amount' => 'decimal:2',
            'is_checked_in' => 'boolean',
            'checked_in_at' => 'datetime',
        ];
    }

    /**
     * Relationships
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function event()
    {
        return $this->belongsTo(Event::class);
    }

    public function ticketType()
    {
        return $this->belongsTo(TicketType::class);
    }

    public function checkedInBy()
    {
        return $this->belongsTo(User::class, 'checked_in_by');
    }

    /**
     * Scopes
     */
    public function scopePaid($query)
    {
        return $query->where('payment_status', 'paid');
    }

    public function scopeCheckedIn($query)
    {
        return $query->where('is_checked_in', true);
    }

    /**
     * Accessors
     */
    public function getIsPaidAttribute()
    {
        return $this->payment_status === 'paid';
    }
}
