<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Storage;
use App\Models\Booking;
use App\Models\Event;
use App\Services\TicketService;
use Carbon\Carbon;

class UserDashboardController extends Controller
{
    protected $ticketService;

    public function __construct(TicketService $ticketService)
    {
        $this->ticketService = $ticketService;
    }

    /**
     * Show user dashboard
     */
    public function dashboard()
    {
        $user = Auth::user();

        // Get user statistics
        $stats = [
            'total_bookings' => $user->bookings()->count(),
            'upcoming_events' => $user->bookings()
                ->whereHas('event', function($query) {
                    $query->where('event_date', '>=', Carbon::now());
                })
                ->where('payment_status', 'paid')
                ->count(),
            'total_spent' => $user->bookings()
                ->where('payment_status', 'paid')
                ->sum('total_amount'),
            'pending_bookings' => $user->bookings()
                ->where('payment_status', 'pending')
                ->count(),
        ];

        // Get recent bookings
        $recentBookings = $user->bookings()
            ->with(['event', 'ticketType'])
            ->orderBy('created_at', 'desc')
            ->limit(5)
            ->get();

        // Get upcoming events
        $upcomingEvents = $user->bookings()
            ->with(['event', 'ticketType'])
            ->whereHas('event', function($query) {
                $query->where('event_date', '>=', Carbon::now());
            })
            ->where('payment_status', 'paid')
            ->orderBy('created_at', 'desc')
            ->limit(3)
            ->get();

        return view('user.dashboard', compact('stats', 'recentBookings', 'upcomingEvents'));
    }

    /**
     * Show user bookings
     */
    public function bookings(Request $request)
    {
        $user = Auth::user();

        $query = $user->bookings()->with(['event', 'ticketType']);

        // Apply filters
        if ($request->has('status')) {
            $query->where('payment_status', $request->status);
        }

        if ($request->has('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('booking_reference', 'like', "%{$search}%")
                  ->orWhereHas('event', function($eventQuery) use ($search) {
                      $eventQuery->where('title', 'like', "%{$search}%");
                  });
            });
        }

        // Sorting
        $sortBy = $request->get('sort', 'created_at');
        $sortOrder = $request->get('order', 'desc');
        $query->orderBy($sortBy, $sortOrder);

        $bookings = $query->paginate(10);

        return view('user.bookings', compact('bookings'));
    }

    /**
     * Show single booking
     */
    public function booking($id)
    {
        $user = Auth::user();
        $booking = $user->bookings()
            ->with(['event', 'ticketType', 'checkedInBy'])
            ->findOrFail($id);

        return view('user.booking-detail', compact('booking'));
    }

    /**
     * Show user profile
     */
    public function profile()
    {
        $user = Auth::user();
        return view('user.profile', compact('user'));
    }

    /**
     * Update user profile
     */
    public function updateProfile(Request $request)
    {
        $user = Auth::user();

        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'email' => 'required|email|unique:users,email,' . $user->id,
            'phone' => 'nullable|string|regex:/^\+[1-9]\d{1,14}$/|unique:users,phone,' . $user->id,
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        $user->update($request->only(['name', 'email', 'phone']));

        return back()->with('success', 'Profile updated successfully!');
    }

    /**
     * Update password
     */
    public function updatePassword(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'current_password' => 'required',
            'password' => 'required|string|min:8|confirmed',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator);
        }

        $user = Auth::user();

        if (!Hash::check($request->current_password, $user->password)) {
            return back()->withErrors(['current_password' => 'Current password is incorrect.']);
        }

        $user->update([
            'password' => Hash::make($request->password)
        ]);

        return back()->with('success', 'Password updated successfully!');
    }

    /**
     * Download ticket
     */
    public function downloadTicket($bookingId)
    {
        $user = Auth::user();
        $booking = $user->bookings()
            ->with(['event', 'ticketType'])
            ->where('payment_status', 'paid')
            ->findOrFail($bookingId);

        try {
            // Get or generate ticket download URL
            $downloadUrl = $this->ticketService->getTicketDownloadUrl($booking);

            if (!$downloadUrl) {
                return response()->json([
                    'success' => false,
                    'message' => 'Ticket not available for download'
                ], 404);
            }

            // Return the file for download
            $filePath = str_replace(Storage::disk('public')->url(''), '', $downloadUrl);

            if (!Storage::disk('public')->exists($filePath)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Ticket file not found'
                ], 404);
            }

            return Storage::disk('public')->download($filePath, "ticket_{$booking->booking_reference}.pdf");

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to download ticket: ' . $e->getMessage()
            ], 500);
        }
    }
}
