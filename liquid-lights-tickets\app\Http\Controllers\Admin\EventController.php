<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use App\Models\Event;
use App\Models\Artist;
use App\Models\TicketType;

class EventController extends Controller
{
    /**
     * Display a listing of events with filters
     */
    public function index(Request $request)
    {
        $query = Event::with(['creator', 'ticketTypes', 'artists']);

        // Apply filters
        if ($request->has('status')) {
            $query->where('status', $request->status);
        }

        if ($request->has('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhere('venue_name', 'like', "%{$search}%");
            });
        }

        if ($request->has('date_from')) {
            $query->where('event_date', '>=', $request->date_from);
        }

        if ($request->has('date_to')) {
            $query->where('event_date', '<=', $request->date_to);
        }

        // Sorting
        $sortBy = $request->get('sort_by', 'created_at');
        $sortOrder = $request->get('sort_order', 'desc');
        $query->orderBy($sortBy, $sortOrder);

        // Pagination
        $perPage = $request->get('per_page', 15);
        $events = $query->paginate($perPage);

        // Add booking statistics
        $events->getCollection()->transform(function ($event) {
            $totalTickets = $event->ticketTypes->sum('quantity_available');
            $soldTickets = $event->bookings()->where('payment_status', 'paid')->sum('quantity');

            $event->total_tickets = $totalTickets;
            $event->sold_tickets = $soldTickets;
            $event->available_tickets = $totalTickets - $soldTickets;
            $event->sales_percentage = $totalTickets > 0 ? round(($soldTickets / $totalTickets) * 100, 1) : 0;

            return $event;
        });

        return response()->json([
            'success' => true,
            'data' => $events
        ]);
    }

    /**
     * Store a newly created event
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'title' => 'required|string|max:255',
            'description' => 'required|string',
            'short_description' => 'required|string|max:500',
            'venue_name' => 'required|string|max:255',
            'venue_address' => 'required|string',
            'event_date' => 'required|date|after:today',
            'event_time' => 'required|date_format:H:i',
            'banner_image' => 'nullable|string',
            'gallery_images' => 'nullable|array',
            'meta_title' => 'nullable|string|max:255',
            'meta_description' => 'nullable|string|max:500',
            'is_featured' => 'boolean',
            'artist_ids' => 'nullable|array',
            'artist_ids.*' => 'exists:artists,id',
            'ticket_types' => 'required|array|min:1',
            'ticket_types.*.name' => 'required|string|max:255',
            'ticket_types.*.price' => 'required|numeric|min:0',
            'ticket_types.*.quantity_available' => 'required|integer|min:1',
            'ticket_types.*.sale_start_date' => 'required|date',
            'ticket_types.*.sale_end_date' => 'required|date|after:ticket_types.*.sale_start_date',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        // Create event
        $event = Event::create([
            'title' => $request->title,
            'slug' => Str::slug($request->title),
            'description' => $request->description,
            'short_description' => $request->short_description,
            'venue_name' => $request->venue_name,
            'venue_address' => $request->venue_address,
            'event_date' => $request->event_date,
            'event_time' => $request->event_time,
            'banner_image' => $request->banner_image,
            'gallery_images' => $request->gallery_images,
            'meta_title' => $request->meta_title ?: $request->title,
            'meta_description' => $request->meta_description ?: $request->short_description,
            'is_featured' => $request->boolean('is_featured'),
            'status' => 'draft',
            'created_by' => $request->user()->id,
        ]);

        // Create ticket types
        foreach ($request->ticket_types as $ticketTypeData) {
            $event->ticketTypes()->create($ticketTypeData);
        }

        // Attach artists if provided
        if ($request->has('artist_ids')) {
            $event->artists()->attach($request->artist_ids);
        }

        return response()->json([
            'success' => true,
            'message' => 'Event created successfully',
            'data' => $event->load(['ticketTypes', 'artists'])
        ], 201);
    }

    /**
     * Display the specified event
     */
    public function show($id)
    {
        $event = Event::with(['creator', 'ticketTypes', 'artists', 'bookings'])
            ->findOrFail($id);

        // Add booking statistics
        $totalTickets = $event->ticketTypes->sum('quantity_available');
        $soldTickets = $event->bookings()->where('payment_status', 'paid')->sum('quantity');

        $event->total_tickets = $totalTickets;
        $event->sold_tickets = $soldTickets;
        $event->available_tickets = $totalTickets - $soldTickets;
        $event->sales_percentage = $totalTickets > 0 ? round(($soldTickets / $totalTickets) * 100, 1) : 0;

        return response()->json([
            'success' => true,
            'data' => $event
        ]);
    }

    /**
     * Update the specified event
     */
    public function update(Request $request, $id)
    {
        $event = Event::findOrFail($id);

        $validator = Validator::make($request->all(), [
            'title' => 'sometimes|string|max:255',
            'description' => 'sometimes|string',
            'short_description' => 'sometimes|string|max:500',
            'venue_name' => 'sometimes|string|max:255',
            'venue_address' => 'sometimes|string',
            'event_date' => 'sometimes|date',
            'event_time' => 'sometimes|date_format:H:i',
            'banner_image' => 'nullable|string',
            'gallery_images' => 'nullable|array',
            'meta_title' => 'nullable|string|max:255',
            'meta_description' => 'nullable|string|max:500',
            'is_featured' => 'boolean',
            'status' => 'sometimes|in:draft,published,cancelled',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $updateData = $request->only([
            'title', 'description', 'short_description', 'venue_name', 'venue_address',
            'event_date', 'event_time', 'banner_image', 'gallery_images',
            'meta_title', 'meta_description', 'is_featured', 'status'
        ]);

        if (isset($updateData['title'])) {
            $updateData['slug'] = Str::slug($updateData['title']);
        }

        $event->update($updateData);

        return response()->json([
            'success' => true,
            'message' => 'Event updated successfully',
            'data' => $event->load(['ticketTypes', 'artists'])
        ]);
    }

    /**
     * Remove the specified event
     */
    public function destroy($id)
    {
        $event = Event::findOrFail($id);

        // Check if event has bookings
        if ($event->bookings()->where('payment_status', 'paid')->exists()) {
            return response()->json([
                'success' => false,
                'message' => 'Cannot delete event with paid bookings'
            ], 400);
        }

        $event->delete();

        return response()->json([
            'success' => true,
            'message' => 'Event deleted successfully'
        ]);
    }

    /**
     * Publish an event
     */
    public function publish($id)
    {
        $event = Event::findOrFail($id);

        $event->update(['status' => 'published']);

        return response()->json([
            'success' => true,
            'message' => 'Event published successfully',
            'data' => $event
        ]);
    }

    /**
     * Cancel an event
     */
    public function cancel($id)
    {
        $event = Event::findOrFail($id);

        $event->update(['status' => 'cancelled']);

        return response()->json([
            'success' => true,
            'message' => 'Event cancelled successfully',
            'data' => $event
        ]);
    }
}
