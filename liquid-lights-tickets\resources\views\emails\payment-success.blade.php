<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Payment Successful</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f4f4f4;
        }
        .container {
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            border-bottom: 2px solid #10B981;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        .logo {
            font-size: 24px;
            font-weight: bold;
            color: #3B82F6;
            margin-bottom: 10px;
        }
        .success-icon {
            width: 60px;
            height: 60px;
            background-color: #10B981;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
        }
        .title {
            color: #10B981;
            font-size: 28px;
            margin-bottom: 10px;
        }
        .subtitle {
            color: #666;
            font-size: 16px;
        }
        .payment-summary {
            background: linear-gradient(135deg, #10B981 0%, #059669 100%);
            color: white;
            padding: 25px;
            border-radius: 8px;
            margin: 20px 0;
            text-align: center;
        }
        .amount {
            font-size: 36px;
            font-weight: bold;
            margin-bottom: 10px;
        }
        .payment-method {
            opacity: 0.9;
            font-size: 14px;
        }
        .booking-details {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .detail-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            padding: 8px 0;
            border-bottom: 1px solid #e9ecef;
        }
        .detail-row:last-child {
            border-bottom: none;
        }
        .detail-label {
            font-weight: bold;
            color: #555;
        }
        .detail-value {
            color: #333;
        }
        .event-info {
            background-color: #f1f5f9;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .event-title {
            font-size: 20px;
            font-weight: bold;
            color: #1a1a1a;
            margin-bottom: 15px;
        }
        .event-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
        }
        .event-detail {
            display: flex;
            align-items: center;
            color: #555;
        }
        .event-detail svg {
            width: 16px;
            height: 16px;
            margin-right: 8px;
            color: #3B82F6;
        }
        .next-steps {
            background-color: #eff6ff;
            border: 1px solid #3B82F6;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .next-steps h3 {
            color: #1e40af;
            margin-top: 0;
            margin-bottom: 15px;
        }
        .next-steps ol {
            margin: 0;
            padding-left: 20px;
            color: #1e40af;
        }
        .next-steps li {
            margin-bottom: 8px;
        }
        .cta-buttons {
            text-align: center;
            margin: 30px 0;
        }
        .cta-button {
            display: inline-block;
            background-color: #3B82F6;
            color: white;
            padding: 12px 24px;
            text-decoration: none;
            border-radius: 6px;
            font-weight: bold;
            margin: 0 10px;
        }
        .cta-button.secondary {
            background-color: #10B981;
        }
        .footer {
            text-align: center;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #e9ecef;
            color: #666;
            font-size: 14px;
        }
        @media (max-width: 600px) {
            .event-details {
                grid-template-columns: 1fr;
            }
            .detail-row {
                flex-direction: column;
            }
            .detail-label {
                margin-bottom: 5px;
            }
            .cta-button {
                display: block;
                margin: 10px 0;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <div class="logo">🎫 LIQUID LIGHTS</div>
            <div class="success-icon">
                <svg width="30" height="30" fill="white" viewBox="0 0 24 24">
                    <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"/>
                </svg>
            </div>
            <h1 class="title">Payment Successful!</h1>
            <p class="subtitle">Your tickets are confirmed, {{ $user->name }}</p>
        </div>

        <!-- Payment Summary -->
        <div class="payment-summary">
            <div class="amount">₹{{ number_format($booking->total_amount, 2) }}</div>
            <div class="payment-method">Payment completed successfully</div>
        </div>

        <!-- Event Information -->
        <div class="event-info">
            <div class="event-title">{{ $event->title }}</div>
            <div class="event-details">
                <div class="event-detail">
                    <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                    </svg>
                    {{ \Carbon\Carbon::parse($event->event_date)->format('l, F d, Y') }}
                </div>
                <div class="event-detail">
                    <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    {{ \Carbon\Carbon::parse($event->event_time)->format('g:i A') }}
                </div>
                <div class="event-detail">
                    <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                    </svg>
                    {{ $event->venue_name }}
                </div>
                <div class="event-detail">
                    <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                    </svg>
                    {{ $booking->booking_reference }}
                </div>
            </div>
        </div>

        <!-- Booking Details -->
        <div class="booking-details">
            <h3 style="margin-top: 0; color: #1a1a1a;">Payment Details</h3>
            
            <div class="detail-row">
                <span class="detail-label">Ticket Type:</span>
                <span class="detail-value">{{ $ticketType->name }}</span>
            </div>
            
            <div class="detail-row">
                <span class="detail-label">Quantity:</span>
                <span class="detail-value">{{ $booking->quantity }} {{ Str::plural('ticket', $booking->quantity) }}</span>
            </div>
            
            <div class="detail-row">
                <span class="detail-label">Price per ticket:</span>
                <span class="detail-value">₹{{ number_format($ticketType->price, 2) }}</span>
            </div>
            
            <div class="detail-row">
                <span class="detail-label">Payment Method:</span>
                <span class="detail-value">{{ ucfirst($booking->payment_method) }}</span>
            </div>
            
            <div class="detail-row">
                <span class="detail-label">Transaction Date:</span>
                <span class="detail-value">{{ $booking->updated_at->format('F d, Y g:i A') }}</span>
            </div>
        </div>

        <!-- Next Steps -->
        <div class="next-steps">
            <h3>What's Next?</h3>
            <ol>
                <li>Your tickets are now available for download in your account</li>
                <li>You'll receive your tickets via email within the next few minutes</li>
                <li>Present the QR code on your ticket at the venue for entry</li>
                <li>Arrive 30 minutes early with a valid photo ID</li>
                <li>We'll send you a reminder 24 hours before the event</li>
            </ol>
        </div>

        <!-- Call to Action Buttons -->
        <div class="cta-buttons">
            <a href="{{ route('user.booking', $booking->id) }}" class="cta-button">
                View Booking
            </a>
            <a href="{{ route('user.booking.download', $booking->id) }}" class="cta-button secondary">
                Download Tickets
            </a>
        </div>

        <!-- Venue Information -->
        @if($event->venue_address)
        <div style="margin: 20px 0; padding: 15px; background-color: #f9fafb; border-radius: 8px;">
            <h3 style="margin-top: 0; color: #1a1a1a;">Venue Information</h3>
            <p style="margin: 0;"><strong>{{ $event->venue_name }}</strong></p>
            <p style="margin: 5px 0 0 0; color: #666;">{{ $event->venue_address }}</p>
        </div>
        @endif

        <!-- Footer -->
        <div class="footer">
            <p>
                Questions about your booking? Contact us at 
                <a href="mailto:<EMAIL>"><EMAIL></a> 
                or call +91-XXXXXXXXXX
            </p>
            
            <p style="margin-top: 20px; font-size: 12px; color: #999;">
                © {{ date('Y') }} Liquid Lights Tickets. All rights reserved.<br>
                This email was sent to {{ $user->email }}
            </p>
        </div>
    </div>
</body>
</html>
