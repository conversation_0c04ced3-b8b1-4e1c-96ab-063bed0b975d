<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\EmailNotificationService;

class SendEventReminders extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'reminders:send {--hours=24 : Hours before event to send reminder}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Send event reminder emails to customers';

    protected $emailService;

    public function __construct(EmailNotificationService $emailService)
    {
        parent::__construct();
        $this->emailService = $emailService;
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $hoursBeforeEvent = (int) $this->option('hours');

        $this->info("Sending event reminders for events in {$hoursBeforeEvent} hours...");

        $result = $this->emailService->sendBulkEventReminders($hoursBeforeEvent);

        if ($result['success']) {
            $this->info("✅ Reminder emails sent successfully!");
            $this->table(
                ['Metric', 'Count'],
                [
                    ['Events Processed', $result['events_processed']],
                    ['Emails Sent', $result['total_sent']],
                    ['Failed', $result['total_failed']]
                ]
            );
        } else {
            $this->error("❌ Failed to send reminder emails: " . $result['error']);
            return 1;
        }

        return 0;
    }
}
