<svg width="400" height="300" viewBox="0 0 400 300" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bg" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
    </linearGradient>
  </defs>
  <rect width="400" height="300" fill="url(#bg)"/>
  <circle cx="200" cy="120" r="40" fill="white" opacity="0.2"/>
  <circle cx="200" cy="120" r="25" fill="white" opacity="0.4"/>
  <path d="M180 110 L190 120 L180 130 M220 110 L210 120 L220 130 M190 100 L210 140" stroke="white" stroke-width="3" stroke-linecap="round" stroke-linejoin="round" opacity="0.8"/>
  <rect x="150" y="180" width="100" height="8" fill="white" opacity="0.6" rx="4"/>
  <rect x="170" y="200" width="60" height="6" fill="white" opacity="0.4" rx="3"/>
  <text x="200" y="240" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="14" opacity="0.8">Event Image</text>
</svg>
