@extends('public.layout')

@section('title', 'Shopping Cart - Liquid Lights Tickets')

@section('content')
<div x-data="cart" class="min-h-screen bg-gray-50 py-12">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- <PERSON> Header -->
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-gray-900">Shopping Cart</h1>
            <p class="mt-2 text-gray-600">Review your selected tickets before checkout</p>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Cart Items -->
            <div class="lg:col-span-2">
                <div x-show="items.length === 0" class="text-center py-16">
                    <svg class="mx-auto h-24 w-24 text-gray-400 mb-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-1.5 6M7 13l-1.5 6m0 0h9M17 13v6a2 2 0 01-2 2H9a2 2 0 01-2-2v-6" />
                    </svg>
                    <h3 class="text-2xl font-bold text-gray-900 mb-2">Your cart is empty</h3>
                    <p class="text-gray-600 mb-8">Looks like you haven't added any tickets to your cart yet.</p>
                    <a href="{{ route('public.events') }}" 
                       class="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700">
                        Browse Events
                        <svg class="ml-2 -mr-1 w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
                        </svg>
                    </a>
                </div>

                <div x-show="items.length > 0" class="space-y-6">
                    <template x-for="item in items" :key="`${item.eventId}-${item.ticketTypeId}`">
                        <div class="bg-white rounded-lg shadow-md p-6">
                            <div class="flex items-start justify-between">
                                <div class="flex-1">
                                    <h3 class="text-lg font-semibold text-gray-900" x-text="item.eventTitle"></h3>
                                    <p class="text-gray-600 mt-1" x-text="item.ticketTypeName"></p>
                                    <p class="text-2xl font-bold text-blue-600 mt-2" x-text="`₹${item.price.toLocaleString()}`"></p>
                                </div>
                                
                                <button @click="removeItem(item.eventId, item.ticketTypeId)" 
                                        class="text-gray-400 hover:text-red-500 transition-colors">
                                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                    </svg>
                                </button>
                            </div>
                            
                            <div class="flex items-center justify-between mt-6">
                                <div class="flex items-center space-x-3">
                                    <span class="text-sm text-gray-700">Quantity:</span>
                                    <div class="flex items-center space-x-2">
                                        <button @click="updateQuantity(item.eventId, item.ticketTypeId, item.quantity - 1)"
                                                :disabled="item.quantity <= 1"
                                                class="w-8 h-8 rounded-full border border-gray-300 flex items-center justify-center hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
                                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 12H4"></path>
                                            </svg>
                                        </button>
                                        <span class="w-8 text-center font-semibold" x-text="item.quantity"></span>
                                        <button @click="updateQuantity(item.eventId, item.ticketTypeId, item.quantity + 1)"
                                                class="w-8 h-8 rounded-full border border-gray-300 flex items-center justify-center hover:bg-gray-50">
                                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                            </svg>
                                        </button>
                                    </div>
                                </div>
                                
                                <div class="text-right">
                                    <p class="text-lg font-bold text-gray-900" x-text="`₹${item.total.toLocaleString()}`"></p>
                                    <p class="text-sm text-gray-500" x-text="`₹${item.price.toLocaleString()} × ${item.quantity}`"></p>
                                </div>
                            </div>
                        </div>
                    </template>
                </div>
            </div>

            <!-- Order Summary -->
            <div class="lg:col-span-1">
                <div x-show="items.length > 0" class="bg-white rounded-lg shadow-md p-6 sticky top-24">
                    <h2 class="text-lg font-semibold text-gray-900 mb-6">Order Summary</h2>
                    
                    <div class="space-y-4 mb-6">
                        <div class="flex justify-between text-sm">
                            <span class="text-gray-600">Total Items:</span>
                            <span class="font-medium" x-text="totalItems"></span>
                        </div>
                        
                        <div class="flex justify-between text-sm">
                            <span class="text-gray-600">Subtotal:</span>
                            <span class="font-medium" x-text="`₹${totalAmount.toLocaleString()}`"></span>
                        </div>
                        
                        <div class="flex justify-between text-sm">
                            <span class="text-gray-600">Service Fee:</span>
                            <span class="font-medium">₹0</span>
                        </div>
                        
                        <div class="border-t border-gray-200 pt-4">
                            <div class="flex justify-between text-lg font-bold">
                                <span>Total:</span>
                                <span x-text="`₹${totalAmount.toLocaleString()}`"></span>
                            </div>
                        </div>
                    </div>
                    
                    @auth
                    <a href="{{ route('booking.checkout') }}" 
                       class="w-full bg-blue-600 hover:bg-blue-700 text-white py-3 px-4 rounded-lg font-semibold text-center block transition-colors">
                        Proceed to Checkout
                    </a>
                    @else
                    <div class="space-y-3">
                        <p class="text-sm text-gray-600 text-center">Please sign in to continue</p>
                        <a href="{{ route('login') }}" 
                           class="w-full bg-blue-600 hover:bg-blue-700 text-white py-3 px-4 rounded-lg font-semibold text-center block transition-colors">
                            Sign In to Checkout
                        </a>
                    </div>
                    @endauth
                    
                    <button @click="clearCart()" 
                            class="w-full mt-3 border border-gray-300 text-gray-700 hover:bg-gray-50 py-2 px-4 rounded-lg font-medium transition-colors">
                        Clear Cart
                    </button>
                </div>
            </div>
        </div>

        <!-- Continue Shopping -->
        <div x-show="items.length > 0" class="mt-12 text-center">
            <a href="{{ route('public.events') }}" 
               class="inline-flex items-center text-blue-600 hover:text-blue-700 font-medium">
                <svg class="mr-2 w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16l-4-4m0 0l4-4m-4 4h18"></path>
                </svg>
                Continue Shopping
            </a>
        </div>
    </div>
</div>

@push('scripts')
<script>
// Initialize cart data from localStorage
document.addEventListener('alpine:init', () => {
    Alpine.store('cart', {
        items: JSON.parse(localStorage.getItem('cart') || '[]'),
        
        get totalItems() {
            return this.items.reduce((sum, item) => sum + item.quantity, 0);
        },
        
        get totalAmount() {
            return this.items.reduce((sum, item) => sum + item.total, 0);
        }
    });
});
</script>
@endpush
@endsection
