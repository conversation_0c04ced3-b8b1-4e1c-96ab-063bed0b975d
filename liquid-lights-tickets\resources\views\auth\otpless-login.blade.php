<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - Liquid Lights</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&family=Playfair+Display:wght@400;500;600;700;800;900&display=swap" rel="stylesheet">
    <style>
        :root {
            --neon-cyan: #00ffff;
            --neon-pink: #ff00ff;
            --neon-purple: #8a2be2;
        }

        body {
            font-family: 'Inter', sans-serif;
            background: #0a0a0a;
            color: #ffffff;
        }

        .neon-text {
            color: var(--neon-cyan);
            text-shadow: 0 0 10px currentColor, 0 0 20px currentColor, 0 0 30px currentColor;
        }

        .neon-text-pink {
            color: var(--neon-pink);
            text-shadow: 0 0 10px currentColor, 0 0 20px currentColor, 0 0 30px currentColor;
        }

        .glass-morph {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 20px;
        }

        .liquid-btn {
            background: linear-gradient(45deg, var(--neon-cyan), var(--neon-purple));
            border: none;
            border-radius: 50px;
            color: #000;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 1px;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .liquid-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 30px rgba(0, 255, 255, 0.3);
        }

        .input-glow {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            color: white;
            transition: all 0.3s ease;
        }

        .input-glow:focus {
            border-color: var(--neon-cyan);
            box-shadow: 0 0 20px rgba(0, 255, 255, 0.3);
            outline: none;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        .pulse-animation {
            animation: pulse 2s ease-in-out infinite;
        }
    </style>
</head>
<body class="min-h-screen flex items-center justify-center p-4">
    <div class="w-full max-w-md">
        <!-- Logo and Title -->
        <div class="text-center mb-8">
            <div class="inline-flex items-center justify-center w-20 h-20 glass-morph mb-6">
                <svg class="w-10 h-10 neon-text" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                </svg>
            </div>
            <h1 class="text-4xl font-bold font-display mb-2">
                <span class="neon-text">LIQUID</span> 
                <span class="neon-text-pink">LIGHTS</span>
            </h1>
            <p class="text-gray-400">Enter your phone number to continue</p>
        </div>

        <!-- Login Form -->
        <div class="glass-morph p-8 shadow-2xl">
            <form id="otpless-form" class="space-y-6">
                @csrf
                
                <!-- Phone Number Field -->
                <div id="phone-step">
                    <label for="phone" class="block text-sm font-medium text-white mb-2">
                        Phone Number
                    </label>
                    <div class="relative">
                        <input type="tel" 
                               id="phone" 
                               name="phone" 
                               required 
                               class="w-full px-4 py-4 input-glow rounded-lg placeholder-gray-400"
                               placeholder="+91 98765 43210">
                        <div class="absolute inset-y-0 right-0 flex items-center pr-3">
                            <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                            </svg>
                        </div>
                    </div>
                    <p class="mt-2 text-sm text-gray-400">We'll send you a verification code</p>
                </div>

                <!-- OTP Field (Hidden initially) -->
                <div id="otp-step" class="hidden">
                    <label for="otp" class="block text-sm font-medium text-white mb-2">
                        Verification Code
                    </label>
                    <input type="text" 
                           id="otp" 
                           name="otp" 
                           maxlength="6"
                           class="w-full px-4 py-4 input-glow rounded-lg text-center text-2xl tracking-widest placeholder-gray-400"
                           placeholder="000000">
                    <p class="mt-2 text-sm text-gray-400">Enter the 6-digit code sent to your phone</p>
                </div>

                <!-- Submit Button -->
                <button type="submit" 
                        id="submit-btn"
                        class="w-full liquid-btn py-4 px-4 rounded-lg font-semibold text-lg">
                    <span id="btn-text">Send OTP</span>
                    <span id="btn-loading" class="hidden">
                        <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-black inline" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        Processing...
                    </span>
                </button>
            </form>

            <!-- Error Message -->
            <div id="error-message" class="hidden mt-4 p-4 bg-red-500/20 border border-red-500/30 rounded-lg">
                <p class="text-red-300 text-sm"></p>
            </div>

            <!-- Success Message -->
            <div id="success-message" class="hidden mt-4 p-4 bg-green-500/20 border border-green-500/30 rounded-lg">
                <p class="text-green-300 text-sm"></p>
            </div>

            <!-- Demo Info -->
            <div class="mt-6 p-4 bg-blue-500/10 border border-blue-500/20 rounded-lg">
                <p class="text-blue-300 text-sm font-medium mb-2">Demo Mode:</p>
                <div class="space-y-1 text-sm text-blue-200">
                    <p>• Enter any phone number</p>
                    <p>• Use any 6-digit code (e.g., 123456)</p>
                    <p>• OTPless integration ready for production</p>
                </div>
            </div>
        </div>

        <!-- Footer -->
        <div class="text-center mt-8">
            <p class="text-gray-400 text-sm">
                © {{ date('Y') }} Liquid Lights. All rights reserved.
            </p>
            <a href="{{ url('/') }}" class="text-gray-300 hover:neon-text text-sm underline mt-2 inline-block">
                ← Back to Website
            </a>
        </div>
    </div>

    <script>
        let currentStep = 'phone';
        let requestId = null;

        document.getElementById('otpless-form').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const submitBtn = document.getElementById('submit-btn');
            const btnText = document.getElementById('btn-text');
            const btnLoading = document.getElementById('btn-loading');
            
            // Show loading
            btnText.classList.add('hidden');
            btnLoading.classList.remove('hidden');
            submitBtn.disabled = true;
            
            hideMessages();
            
            if (currentStep === 'phone') {
                await sendOTP();
            } else {
                await verifyOTP();
            }
            
            // Hide loading
            btnText.classList.remove('hidden');
            btnLoading.classList.add('hidden');
            submitBtn.disabled = false;
        });

        async function sendOTP() {
            const phone = document.getElementById('phone').value;
            
            try {
                const response = await fetch('/auth/otpless/initiate', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '{{ csrf_token() }}'
                    },
                    body: JSON.stringify({ phone })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    requestId = data.request_id;
                    showOTPStep();
                    showSuccess('OTP sent successfully!');
                } else {
                    showError(data.message || 'Failed to send OTP');
                }
            } catch (error) {
                showError('Network error. Please try again.');
            }
        }

        async function verifyOTP() {
            const phone = document.getElementById('phone').value;
            const otp = document.getElementById('otp').value;
            
            try {
                const response = await fetch('/auth/otpless/verify', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '{{ csrf_token() }}'
                    },
                    body: JSON.stringify({ phone, otp, request_id: requestId })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    showSuccess('Login successful! Redirecting...');
                    setTimeout(() => {
                        window.location.href = data.redirect_url || '/user/dashboard';
                    }, 1500);
                } else {
                    showError(data.message || 'Invalid OTP');
                }
            } catch (error) {
                showError('Network error. Please try again.');
            }
        }

        function showOTPStep() {
            document.getElementById('phone-step').classList.add('hidden');
            document.getElementById('otp-step').classList.remove('hidden');
            document.getElementById('btn-text').textContent = 'Verify & Login';
            document.getElementById('otp').focus();
            currentStep = 'otp';
        }

        function showError(message) {
            const errorDiv = document.getElementById('error-message');
            errorDiv.querySelector('p').textContent = message;
            errorDiv.classList.remove('hidden');
        }

        function showSuccess(message) {
            const successDiv = document.getElementById('success-message');
            successDiv.querySelector('p').textContent = message;
            successDiv.classList.remove('hidden');
        }

        function hideMessages() {
            document.getElementById('error-message').classList.add('hidden');
            document.getElementById('success-message').classList.add('hidden');
        }

        // Auto-format phone number
        document.getElementById('phone').addEventListener('input', function(e) {
            let value = e.target.value.replace(/\D/g, '');
            if (value.length > 10) value = value.slice(0, 10);
            e.target.value = value;
        });

        // Auto-format OTP
        document.getElementById('otp').addEventListener('input', function(e) {
            let value = e.target.value.replace(/\D/g, '');
            if (value.length > 6) value = value.slice(0, 6);
            e.target.value = value;
        });
    </script>
</body>
</html>
