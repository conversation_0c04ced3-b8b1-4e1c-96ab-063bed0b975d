<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\SponsorshipTier;

class SponsorshipTierSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $tiers = [
            [
                'name' => 'Title Sponsor',
                'slug' => 'title-sponsor',
                'description' => 'Exclusive title sponsorship with maximum visibility and benefits',
                'price' => 500000.00,
                'benefits' => [
                    'Event naming rights',
                    'Logo on all marketing materials',
                    'Prime booth location',
                    'VIP tickets (10)',
                    'Speaking opportunity',
                    'Social media mentions',
                    'Press release inclusion',
                    'Website homepage feature'
                ],
                'color' => '#FFD700',
                'max_sponsors_per_event' => 1,
                'display_order' => 1,
                'is_active' => true
            ],
            [
                'name' => 'Platinum Sponsor',
                'slug' => 'platinum-sponsor',
                'description' => 'Premium sponsorship with extensive visibility and benefits',
                'price' => 250000.00,
                'benefits' => [
                    'Logo on main stage backdrop',
                    'Premium booth location',
                    'VIP tickets (6)',
                    'Social media mentions',
                    'Website feature',
                    'Program book full page ad',
                    'Email newsletter inclusion'
                ],
                'color' => '#E5E4E2',
                'max_sponsors_per_event' => 2,
                'display_order' => 2,
                'is_active' => true
            ],
            [
                'name' => 'Gold Sponsor',
                'slug' => 'gold-sponsor',
                'description' => 'High-visibility sponsorship with excellent brand exposure',
                'price' => 150000.00,
                'benefits' => [
                    'Logo on event materials',
                    'Standard booth space',
                    'VIP tickets (4)',
                    'Social media mentions',
                    'Website listing',
                    'Program book half page ad'
                ],
                'color' => '#FFD700',
                'max_sponsors_per_event' => 3,
                'display_order' => 3,
                'is_active' => true
            ],
            [
                'name' => 'Silver Sponsor',
                'slug' => 'silver-sponsor',
                'description' => 'Great value sponsorship with good brand visibility',
                'price' => 75000.00,
                'benefits' => [
                    'Logo on select materials',
                    'Booth space',
                    'VIP tickets (2)',
                    'Website listing',
                    'Program book quarter page ad'
                ],
                'color' => '#C0C0C0',
                'max_sponsors_per_event' => 5,
                'display_order' => 4,
                'is_active' => true
            ],
            [
                'name' => 'Bronze Sponsor',
                'slug' => 'bronze-sponsor',
                'description' => 'Entry-level sponsorship with basic brand exposure',
                'price' => 35000.00,
                'benefits' => [
                    'Logo on website',
                    'Small booth space',
                    'Event tickets (2)',
                    'Program book listing'
                ],
                'color' => '#CD7F32',
                'max_sponsors_per_event' => 10,
                'display_order' => 5,
                'is_active' => true
            ],
            [
                'name' => 'Supporting Sponsor',
                'slug' => 'supporting-sponsor',
                'description' => 'Community-focused sponsorship for local businesses',
                'price' => 15000.00,
                'benefits' => [
                    'Logo on website',
                    'Event tickets (2)',
                    'Program book listing',
                    'Social media mention'
                ],
                'color' => '#6B7280',
                'max_sponsors_per_event' => 20,
                'display_order' => 6,
                'is_active' => true
            ]
        ];

        foreach ($tiers as $tier) {
            SponsorshipTier::updateOrCreate(
                ['slug' => $tier['slug']],
                $tier
            );
        }
    }
}
