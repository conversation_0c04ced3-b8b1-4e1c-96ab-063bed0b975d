@extends('public.layout')

@section('title', 'Notification Settings - Liquid Lights Tickets')

@section('content')
<div x-data="notificationSettings" class="min-h-screen bg-gray-50 py-8">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Page Header -->
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-gray-900">Notification Settings</h1>
            <p class="mt-2 text-gray-600">Manage how you receive updates about your bookings and events</p>
        </div>

        <!-- PWA Installation Card -->
        <div x-show="!isAppInstalled" class="bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg p-6 mb-8 text-white">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4">
                    <div class="bg-white bg-opacity-20 p-3 rounded-lg">
                        <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z"></path>
                        </svg>
                    </div>
                    <div>
                        <h3 class="text-lg font-semibold">Install Our App</h3>
                        <p class="text-blue-100">Get instant notifications and offline access to your tickets</p>
                    </div>
                </div>
                <button @click="installApp()" 
                        class="bg-white text-blue-600 px-6 py-2 rounded-lg font-medium hover:bg-gray-100 transition-colors">
                    Install App
                </button>
            </div>
        </div>

        <!-- Push Notifications Card -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-8">
            <div class="p-6 border-b border-gray-200">
                <div class="flex items-center justify-between">
                    <div>
                        <h2 class="text-lg font-semibold text-gray-900">Push Notifications</h2>
                        <p class="text-sm text-gray-600 mt-1">Receive instant notifications on your device</p>
                    </div>
                    <div class="flex items-center space-x-4">
                        <span x-text="pushNotificationStatus" 
                              :class="isPushEnabled ? 'text-green-600' : 'text-gray-500'"
                              class="text-sm font-medium"></span>
                        <button @click="togglePushNotifications()" 
                                :disabled="loading"
                                :class="isPushEnabled ? 'bg-red-600 hover:bg-red-700' : 'bg-green-600 hover:bg-green-700'"
                                class="px-4 py-2 text-white rounded-lg font-medium transition-colors disabled:opacity-50">
                            <span x-show="!loading" x-text="isPushEnabled ? 'Disable' : 'Enable'"></span>
                            <span x-show="loading">Processing...</span>
                        </button>
                    </div>
                </div>
            </div>

            <!-- Push Notification Test -->
            <div x-show="isPushEnabled" class="p-6 bg-gray-50">
                <div class="flex items-center justify-between">
                    <div>
                        <h3 class="font-medium text-gray-900">Test Notifications</h3>
                        <p class="text-sm text-gray-600">Send a test notification to verify everything is working</p>
                    </div>
                    <button @click="sendTestNotification()" 
                            :disabled="loading"
                            class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors disabled:opacity-50">
                        Send Test
                    </button>
                </div>
            </div>
        </div>

        <!-- Notification Preferences -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-8">
            <div class="p-6 border-b border-gray-200">
                <h2 class="text-lg font-semibold text-gray-900">Notification Preferences</h2>
                <p class="text-sm text-gray-600 mt-1">Choose what notifications you want to receive</p>
            </div>

            <div class="p-6 space-y-6">
                <!-- Booking Confirmations -->
                <div class="flex items-center justify-between">
                    <div>
                        <h3 class="font-medium text-gray-900">Booking Confirmations</h3>
                        <p class="text-sm text-gray-600">Get notified when your booking is confirmed</p>
                    </div>
                    <label class="relative inline-flex items-center cursor-pointer">
                        <input type="checkbox" 
                               x-model="preferences.booking_confirmations"
                               @change="updatePreferences()"
                               class="sr-only peer">
                        <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                    </label>
                </div>

                <!-- Event Reminders -->
                <div class="flex items-center justify-between">
                    <div>
                        <h3 class="font-medium text-gray-900">Event Reminders</h3>
                        <p class="text-sm text-gray-600">Get reminded 24 hours before your events</p>
                    </div>
                    <label class="relative inline-flex items-center cursor-pointer">
                        <input type="checkbox" 
                               x-model="preferences.event_reminders"
                               @change="updatePreferences()"
                               class="sr-only peer">
                        <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                    </label>
                </div>

                <!-- Payment Updates -->
                <div class="flex items-center justify-between">
                    <div>
                        <h3 class="font-medium text-gray-900">Payment Updates</h3>
                        <p class="text-sm text-gray-600">Get notified about payment confirmations and failures</p>
                    </div>
                    <label class="relative inline-flex items-center cursor-pointer">
                        <input type="checkbox" 
                               x-model="preferences.payment_updates"
                               @change="updatePreferences()"
                               class="sr-only peer">
                        <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                    </label>
                </div>

                <!-- Event Updates -->
                <div class="flex items-center justify-between">
                    <div>
                        <h3 class="font-medium text-gray-900">Event Updates</h3>
                        <p class="text-sm text-gray-600">Get notified about changes to events you've booked</p>
                    </div>
                    <label class="relative inline-flex items-center cursor-pointer">
                        <input type="checkbox" 
                               x-model="preferences.event_updates"
                               @change="updatePreferences()"
                               class="sr-only peer">
                        <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                    </label>
                </div>

                <!-- Promotional Offers -->
                <div class="flex items-center justify-between">
                    <div>
                        <h3 class="font-medium text-gray-900">Promotional Offers</h3>
                        <p class="text-sm text-gray-600">Get notified about special deals and new events</p>
                    </div>
                    <label class="relative inline-flex items-center cursor-pointer">
                        <input type="checkbox" 
                               x-model="preferences.promotional_offers"
                               @change="updatePreferences()"
                               class="sr-only peer">
                        <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                    </label>
                </div>
            </div>
        </div>

        <!-- Browser Support Info -->
        <div class="bg-blue-50 border border-blue-200 rounded-lg p-6">
            <div class="flex items-start">
                <svg class="w-6 h-6 text-blue-600 mt-0.5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                <div>
                    <h3 class="font-medium text-blue-900">About Push Notifications</h3>
                    <div class="mt-2 text-sm text-blue-700 space-y-1">
                        <p>• Push notifications work best when you install our app on your device</p>
                        <p>• Notifications may not work in private/incognito browsing mode</p>
                        <p>• You can change these settings anytime from your browser or device settings</p>
                        <p>• We respect your privacy and only send relevant notifications</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
document.addEventListener('alpine:init', () => {
    Alpine.data('notificationSettings', () => ({
        isPushEnabled: false,
        isAppInstalled: false,
        loading: false,
        preferences: {
            booking_confirmations: true,
            event_reminders: true,
            payment_updates: true,
            event_updates: true,
            promotional_offers: false
        },
        
        get pushNotificationStatus() {
            if (!('Notification' in window)) {
                return 'Not supported';
            }
            
            if (this.isPushEnabled) {
                return 'Enabled';
            }
            
            return Notification.permission === 'denied' ? 'Blocked' : 'Disabled';
        },
        
        init() {
            this.checkAppInstallation();
            this.checkPushNotificationStatus();
            this.loadPreferences();
        },
        
        checkAppInstallation() {
            this.isAppInstalled = window.matchMedia('(display-mode: standalone)').matches ||
                                 window.navigator.standalone ||
                                 localStorage.getItem('pwa-installed') === 'true';
        },
        
        async checkPushNotificationStatus() {
            if (!('serviceWorker' in navigator) || !('PushManager' in window)) {
                return;
            }
            
            try {
                const registration = await navigator.serviceWorker.ready;
                const subscription = await registration.pushManager.getSubscription();
                this.isPushEnabled = subscription !== null;
            } catch (error) {
                console.error('Error checking push notification status:', error);
            }
        },
        
        async loadPreferences() {
            try {
                const response = await fetch('/api/notifications/preferences');
                const data = await response.json();
                
                if (data.success) {
                    this.preferences = { ...this.preferences, ...data.preferences };
                }
            } catch (error) {
                console.error('Error loading preferences:', error);
            }
        },
        
        async togglePushNotifications() {
            this.loading = true;
            
            try {
                if (this.isPushEnabled) {
                    await this.disablePushNotifications();
                } else {
                    await this.enablePushNotifications();
                }
            } catch (error) {
                console.error('Error toggling push notifications:', error);
                this.showNotification('Failed to update notification settings', 'error');
            } finally {
                this.loading = false;
            }
        },
        
        async enablePushNotifications() {
            if (!window.pushManager) {
                this.showNotification('Push notifications not supported', 'error');
                return;
            }
            
            try {
                await window.pushManager.subscribe();
                this.isPushEnabled = true;
                this.showNotification('Push notifications enabled!', 'success');
            } catch (error) {
                this.showNotification('Failed to enable push notifications', 'error');
                throw error;
            }
        },
        
        async disablePushNotifications() {
            if (!window.pushManager) {
                return;
            }
            
            try {
                await window.pushManager.unsubscribe();
                this.isPushEnabled = false;
                this.showNotification('Push notifications disabled', 'info');
            } catch (error) {
                this.showNotification('Failed to disable push notifications', 'error');
                throw error;
            }
        },
        
        async sendTestNotification() {
            if (!this.isPushEnabled) {
                this.showNotification('Please enable push notifications first', 'warning');
                return;
            }
            
            this.loading = true;
            
            try {
                const response = await fetch('/api/notifications/test', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    }
                });
                
                const data = await response.json();
                
                if (data.success) {
                    this.showNotification('Test notification sent! Check your device.', 'success');
                } else {
                    this.showNotification(data.message || 'Failed to send test notification', 'error');
                }
            } catch (error) {
                console.error('Error sending test notification:', error);
                this.showNotification('Failed to send test notification', 'error');
            } finally {
                this.loading = false;
            }
        },
        
        async updatePreferences() {
            try {
                const response = await fetch('/api/notifications/preferences', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    },
                    body: JSON.stringify(this.preferences)
                });
                
                const data = await response.json();
                
                if (data.success) {
                    this.showNotification('Preferences updated successfully', 'success');
                } else {
                    this.showNotification('Failed to update preferences', 'error');
                }
            } catch (error) {
                console.error('Error updating preferences:', error);
                this.showNotification('Failed to update preferences', 'error');
            }
        },
        
        installApp() {
            if (window.installManager) {
                window.installManager.promptInstall();
            } else {
                this.showNotification('App installation not available', 'info');
            }
        },
        
        showNotification(message, type = 'info') {
            // Create notification element
            const notification = document.createElement('div');
            notification.className = `fixed top-4 right-4 max-w-sm bg-white border border-gray-200 rounded-lg shadow-lg z-50 transform transition-all duration-300 translate-x-full`;
            
            const colors = {
                success: 'border-green-200 bg-green-50',
                error: 'border-red-200 bg-red-50',
                warning: 'border-yellow-200 bg-yellow-50',
                info: 'border-blue-200 bg-blue-50'
            };
            
            notification.className = notification.className.replace('bg-white border-gray-200', colors[type] || colors.info);
            
            notification.innerHTML = `
                <div class="p-4">
                    <div class="flex items-start">
                        <div class="flex-1">
                            <p class="text-sm text-gray-900">${message}</p>
                        </div>
                        <button onclick="this.closest('.fixed').remove()" class="ml-4 text-gray-400 hover:text-gray-600">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                        </button>
                    </div>
                </div>
            `;
            
            document.body.appendChild(notification);
            
            // Animate in
            setTimeout(() => {
                notification.classList.remove('translate-x-full');
            }, 100);
            
            // Auto-remove after 5 seconds
            setTimeout(() => {
                notification.classList.add('translate-x-full');
                setTimeout(() => {
                    if (notification.parentElement) {
                        notification.remove();
                    }
                }, 300);
            }, 5000);
        }
    }));
});
</script>
@endpush

@endsection
