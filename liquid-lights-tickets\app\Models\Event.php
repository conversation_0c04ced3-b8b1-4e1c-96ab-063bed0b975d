<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Event extends Model
{
    use HasFactory;

    protected $fillable = [
        'title',
        'slug',
        'description',
        'short_description',
        'venue_name',
        'venue_address',
        'event_date',
        'event_time',
        'banner_image',
        'gallery_images',
        'status',
        'meta_title',
        'meta_description',
        'is_featured',
        'created_by',
    ];

    protected function casts(): array
    {
        return [
            'event_date' => 'date',
            'event_time' => 'datetime',
            'gallery_images' => 'array',
            'is_featured' => 'boolean',
        ];
    }

    /**
     * Relationships
     */
    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function ticketTypes()
    {
        return $this->hasMany(TicketType::class);
    }

    public function bookings()
    {
        return $this->hasMany(Booking::class);
    }

    public function artists()
    {
        return $this->belongsToMany(Artist::class, 'event_artists')
                    ->withPivot('role')
                    ->withTimestamps();
    }

    /**
     * Scopes
     */
    public function scopePublished($query)
    {
        return $query->where('status', 'published');
    }

    public function scopeFeatured($query)
    {
        return $query->where('is_featured', true);
    }

    /**
     * Get the sponsors for this event
     */
    public function sponsors()
    {
        return $this->belongsToMany(Sponsor::class, 'event_sponsors')
            ->withPivot(['sponsorship_tier_id', 'amount', 'start_date', 'end_date', 'status', 'custom_benefits', 'notes', 'display_order'])
            ->withTimestamps();
    }

    /**
     * Get the event sponsors pivot records
     */
    public function eventSponsors()
    {
        return $this->hasMany(EventSponsor::class);
    }

    /**
     * Get active sponsors
     */
    public function activeSponsors()
    {
        return $this->eventSponsors()
            ->active()
            ->current()
            ->with(['sponsor', 'sponsorshipTier'])
            ->ordered();
    }

    /**
     * Get sponsors by tier
     */
    public function getSponsorsByTier($tierSlug = null)
    {
        $query = $this->activeSponsors();

        if ($tierSlug) {
            $query->whereHas('sponsorshipTier', function ($q) use ($tierSlug) {
                $q->where('slug', $tierSlug);
            });
        }

        return $query->get()->groupBy('sponsorshipTier.name');
    }

    /**
     * Check if event has sponsors
     */
    public function hasSponsors()
    {
        return $this->activeSponsors()->count() > 0;
    }
}
