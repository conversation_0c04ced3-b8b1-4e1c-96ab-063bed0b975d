<?php

namespace App\Services;

use App\Models\User;
use App\Models\Booking;
use App\Models\Event;
use App\Models\PushSubscription;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Exception;

class PushNotificationService
{
    protected $vapidPublicKey;
    protected $vapidPrivateKey;
    protected $vapidSubject;

    public function __construct()
    {
        $this->vapidPublicKey = config('services.vapid.public_key');
        $this->vapidPrivateKey = config('services.vapid.private_key');
        $this->vapidSubject = config('services.vapid.subject', 'mailto:<EMAIL>');
    }

    /**
     * Subscribe user to push notifications
     */
    public function subscribe(User $user, array $subscriptionData)
    {
        try {
            // Validate subscription data
            if (!isset($subscriptionData['endpoint'], $subscriptionData['keys']['p256dh'], $subscriptionData['keys']['auth'])) {
                throw new Exception('Invalid subscription data');
            }

            // Create or update subscription
            $subscription = PushSubscription::updateOrCreate(
                [
                    'user_id' => $user->id,
                    'endpoint' => $subscriptionData['endpoint']
                ],
                [
                    'p256dh_key' => $subscriptionData['keys']['p256dh'],
                    'auth_key' => $subscriptionData['keys']['auth'],
                    'user_agent' => request()->userAgent(),
                    'ip_address' => request()->ip(),
                    'is_active' => true,
                    'subscribed_at' => now()
                ]
            );

            Log::info('Push notification subscription created', [
                'user_id' => $user->id,
                'subscription_id' => $subscription->id
            ]);

            return [
                'success' => true,
                'subscription_id' => $subscription->id
            ];

        } catch (Exception $e) {
            Log::error('Failed to create push subscription', [
                'user_id' => $user->id,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Unsubscribe user from push notifications
     */
    public function unsubscribe(User $user, string $endpoint = null)
    {
        try {
            $query = PushSubscription::where('user_id', $user->id);
            
            if ($endpoint) {
                $query->where('endpoint', $endpoint);
            }

            $deletedCount = $query->delete();

            Log::info('Push notification unsubscribed', [
                'user_id' => $user->id,
                'deleted_count' => $deletedCount
            ]);

            return [
                'success' => true,
                'deleted_count' => $deletedCount
            ];

        } catch (Exception $e) {
            Log::error('Failed to unsubscribe from push notifications', [
                'user_id' => $user->id,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Send booking confirmation notification
     */
    public function sendBookingConfirmation(Booking $booking)
    {
        $payload = [
            'title' => 'Booking Confirmed! 🎉',
            'body' => "Your tickets for {$booking->event->title} are confirmed!",
            'icon' => '/images/icons/icon-192x192.png',
            'badge' => '/images/icons/badge-72x72.png',
            'data' => [
                'type' => 'booking_confirmation',
                'booking_id' => $booking->id,
                'event_id' => $booking->event_id,
                'url' => route('user.booking', $booking->id)
            ],
            'actions' => [
                [
                    'action' => 'view_booking',
                    'title' => 'View Booking',
                    'icon' => '/images/icons/action-view.png'
                ],
                [
                    'action' => 'download_ticket',
                    'title' => 'Download Ticket',
                    'icon' => '/images/icons/action-download.png'
                ]
            ],
            'requireInteraction' => true,
            'vibrate' => [200, 100, 200]
        ];

        return $this->sendToUser($booking->user, $payload);
    }

    /**
     * Send event reminder notification
     */
    public function sendEventReminder(Booking $booking, int $hoursBeforeEvent = 24)
    {
        $eventDateTime = \Carbon\Carbon::parse($booking->event->event_date . ' ' . $booking->event->event_time);
        
        $payload = [
            'title' => 'Event Reminder ⏰',
            'body' => "{$booking->event->title} is in {$hoursBeforeEvent} hours!",
            'icon' => '/images/icons/icon-192x192.png',
            'badge' => '/images/icons/badge-72x72.png',
            'data' => [
                'type' => 'event_reminder',
                'booking_id' => $booking->id,
                'event_id' => $booking->event_id,
                'hours_before' => $hoursBeforeEvent,
                'url' => route('user.booking', $booking->id)
            ],
            'actions' => [
                [
                    'action' => 'view_ticket',
                    'title' => 'View Ticket',
                    'icon' => '/images/icons/action-ticket.png'
                ],
                [
                    'action' => 'get_directions',
                    'title' => 'Get Directions',
                    'icon' => '/images/icons/action-directions.png'
                ]
            ],
            'requireInteraction' => true,
            'vibrate' => [100, 50, 100, 50, 100]
        ];

        return $this->sendToUser($booking->user, $payload);
    }

    /**
     * Send payment success notification
     */
    public function sendPaymentSuccess(Booking $booking)
    {
        $payload = [
            'title' => 'Payment Successful ✅',
            'body' => "Payment of ₹{$booking->total_amount} completed for {$booking->event->title}",
            'icon' => '/images/icons/icon-192x192.png',
            'badge' => '/images/icons/badge-72x72.png',
            'data' => [
                'type' => 'payment_success',
                'booking_id' => $booking->id,
                'amount' => $booking->total_amount,
                'url' => route('user.booking', $booking->id)
            ],
            'actions' => [
                [
                    'action' => 'download_ticket',
                    'title' => 'Download Ticket',
                    'icon' => '/images/icons/action-download.png'
                ]
            ],
            'vibrate' => [200, 100, 200]
        ];

        return $this->sendToUser($booking->user, $payload);
    }

    /**
     * Send event update notification
     */
    public function sendEventUpdate(Event $event, string $updateMessage, array $affectedUserIds = [])
    {
        $payload = [
            'title' => 'Event Update 📢',
            'body' => $updateMessage,
            'icon' => '/images/icons/icon-192x192.png',
            'badge' => '/images/icons/badge-72x72.png',
            'data' => [
                'type' => 'event_update',
                'event_id' => $event->id,
                'url' => route('public.event', $event->id)
            ],
            'actions' => [
                [
                    'action' => 'view_event',
                    'title' => 'View Event',
                    'icon' => '/images/icons/action-view.png'
                ]
            ]
        ];

        if (empty($affectedUserIds)) {
            // Send to all users with bookings for this event
            $affectedUserIds = $event->bookings()
                ->where('payment_status', 'paid')
                ->pluck('user_id')
                ->unique()
                ->toArray();
        }

        return $this->sendToMultipleUsers($affectedUserIds, $payload);
    }

    /**
     * Send notification to a single user
     */
    public function sendToUser(User $user, array $payload)
    {
        $subscriptions = $user->pushSubscriptions()->where('is_active', true)->get();
        
        if ($subscriptions->isEmpty()) {
            return [
                'success' => false,
                'message' => 'No active subscriptions found for user'
            ];
        }

        $results = [];
        foreach ($subscriptions as $subscription) {
            $result = $this->sendToSubscription($subscription, $payload);
            $results[] = $result;
            
            // Deactivate subscription if it failed with certain errors
            if (!$result['success'] && in_array($result['status_code'] ?? 0, [410, 413, 429])) {
                $subscription->update(['is_active' => false]);
            }
        }

        $successCount = collect($results)->where('success', true)->count();
        
        return [
            'success' => $successCount > 0,
            'total_sent' => $successCount,
            'total_failed' => count($results) - $successCount,
            'results' => $results
        ];
    }

    /**
     * Send notification to multiple users
     */
    public function sendToMultipleUsers(array $userIds, array $payload)
    {
        $users = User::whereIn('id', $userIds)->get();
        $results = [];

        foreach ($users as $user) {
            $result = $this->sendToUser($user, $payload);
            $results[] = [
                'user_id' => $user->id,
                'result' => $result
            ];
        }

        $totalSent = collect($results)->sum('result.total_sent');
        $totalFailed = collect($results)->sum('result.total_failed');

        return [
            'success' => $totalSent > 0,
            'total_users' => count($users),
            'total_sent' => $totalSent,
            'total_failed' => $totalFailed,
            'results' => $results
        ];
    }

    /**
     * Send notification to a specific subscription
     */
    protected function sendToSubscription(PushSubscription $subscription, array $payload)
    {
        try {
            $headers = [
                'TTL' => 86400, // 24 hours
                'Urgency' => 'normal',
                'Content-Type' => 'application/json',
                'Content-Encoding' => 'aes128gcm'
            ];

            // Add VAPID headers for authentication
            if ($this->vapidPublicKey && $this->vapidPrivateKey) {
                $vapidHeaders = $this->generateVapidHeaders($subscription->endpoint);
                $headers = array_merge($headers, $vapidHeaders);
            }

            $response = Http::withHeaders($headers)
                ->timeout(30)
                ->post($subscription->endpoint, [
                    'payload' => json_encode($payload)
                ]);

            if ($response->successful()) {
                Log::info('Push notification sent successfully', [
                    'subscription_id' => $subscription->id,
                    'user_id' => $subscription->user_id
                ]);

                return [
                    'success' => true,
                    'status_code' => $response->status()
                ];
            } else {
                Log::warning('Push notification failed', [
                    'subscription_id' => $subscription->id,
                    'status_code' => $response->status(),
                    'response' => $response->body()
                ]);

                return [
                    'success' => false,
                    'status_code' => $response->status(),
                    'error' => $response->body()
                ];
            }

        } catch (Exception $e) {
            Log::error('Push notification exception', [
                'subscription_id' => $subscription->id,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Generate VAPID headers for authentication
     */
    protected function generateVapidHeaders(string $endpoint)
    {
        // This is a simplified version - in production, use a proper VAPID library
        $audience = parse_url($endpoint, PHP_URL_SCHEME) . '://' . parse_url($endpoint, PHP_URL_HOST);
        
        return [
            'Authorization' => 'vapid t=' . $this->generateJWT($audience) . ', k=' . $this->vapidPublicKey
        ];
    }

    /**
     * Generate JWT token for VAPID
     */
    protected function generateJWT(string $audience)
    {
        // This is a placeholder - use a proper JWT library like firebase/php-jwt
        $header = base64url_encode(json_encode(['typ' => 'JWT', 'alg' => 'ES256']));
        $payload = base64url_encode(json_encode([
            'aud' => $audience,
            'exp' => time() + 43200, // 12 hours
            'sub' => $this->vapidSubject
        ]));
        
        return $header . '.' . $payload . '.signature';
    }

    /**
     * Get VAPID public key for client-side subscription
     */
    public function getVapidPublicKey()
    {
        return $this->vapidPublicKey;
    }
}

// Helper function for base64url encoding
if (!function_exists('base64url_encode')) {
    function base64url_encode($data) {
        return rtrim(strtr(base64_encode($data), '+/', '-_'), '=');
    }
}
