<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('event_sponsors', function (Blueprint $table) {
            $table->id();
            $table->foreignId('event_id')->constrained()->onDelete('cascade');
            $table->foreignId('sponsor_id')->constrained()->onDelete('cascade');
            $table->foreignId('sponsorship_tier_id')->constrained()->onDelete('cascade');
            $table->decimal('amount', 10, 2)->nullable(); // Actual sponsorship amount
            $table->date('start_date')->nullable();
            $table->date('end_date')->nullable();
            $table->enum('status', ['active', 'inactive', 'pending', 'expired'])->default('active');
            $table->json('custom_benefits')->nullable(); // Additional benefits for this specific sponsorship
            $table->text('notes')->nullable();
            $table->integer('display_order')->default(0);
            $table->timestamps();

            $table->unique(['event_id', 'sponsor_id', 'sponsorship_tier_id'], 'event_sponsor_tier_unique');
            $table->index(['event_id', 'status']);
            $table->index(['sponsor_id', 'status']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('event_sponsors');
    }
};
