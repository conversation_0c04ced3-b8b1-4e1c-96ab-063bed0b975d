# 🔄 User Flow – Liquid Lights Ticketing Platform

This document outlines the complete user journey and system flow for different roles on the Liquid Lights platform. It covers both frontend (public and user) and backend (admin/staff) interactions.

---

## 👤 Public User Flow (Customer/Attendee)

### 1. **Landing on Homepage**

* Sees animated hero banner + featured events
* Can navigate to Events, About, Contact, Gallery

### 2. **Event Discovery**

* Click on "Events" to view listings
* Filters by location, date, type (party, concert)
* Click on any event card to open the detail page

### 3. **Booking Tickets**

* Click "Book Now"
* Login via OTP-less (Email or WhatsApp Magic Link)
* Fill short form (Name, Phone, Email)
* Select ticket type & quantity
* Make payment (PhonePe, Razorpay, UPI)
* On success:

  * Ticket is displayed with QR code
  * Ticket sent via WhatsApp/Email
  * Ticket saved to PWA (offline)

### 4. **Post-booking Experience**

* Access "My Tickets"
* See all booked events, download QR code
* Reminder via WhatsApp/SMS before event
* Show QR at entry gate (scanned by staff)

---

## 🧑‍💼 Admin/Staff Flow

### 1. **Admin Login**

* Email/password login with optional 2FA
* Role-based redirect (<PERSON> Admin, Manager, <PERSON><PERSON><PERSON>, Tick<PERSON>)

### 2. **Dashboard Access**

* View KPIs, alerts, booking charts, active events
* Quick actions: Create Event, Export Bookings

### 3. **Event Creation**

* Fill form: Event name, description, venue, time
* Upload images/videos
* Set ticket types, pricing, seat limits
* Enable features: WhatsApp Reminder, Artist Info
* SEO & publish options

### 4. **Booking Management**

* View bookings list with filters
* Resend ticket, refund or cancel
* Export as CSV/PDF
* Manual booking entry supported

### 5. **User Management**

* Search by phone/email
* View booking history
* Blacklist abusive users

### 6. **QR Scanning (Event Day)**

* Scanner role login
* Opens camera via browser
* Scans guest QR code
* Validates ticket and logs time/status

### 7. **CMS Page Builder**

* Drag and drop blocks (text, images, videos, CTAs)
* Create new landing pages or modify existing
* Save as draft, preview, publish

### 8. **Notification & Campaigns**

* Send WhatsApp blasts, SMS alerts, push notifications
* Use templates or custom messages
* Review delivery and open rates

---

## 🔄 System Integrations & Flow

* **OTPless**: Magic link generation and session creation
* **PhonePe/Razorpay**: Payment initiation and callback handling
* **WhatsApp API**: Ticket sharing, reminders, thank-you messages
* **OneSignal**: Push notification delivery
* **IndexedDB / Service Worker**: Offline ticket caching for PWA

Let me know if you need a visual user flow diagram or a component architecture map.
