<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\EventSponsor;
use App\Models\Event;
use App\Models\Sponsor;
use App\Models\SponsorshipTier;

class EventSponsorController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = EventSponsor::with(['event', 'sponsor', 'sponsorshipTier']);

        // Search functionality
        if ($request->filled('search')) {
            $query->whereHas('sponsor', function($q) use ($request) {
                $q->where('name', 'like', "%{$request->search}%");
            })->orWhereHas('event', function($q) use ($request) {
                $q->where('title', 'like', "%{$request->search}%");
            });
        }

        // Status filter
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Event filter
        if ($request->filled('event_id')) {
            $query->where('event_id', $request->event_id);
        }

        // Sponsor filter
        if ($request->filled('sponsor_id')) {
            $query->where('sponsor_id', $request->sponsor_id);
        }

        // Sort
        $sortBy = $request->get('sort_by', 'created_at');
        $sortOrder = $request->get('sort_order', 'desc');
        $query->orderBy($sortBy, $sortOrder);

        $eventSponsors = $query->paginate(15)->withQueryString();

        // Get filter options
        $events = Event::where('status', 'published')->orderBy('title')->get();
        $sponsors = Sponsor::where('status', 'active')->orderBy('name')->get();

        // Get statistics
        $stats = [
            'total' => EventSponsor::count(),
            'active' => EventSponsor::where('status', 'active')->count(),
            'pending' => EventSponsor::where('status', 'pending')->count(),
            'total_revenue' => EventSponsor::where('status', 'active')->sum('amount'),
        ];

        return view('admin.event-sponsors.index', compact('eventSponsors', 'events', 'sponsors', 'stats'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create(Request $request)
    {
        $events = Event::where('status', 'published')->orderBy('title')->get();
        $sponsors = Sponsor::where('status', 'active')->orderBy('name')->get();
        $tiers = SponsorshipTier::where('is_active', true)->ordered()->get();

        $selectedEvent = $request->get('event_id') ? Event::find($request->get('event_id')) : null;
        $selectedSponsor = $request->get('sponsor_id') ? Sponsor::find($request->get('sponsor_id')) : null;

        return view('admin.event-sponsors.create', compact('events', 'sponsors', 'tiers', 'selectedEvent', 'selectedSponsor'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'event_id' => 'required|exists:events,id',
            'sponsor_id' => 'required|exists:sponsors,id',
            'sponsorship_tier_id' => 'required|exists:sponsorship_tiers,id',
            'amount' => 'nullable|numeric|min:0',
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
            'status' => 'required|in:active,inactive,pending,expired',
            'custom_benefits' => 'nullable|array',
            'custom_benefits.*' => 'string',
            'notes' => 'nullable|string',
            'display_order' => 'nullable|integer|min:0'
        ]);

        // Check if this combination already exists
        $existing = EventSponsor::where('event_id', $validated['event_id'])
            ->where('sponsor_id', $validated['sponsor_id'])
            ->where('sponsorship_tier_id', $validated['sponsorship_tier_id'])
            ->first();

        if ($existing) {
            return back()->withErrors(['sponsor_id' => 'This sponsor is already assigned to this event with the same tier.'])->withInput();
        }

        // Check tier availability
        $tier = SponsorshipTier::find($validated['sponsorship_tier_id']);
        if (!$tier->hasAvailableSlots($validated['event_id'])) {
            return back()->withErrors(['sponsorship_tier_id' => 'No available slots for this sponsorship tier.'])->withInput();
        }

        // Convert custom benefits array to proper format
        if (isset($validated['custom_benefits'])) {
            $validated['custom_benefits'] = array_filter($validated['custom_benefits']);
        }

        $eventSponsor = EventSponsor::create($validated);

        return redirect()->route('admin.event-sponsors.show', $eventSponsor)
            ->with('success', 'Event sponsorship created successfully.');
    }

    /**
     * Display the specified resource.
     */
    public function show(EventSponsor $eventSponsor)
    {
        $eventSponsor->load(['event', 'sponsor', 'sponsorshipTier']);

        // Get all benefits (tier + custom)
        $allBenefits = $eventSponsor->getAllBenefits();

        return view('admin.event-sponsors.show', compact('eventSponsor', 'allBenefits'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(EventSponsor $eventSponsor)
    {
        $events = Event::where('status', 'published')->orderBy('title')->get();
        $sponsors = Sponsor::where('status', 'active')->orderBy('name')->get();
        $tiers = SponsorshipTier::where('is_active', true)->ordered()->get();

        return view('admin.event-sponsors.edit', compact('eventSponsor', 'events', 'sponsors', 'tiers'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, EventSponsor $eventSponsor)
    {
        $validated = $request->validate([
            'event_id' => 'required|exists:events,id',
            'sponsor_id' => 'required|exists:sponsors,id',
            'sponsorship_tier_id' => 'required|exists:sponsorship_tiers,id',
            'amount' => 'nullable|numeric|min:0',
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
            'status' => 'required|in:active,inactive,pending,expired',
            'custom_benefits' => 'nullable|array',
            'custom_benefits.*' => 'string',
            'notes' => 'nullable|string',
            'display_order' => 'nullable|integer|min:0'
        ]);

        // Check if this combination already exists (excluding current record)
        $existing = EventSponsor::where('event_id', $validated['event_id'])
            ->where('sponsor_id', $validated['sponsor_id'])
            ->where('sponsorship_tier_id', $validated['sponsorship_tier_id'])
            ->where('id', '!=', $eventSponsor->id)
            ->first();

        if ($existing) {
            return back()->withErrors(['sponsor_id' => 'This sponsor is already assigned to this event with the same tier.'])->withInput();
        }

        // Convert custom benefits array to proper format
        if (isset($validated['custom_benefits'])) {
            $validated['custom_benefits'] = array_filter($validated['custom_benefits']);
        }

        $eventSponsor->update($validated);

        return redirect()->route('admin.event-sponsors.show', $eventSponsor)
            ->with('success', 'Event sponsorship updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(EventSponsor $eventSponsor)
    {
        $eventSponsor->delete();

        return redirect()->route('admin.event-sponsors.index')
            ->with('success', 'Event sponsorship deleted successfully.');
    }
}
