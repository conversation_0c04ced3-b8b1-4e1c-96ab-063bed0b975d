import{m as n}from"./module.esm-DtktNP9n.js";n.data("cart",()=>({items:[],isOpen:!1,loading:!1,init(){this.loadCart()},loadCart(){const e=localStorage.getItem("cart");e&&(this.items=JSON.parse(e))},saveCart(){localStorage.setItem("cart",JSON.stringify(this.items))},addItem(e,t,s=1,i,a,o){const r=this.items.find(c=>c.eventId===e&&c.ticketTypeId===t);r?r.quantity+=s:this.items.push({eventId:e,ticketTypeId:t,quantity:s,price:i,eventTitle:a,ticketTypeName:o,total:i*s}),this.updateTotals(),this.saveCart(),this.showNotification("Item added to cart!")},removeItem(e,t){this.items=this.items.filter(s=>!(s.eventId===e&&s.ticketTypeId===t)),this.updateTotals(),this.saveCart()},updateQuantity(e,t,s){const i=this.items.find(a=>a.eventId===e&&a.ticketTypeId===t);i&&(i.quantity=Math.max(1,s),i.total=i.price*i.quantity,this.updateTotals(),this.saveCart())},updateTotals(){this.items.forEach(e=>{e.total=e.price*e.quantity})},get totalItems(){return this.items.reduce((e,t)=>e+t.quantity,0)},get totalAmount(){return this.items.reduce((e,t)=>e+t.total,0)},clearCart(){this.items=[],this.saveCart()},showNotification(e){const t=document.createElement("div");t.className="fixed top-4 right-4 bg-green-500 text-white px-4 py-2 rounded-lg shadow-lg z-50",t.textContent=e,document.body.appendChild(t),setTimeout(()=>{t.remove()},3e3)}}));n.data("eventFilters",()=>({filters:{search:"",category:"",date_from:"",date_to:"",price_min:"",price_max:"",sort:"date"},init(){const e=new URLSearchParams(window.location.search);Object.keys(this.filters).forEach(t=>{e.has(t)&&(this.filters[t]=e.get(t))})},applyFilters(){const e=new URLSearchParams;Object.keys(this.filters).forEach(t=>{this.filters[t]&&e.set(t,this.filters[t])}),window.location.href=`${window.location.pathname}?${e.toString()}`},clearFilters(){Object.keys(this.filters).forEach(e=>{this.filters[e]=""}),window.location.href=window.location.pathname}}));n.data("ticketSelector",(e,t)=>({eventId:e,ticketTypes:t,selectedTickets:{},loading:!1,init(){this.ticketTypes.forEach(s=>{this.selectedTickets[s.id]=0})},updateQuantity(s,i){this.selectedTickets[s]=Math.max(0,Math.min(i,this.getAvailableQuantity(s)))},getAvailableQuantity(s){const i=this.ticketTypes.find(a=>a.id===s);return i?i.quantity_available-i.quantity_sold:0},get totalTickets(){return Object.values(this.selectedTickets).reduce((s,i)=>s+i,0)},get totalAmount(){return Object.entries(this.selectedTickets).reduce((s,[i,a])=>{const o=this.ticketTypes.find(r=>r.id==i);return s+(o?o.price*a:0)},0)},addToCart(){const s=n.store("cart");Object.entries(this.selectedTickets).forEach(([i,a])=>{var o;if(a>0){const r=this.ticketTypes.find(c=>c.id==i);r&&s.addItem(this.eventId,parseInt(i),a,r.price,((o=r.event)==null?void 0:o.title)||"Event",r.name)}}),Object.keys(this.selectedTickets).forEach(i=>{this.selectedTickets[i]=0})}}));n.data("imageGallery",e=>({images:e,currentIndex:0,isOpen:!1,openGallery(t=0){this.currentIndex=t,this.isOpen=!0,document.body.style.overflow="hidden"},closeGallery(){this.isOpen=!1,document.body.style.overflow="auto"},nextImage(){this.currentIndex=(this.currentIndex+1)%this.images.length},prevImage(){this.currentIndex=this.currentIndex===0?this.images.length-1:this.currentIndex-1},get currentImage(){return this.images[this.currentIndex]}}));n.data("search",()=>({query:"",results:[],loading:!1,showResults:!1,async performSearch(){if(this.query.length<2){this.results=[],this.showResults=!1;return}this.loading=!0;try{const t=await(await fetch(`/api/search?q=${encodeURIComponent(this.query)}`)).json();t.success&&(this.results=t.data,this.showResults=!0)}catch(e){console.error("Search error:",e)}finally{this.loading=!1}},selectResult(e){window.location.href=`/events/${e.id}`},hideResults(){setTimeout(()=>{this.showResults=!1},200)}}));n.data("formValidator",e=>({errors:{},validate(t,s){const i=e[t];return i?(this.errors[t]=[],i.forEach(a=>{a.required&&(!s||s.trim()==="")&&this.errors[t].push(a.message||`${t} is required`),a.email&&s&&!this.isValidEmail(s)&&this.errors[t].push(a.message||"Please enter a valid email address"),a.min&&s&&s.length<a.min&&this.errors[t].push(a.message||`${t} must be at least ${a.min} characters`),a.max&&s&&s.length>a.max&&this.errors[t].push(a.message||`${t} must not exceed ${a.max} characters`)}),this.errors[t].length===0):!0},isValidEmail(t){return/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(t)},hasError(t){return this.errors[t]&&this.errors[t].length>0},getError(t){return this.hasError(t)?this.errors[t][0]:""}}));window.formatCurrency=e=>new Intl.NumberFormat("en-IN",{style:"currency",currency:"INR"}).format(e);window.formatDate=e=>new Date(e).toLocaleDateString("en-IN",{weekday:"long",year:"numeric",month:"long",day:"numeric"});window.formatTime=e=>new Date(`2000-01-01T${e}`).toLocaleTimeString("en-IN",{hour:"2-digit",minute:"2-digit",hour12:!0});n.store("cart",{items:JSON.parse(localStorage.getItem("cart")||"[]"),get totalItems(){return this.items.reduce((e,t)=>e+t.quantity,0)},get totalAmount(){return this.items.reduce((e,t)=>e+t.total,0)},addItem(e,t,s,i,a,o){const r=this.items.find(c=>c.eventId===e&&c.ticketTypeId===t);r?(r.quantity+=s,r.total=r.price*r.quantity):this.items.push({eventId:e,ticketTypeId:t,quantity:s,price:i,eventTitle:a,ticketTypeName:o,total:i*s}),this.saveCart(),this.showNotification("Item added to cart!")},removeItem(e,t){this.items=this.items.filter(s=>!(s.eventId===e&&s.ticketTypeId===t)),this.saveCart()},updateQuantity(e,t,s){const i=this.items.find(a=>a.eventId===e&&a.ticketTypeId===t);i&&(i.quantity=Math.max(1,s),i.total=i.price*i.quantity,this.saveCart())},clearCart(){this.items=[],this.saveCart()},saveCart(){localStorage.setItem("cart",JSON.stringify(this.items))},showNotification(e){const t=document.createElement("div");t.className="fixed top-4 right-4 bg-green-500 text-white px-4 py-2 rounded-lg shadow-lg z-50",t.textContent=e,document.body.appendChild(t),setTimeout(()=>{t.remove()},3e3)}});window.Alpine=n;n.start();
