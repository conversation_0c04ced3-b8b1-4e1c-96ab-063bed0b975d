# 🛠 Admin Dashboard – Liquid Lights Ticketing Platform

The Admin Dashboard is the control center of the Liquid Lights ticketing platform, providing real-time data, event controls, user management, and analytics. It’s role-based and modular, built for ease of use, modern aesthetics, and operational efficiency.

---

## 🎨 Dashboard UI Overview

* Modern, responsive layout (dark/light mode toggle)
* Dashboard sidebar navigation with icons
* Stats overview cards
* Charts (Bar, Line, Pie) with filters
* Alerts, quick actions, recent activity feed

---

## 👥 Role-Based Access

| Role          | Access Level                            |
| ------------- | --------------------------------------- |
| Super Admin   | Full access to all modules, settings    |
| Manager       | Event creation, booking view, campaigns |
| Ticket Booker | Manual bookings only                    |
| Scanner       | QR scan interface only                  |

---

## 📊 Dashboard Home (Overview Page)

* **KPIs:** Total Tickets Sold, Revenue, Active Events, Daily Bookings
* **Charts:**

  * Daily/weekly/monthly bookings
  * Event popularity (bar/pie)
  * Location heatmap (Google Maps)
* **Quick Actions:**

  * Create Event
  * View Bookings
  * Send Campaign
  * Export Data (CSV/PDF)
* **Alerts:**

  * Low ticket inventory
  * Upcoming events in 24–72 hours
  * Failed payment logs

---

## 📅 Event Management Module

* Create/edit/delete events
* Set event date/time/location
* Add ticket types, price, quantity
* Upload banners, galleries
* SEO settings (title, meta, slug)
* Toggle features: PWA save, WhatsApp reminder, seat limit
* Link artists/performers to events

---

## 🎟 Booking Management

* View all bookings by filter (event, user, status, date)
* Booking details: status, QR preview, refund/cancel options
* Export bookings (CSV, PDF)
* Resend ticket (WhatsApp/Email)
* Manual booking entry
* Refund handling (status: Paid / Refunded / Cancelled)

---

## 👤 User Management

* View users by phone/email
* Booking history per user
* Blacklist/unblock users
* Admin user management (create/edit/delete roles)

---

## 📷 QR Scanner Logs

* List of all scan attempts
* Fields: Ticket ID, Event, Timestamp, Success/Error, Device
* Duplicate ticket flag
* Location capture (optional)

---

## ⚙️ API & System Settings

* Razorpay/PhonePe API keys
* WhatsApp API (Interakt, Gupshup)
* OTPless magic link config
* Email SMTP settings (SendGrid, Mailgun)
* SMS Gateway (Msg91, Twilio)
* Google Maps, Analytics, Pixel

---

## 🔔 Notification Center

* Create/send:

  * WhatsApp message (event reminder, confirmation)
  * SMS blast
  * Push notification (OneSignal)
* View delivery stats (sent, failed, opened)

---

## 💰 Promo Codes & Coupons

* Create coupons with expiration and usage limits
* Track redemptions
* Apply to specific events or ticket types

---

## 🧱 CMS – Page Builder

* Drag-and-drop interface for custom landing pages
* Reusable content blocks (text, image, video, buttons, forms)
* Save as draft, preview, and publish workflow
* SEO-friendly editor (meta tags, custom slugs)
* Design templates for event-specific or promotional pages
* Version control and rollback support

---

## 📄 Reports & Analytics

* Downloadable reports: revenue, bookings, refund logs
* Filter by event/date/status
* Charts for campaign performance

Let me know if you want a mock UI wireframe or React components for this dashboard.
