<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Str;

class OTPlessController extends Controller
{
    /**
     * Show OTPless login page
     */
    public function showLogin()
    {
        return view('auth.otpless-login');
    }

    /**
     * Initiate OTPless authentication
     */
    public function initiate(Request $request)
    {
        $request->validate([
            'phone' => 'required|string|min:10|max:15',
        ]);

        if (!config('services.otpless.enabled', false)) {
            return response()->json([
                'success' => false,
                'message' => 'OTPless authentication is not enabled'
            ], 400);
        }

        try {
            // For demo purposes, we'll simulate OTPless integration
            // In production, you would integrate with actual OTPless API
            
            $phone = $request->phone;
            
            // Simulate OTPless API call
            $response = $this->simulateOTPlessAPI($phone);
            
            if ($response['success']) {
                return response()->json([
                    'success' => true,
                    'message' => 'OTP sent successfully',
                    'request_id' => $response['request_id']
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to send OTP'
                ], 400);
            }
            
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Authentication service error'
            ], 500);
        }
    }

    /**
     * Verify OTP and complete authentication
     */
    public function verify(Request $request)
    {
        $request->validate([
            'phone' => 'required|string',
            'otp' => 'required|string|size:6',
            'request_id' => 'required|string',
        ]);

        try {
            // Simulate OTP verification
            $verification = $this->simulateOTPVerification($request->otp, $request->request_id);
            
            if ($verification['success']) {
                // Find or create user
                $user = User::where('phone', $request->phone)->first();
                
                if (!$user) {
                    $user = User::create([
                        'name' => 'User ' . substr($request->phone, -4),
                        'phone' => $request->phone,
                        'email' => null, // OTPless doesn't require email
                        'password' => bcrypt(Str::random(32)), // Random password
                        'role' => 'customer',
                        'is_active' => true,
                        'phone_verified_at' => now(),
                    ]);
                }

                // Log the user in
                Auth::login($user);

                return response()->json([
                    'success' => true,
                    'message' => 'Authentication successful',
                    'redirect_url' => route('user.dashboard')
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => 'Invalid OTP'
                ], 400);
            }
            
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Verification failed'
            ], 500);
        }
    }

    /**
     * Handle OTPless callback (for web-based flow)
     */
    public function callback(Request $request)
    {
        // Handle OTPless callback for web-based authentication
        // This would be used for redirect-based OTPless flow
        
        try {
            $token = $request->get('token');
            $phone = $request->get('phone');
            
            if ($token && $phone) {
                // Verify token with OTPless API
                // For demo, we'll accept any token
                
                $user = User::where('phone', $phone)->first();
                
                if (!$user) {
                    $user = User::create([
                        'name' => 'User ' . substr($phone, -4),
                        'phone' => $phone,
                        'email' => null,
                        'password' => bcrypt(Str::random(32)),
                        'role' => 'customer',
                        'is_active' => true,
                        'phone_verified_at' => now(),
                    ]);
                }

                Auth::login($user);
                
                return redirect()->route('user.dashboard')->with('success', 'Login successful!');
            }
            
            return redirect()->route('login')->with('error', 'Authentication failed');
            
        } catch (\Exception $e) {
            return redirect()->route('login')->with('error', 'Authentication error');
        }
    }

    /**
     * Simulate OTPless API call (for demo)
     */
    private function simulateOTPlessAPI($phone)
    {
        // In production, this would be an actual API call to OTPless
        return [
            'success' => true,
            'request_id' => 'req_' . Str::random(16),
            'message' => 'OTP sent successfully'
        ];
    }

    /**
     * Simulate OTP verification (for demo)
     */
    private function simulateOTPVerification($otp, $requestId)
    {
        // For demo purposes, accept any 6-digit OTP
        // In production, this would verify with OTPless API
        return [
            'success' => strlen($otp) === 6 && is_numeric($otp),
            'message' => strlen($otp) === 6 && is_numeric($otp) ? 'OTP verified' : 'Invalid OTP'
        ];
    }
}
