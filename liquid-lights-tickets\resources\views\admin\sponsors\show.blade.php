@extends('admin.layout')

@section('title', $sponsor->name . ' - Sponsor Details')

@section('content')
<div class="space-y-6">
    <!-- Header -->
    <div class="flex justify-between items-center">
        <div>
            <h1 class="text-2xl font-bold text-gray-900">{{ $sponsor->name }}</h1>
            <p class="text-gray-600">Sponsor Details and Performance</p>
        </div>
        <div class="flex space-x-3">
            <a href="{{ route('admin.sponsors.edit', $sponsor) }}" 
               class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium">
                Edit Sponsor
            </a>
            <a href="{{ route('admin.sponsor-analytics.sponsor-report', $sponsor) }}" 
               class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg font-medium">
                View Analytics
            </a>
            <a href="{{ route('admin.sponsors.index') }}" 
               class="bg-gray-300 hover:bg-gray-400 text-gray-700 px-4 py-2 rounded-lg font-medium">
                Back to Sponsors
            </a>
        </div>
    </div>

    <!-- Sponsor Information -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Basic Info -->
        <div class="lg:col-span-2 bg-white p-6 rounded-lg shadow-sm border border-gray-200">
            <h2 class="text-lg font-semibold text-gray-900 mb-4">Sponsor Information</h2>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Name</label>
                    <p class="text-gray-900">{{ $sponsor->name }}</p>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Status</label>
                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full
                        {{ $sponsor->status === 'active' ? 'bg-green-100 text-green-800' : 
                           ($sponsor->status === 'pending' ? 'bg-yellow-100 text-yellow-800' : 'bg-red-100 text-red-800') }}">
                        {{ ucfirst($sponsor->status) }}
                    </span>
                </div>

                @if($sponsor->description)
                <div class="md:col-span-2">
                    <label class="block text-sm font-medium text-gray-700 mb-1">Description</label>
                    <p class="text-gray-900">{{ $sponsor->description }}</p>
                </div>
                @endif

                @if($sponsor->website_url)
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Website</label>
                    <a href="{{ $sponsor->formatted_website_url }}" target="_blank" 
                       class="text-blue-600 hover:text-blue-800">{{ $sponsor->website_url }}</a>
                </div>
                @endif

                @if($sponsor->contact_person)
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Contact Person</label>
                    <p class="text-gray-900">{{ $sponsor->contact_person }}</p>
                </div>
                @endif

                @if($sponsor->contact_email)
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Contact Email</label>
                    <a href="mailto:{{ $sponsor->contact_email }}" 
                       class="text-blue-600 hover:text-blue-800">{{ $sponsor->contact_email }}</a>
                </div>
                @endif

                @if($sponsor->contact_phone)
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Contact Phone</label>
                    <a href="tel:{{ $sponsor->contact_phone }}" 
                       class="text-blue-600 hover:text-blue-800">{{ $sponsor->contact_phone }}</a>
                </div>
                @endif

                @if($sponsor->address)
                <div class="md:col-span-2">
                    <label class="block text-sm font-medium text-gray-700 mb-1">Address</label>
                    <p class="text-gray-900">{{ $sponsor->address }}</p>
                </div>
                @endif
            </div>

            <!-- Social Media Links -->
            @if($sponsor->social_links && array_filter($sponsor->social_links))
            <div class="mt-6 pt-6 border-t border-gray-200">
                <label class="block text-sm font-medium text-gray-700 mb-3">Social Media</label>
                <div class="flex space-x-4">
                    @if($sponsor->social_links['facebook'])
                        <a href="{{ $sponsor->social_links['facebook'] }}" target="_blank" 
                           class="text-blue-600 hover:text-blue-800">
                            <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                            </svg>
                        </a>
                    @endif
                    
                    @if($sponsor->social_links['twitter'])
                        <a href="{{ $sponsor->social_links['twitter'] }}" target="_blank" 
                           class="text-blue-400 hover:text-blue-600">
                            <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"/>
                            </svg>
                        </a>
                    @endif
                    
                    @if($sponsor->social_links['instagram'])
                        <a href="{{ $sponsor->social_links['instagram'] }}" target="_blank" 
                           class="text-pink-600 hover:text-pink-800">
                            <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M12.017 0C5.396 0 .029 5.367.029 11.987c0 6.62 5.367 11.987 11.988 11.987 6.62 0 11.987-5.367 11.987-11.987C24.014 5.367 18.637.001 12.017.001zM8.449 16.988c-1.297 0-2.448-.49-3.323-1.297C4.198 14.895 3.708 13.744 3.708 12.447s.49-2.448 1.418-3.323c.928-.875 2.026-1.365 3.323-1.365s2.448.49 3.323 1.365c.928.875 1.418 2.026 1.418 3.323s-.49 2.448-1.418 3.244c-.875.807-2.026 1.297-3.323 1.297zm7.83-9.608c-.384 0-.734-.15-.993-.409-.259-.259-.409-.609-.409-.993 0-.384.15-.734.409-.993.259-.259.609-.409.993-.409s.734.15.993.409c.259.259.409.609.409.993 0 .384-.15.734-.409.993-.259.259-.609.409-.993.409z"/>
                            </svg>
                        </a>
                    @endif
                    
                    @if($sponsor->social_links['linkedin'])
                        <a href="{{ $sponsor->social_links['linkedin'] }}" target="_blank" 
                           class="text-blue-700 hover:text-blue-900">
                            <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                            </svg>
                        </a>
                    @endif
                </div>
            </div>
            @endif
        </div>

        <!-- Logo and Stats -->
        <div class="space-y-6">
            <!-- Logo -->
            <div class="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Logo</h3>
                @if($sponsor->logo)
                    <img src="{{ $sponsor->logo_url }}" alt="{{ $sponsor->name }}" 
                         class="w-full h-32 object-contain bg-gray-50 rounded-lg">
                @else
                    <div class="w-full h-32 bg-gray-100 rounded-lg flex items-center justify-center">
                        <svg class="w-12 h-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                        </svg>
                    </div>
                @endif
            </div>

            <!-- Quick Stats -->
            <div class="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Quick Stats</h3>
                <div class="space-y-4">
                    <div class="flex justify-between">
                        <span class="text-gray-600">Total Events</span>
                        <span class="font-semibold">{{ $stats['total_events'] }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-600">Active Events</span>
                        <span class="font-semibold">{{ $stats['active_events'] }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-600">Upcoming Events</span>
                        <span class="font-semibold">{{ $stats['upcoming_events'] }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-600">Total Investment</span>
                        <span class="font-semibold">₹{{ number_format($stats['total_amount']) }}</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Sponsorships -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
        <div class="p-6 border-b border-gray-200">
            <h3 class="text-lg font-semibold text-gray-900">Sponsorships</h3>
        </div>
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Event</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Tier</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    @forelse($sponsor->eventSponsors as $sponsorship)
                    <tr class="hover:bg-gray-50">
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm font-medium text-gray-900">{{ $sponsorship->event->title }}</div>
                            <div class="text-sm text-gray-500">{{ $sponsorship->event->event_date->format('M d, Y') }}</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full" 
                                  style="background-color: {{ $sponsorship->sponsorshipTier->color }}20; color: {{ $sponsorship->sponsorshipTier->color }}">
                                {{ $sponsorship->sponsorshipTier->name }}
                            </span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {{ $sponsorship->formatted_amount }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full
                                {{ $sponsorship->status === 'active' ? 'bg-green-100 text-green-800' : 
                                   ($sponsorship->status === 'pending' ? 'bg-yellow-100 text-yellow-800' : 'bg-red-100 text-red-800') }}">
                                {{ ucfirst($sponsorship->status) }}
                            </span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {{ $sponsorship->created_at->format('M d, Y') }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                            <a href="{{ route('admin.event-sponsors.show', $sponsorship) }}" 
                               class="text-blue-600 hover:text-blue-900">View</a>
                        </td>
                    </tr>
                    @empty
                    <tr>
                        <td colspan="6" class="px-6 py-12 text-center text-gray-500">
                            No sponsorships found for this sponsor
                        </td>
                    </tr>
                    @endforelse
                </tbody>
            </table>
        </div>
    </div>

    @if($sponsor->notes)
    <!-- Internal Notes -->
    <div class="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">Internal Notes</h3>
        <p class="text-gray-700">{{ $sponsor->notes }}</p>
    </div>
    @endif
</div>
@endsection
