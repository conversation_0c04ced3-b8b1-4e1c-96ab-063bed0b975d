<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class EventSponsor extends Model
{
    use HasFactory;

    protected $fillable = [
        'event_id',
        'sponsor_id',
        'sponsorship_tier_id',
        'amount',
        'start_date',
        'end_date',
        'status',
        'custom_benefits',
        'notes',
        'display_order'
    ];

    protected $casts = [
        'amount' => 'decimal:2',
        'start_date' => 'date',
        'end_date' => 'date',
        'custom_benefits' => 'array',
        'display_order' => 'integer'
    ];

    /**
     * Get the event
     */
    public function event()
    {
        return $this->belongsTo(Event::class);
    }

    /**
     * Get the sponsor
     */
    public function sponsor()
    {
        return $this->belongsTo(Sponsor::class);
    }

    /**
     * Get the sponsorship tier
     */
    public function sponsorshipTier()
    {
        return $this->belongsTo(SponsorshipTier::class);
    }

    /**
     * Scope for active sponsorships
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * Scope for current sponsorships (within date range)
     */
    public function scopeCurrent($query)
    {
        return $query->where(function ($q) {
            $q->whereNull('start_date')
              ->orWhere('start_date', '<=', now());
        })->where(function ($q) {
            $q->whereNull('end_date')
              ->orWhere('end_date', '>=', now());
        });
    }

    /**
     * Scope for ordered display
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('display_order')
            ->orderBy('created_at');
    }

    /**
     * Check if sponsorship is currently active
     */
    public function isCurrentlyActive()
    {
        if ($this->status !== 'active') {
            return false;
        }

        $now = now();

        if ($this->start_date && $this->start_date > $now) {
            return false;
        }

        if ($this->end_date && $this->end_date < $now) {
            return false;
        }

        return true;
    }

    /**
     * Get all benefits (tier + custom)
     */
    public function getAllBenefits()
    {
        $tierBenefits = $this->sponsorshipTier->benefits ?? [];
        $customBenefits = $this->custom_benefits ?? [];

        return array_merge($tierBenefits, $customBenefits);
    }

    /**
     * Get formatted amount
     */
    public function getFormattedAmountAttribute()
    {
        return $this->amount ? '₹' . number_format($this->amount) : 'N/A';
    }

    /**
     * Get status badge color
     */
    public function getStatusColorAttribute()
    {
        return match($this->status) {
            'active' => 'green',
            'pending' => 'yellow',
            'expired' => 'red',
            'inactive' => 'gray',
            default => 'gray'
        };
    }

    /**
     * Get duration in days
     */
    public function getDurationInDays()
    {
        if (!$this->start_date || !$this->end_date) {
            return null;
        }

        return $this->start_date->diffInDays($this->end_date);
    }
}
