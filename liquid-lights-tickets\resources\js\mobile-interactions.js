// Mobile Interactions Manager for Liquid Lights Tickets PWA
class MobileInteractionsManager {
    constructor() {
        this.isTouch = 'ontouchstart' in window;
        this.isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent);
        this.isAndroid = /Android/.test(navigator.userAgent);
        this.supportsHaptic = 'vibrate' in navigator;
        
        this.swipeThreshold = 50;
        this.pullThreshold = 80;
        
        this.init();
    }

    init() {
        this.setupTouchFeedback();
        this.setupSwipeGestures();
        this.setupPullToRefresh();
        this.setupMobileNavigation();
        this.setupHapticFeedback();
        this.setupSafeAreaHandling();
        this.setupModalEnhancements();
        
        console.log('Mobile interactions manager initialized');
    }

    // Touch feedback for buttons and interactive elements
    setupTouchFeedback() {
        document.addEventListener('touchstart', (e) => {
            const element = e.target.closest('.touch-feedback');
            if (element) {
                element.classList.add('touching');
                this.triggerHaptic('light');
            }
        });

        document.addEventListener('touchend', (e) => {
            const element = e.target.closest('.touch-feedback');
            if (element) {
                element.classList.remove('touching');
            }
        });

        document.addEventListener('touchcancel', (e) => {
            const element = e.target.closest('.touch-feedback');
            if (element) {
                element.classList.remove('touching');
            }
        });
    }

    // Swipe gestures for cards and lists
    setupSwipeGestures() {
        let startX, startY, currentX, currentY;
        let isSwipeActive = false;
        let swipeElement = null;

        document.addEventListener('touchstart', (e) => {
            const element = e.target.closest('.swipeable-card');
            if (!element) return;

            swipeElement = element;
            startX = e.touches[0].clientX;
            startY = e.touches[0].clientY;
            isSwipeActive = true;
            
            element.classList.add('swiping');
        });

        document.addEventListener('touchmove', (e) => {
            if (!isSwipeActive || !swipeElement) return;

            currentX = e.touches[0].clientX;
            currentY = e.touches[0].clientY;
            
            const deltaX = currentX - startX;
            const deltaY = currentY - startY;
            
            // Only allow horizontal swipes
            if (Math.abs(deltaY) > Math.abs(deltaX)) {
                this.resetSwipe();
                return;
            }
            
            e.preventDefault();
            
            // Apply transform
            swipeElement.style.transform = `translateX(${deltaX}px)`;
            
            // Add visual feedback
            if (Math.abs(deltaX) > this.swipeThreshold) {
                if (deltaX > 0) {
                    swipeElement.classList.add('swipe-right');
                    swipeElement.classList.remove('swipe-left');
                } else {
                    swipeElement.classList.add('swipe-left');
                    swipeElement.classList.remove('swipe-right');
                }
            } else {
                swipeElement.classList.remove('swipe-left', 'swipe-right');
            }
        });

        document.addEventListener('touchend', (e) => {
            if (!isSwipeActive || !swipeElement) return;

            const deltaX = currentX - startX;
            
            if (Math.abs(deltaX) > this.swipeThreshold) {
                this.handleSwipeAction(swipeElement, deltaX > 0 ? 'right' : 'left');
                this.triggerHaptic('medium');
            }
            
            this.resetSwipe();
        });
    }

    resetSwipe() {
        if (swipeElement) {
            swipeElement.classList.remove('swiping', 'swipe-left', 'swipe-right');
            swipeElement.style.transform = '';
            swipeElement = null;
        }
        isSwipeActive = false;
    }

    handleSwipeAction(element, direction) {
        const event = new CustomEvent('swipe', {
            detail: { direction, element }
        });
        element.dispatchEvent(event);
        
        // Default actions
        if (direction === 'left') {
            // Could trigger delete or archive action
            console.log('Swiped left on:', element);
        } else if (direction === 'right') {
            // Could trigger favorite or bookmark action
            console.log('Swiped right on:', element);
        }
    }

    // Pull to refresh functionality
    setupPullToRefresh() {
        let startY, currentY;
        let isPulling = false;
        let pullElement = null;

        document.addEventListener('touchstart', (e) => {
            const element = e.target.closest('.pull-to-refresh');
            if (!element || window.scrollY > 0) return;

            pullElement = element;
            startY = e.touches[0].clientY;
            isPulling = true;
        });

        document.addEventListener('touchmove', (e) => {
            if (!isPulling || !pullElement || window.scrollY > 0) return;

            currentY = e.touches[0].clientY;
            const deltaY = currentY - startY;
            
            if (deltaY > 0) {
                e.preventDefault();
                
                const pullDistance = Math.min(deltaY, this.pullThreshold * 1.5);
                pullElement.style.transform = `translateY(${pullDistance * 0.5}px)`;
                
                if (pullDistance > this.pullThreshold) {
                    pullElement.classList.add('pulling');
                } else {
                    pullElement.classList.remove('pulling');
                }
            }
        });

        document.addEventListener('touchend', (e) => {
            if (!isPulling || !pullElement) return;

            const deltaY = currentY - startY;
            
            if (deltaY > this.pullThreshold) {
                this.triggerRefresh(pullElement);
                this.triggerHaptic('heavy');
            }
            
            this.resetPull();
        });
    }

    resetPull() {
        if (pullElement) {
            pullElement.style.transform = '';
            pullElement.classList.remove('pulling');
            pullElement = null;
        }
        isPulling = false;
    }

    triggerRefresh(element) {
        element.classList.add('refreshing');
        
        const event = new CustomEvent('pullrefresh');
        element.dispatchEvent(event);
        
        // Auto-reset after 2 seconds if not manually reset
        setTimeout(() => {
            if (element.classList.contains('refreshing')) {
                this.resetRefresh(element);
            }
        }, 2000);
    }

    resetRefresh(element) {
        element.classList.remove('refreshing', 'pulling');
        element.style.transform = '';
    }

    // Mobile navigation enhancements
    setupMobileNavigation() {
        // Add mobile navigation if it doesn't exist
        if (!document.querySelector('.mobile-nav') && window.innerWidth <= 768) {
            this.createMobileNavigation();
        }
        
        // Handle navigation item clicks
        document.addEventListener('click', (e) => {
            const navItem = e.target.closest('.mobile-nav-item');
            if (navItem) {
                this.triggerHaptic('light');
                
                // Update active state
                document.querySelectorAll('.mobile-nav-item').forEach(item => {
                    item.classList.remove('active');
                });
                navItem.classList.add('active');
            }
        });
    }

    createMobileNavigation() {
        const nav = document.createElement('nav');
        nav.className = 'mobile-nav safe-area-bottom';
        nav.innerHTML = `
            <div class="flex justify-around">
                <a href="/" class="mobile-nav-item ${window.location.pathname === '/' ? 'active' : ''}">
                    <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"></path>
                    </svg>
                    <span>Home</span>
                </a>
                <a href="/events" class="mobile-nav-item ${window.location.pathname.includes('/events') ? 'active' : ''}">
                    <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                    </svg>
                    <span>Events</span>
                </a>
                <a href="/user/bookings" class="mobile-nav-item ${window.location.pathname.includes('/bookings') ? 'active' : ''}">
                    <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 5v2m0 4v2m0 4v2M5 5a2 2 0 00-2 2v3a2 2 0 110 4v3a2 2 0 002 2h14a2 2 0 002-2v-3a2 2 0 110-4V7a2 2 0 00-2-2H5z"></path>
                    </svg>
                    <span>Tickets</span>
                </a>
                <a href="/user/dashboard" class="mobile-nav-item ${window.location.pathname.includes('/dashboard') ? 'active' : ''}">
                    <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                    </svg>
                    <span>Profile</span>
                </a>
            </div>
        `;
        
        document.body.appendChild(nav);
    }

    // Haptic feedback
    setupHapticFeedback() {
        // Add haptic feedback to buttons
        document.addEventListener('click', (e) => {
            const element = e.target.closest('.haptic-light, .haptic-medium, .haptic-heavy');
            if (element) {
                if (element.classList.contains('haptic-light')) {
                    this.triggerHaptic('light');
                } else if (element.classList.contains('haptic-medium')) {
                    this.triggerHaptic('medium');
                } else if (element.classList.contains('haptic-heavy')) {
                    this.triggerHaptic('heavy');
                }
            }
        });
    }

    triggerHaptic(intensity = 'light') {
        if (!this.supportsHaptic) return;

        const patterns = {
            light: [10],
            medium: [20],
            heavy: [50]
        };

        navigator.vibrate(patterns[intensity] || patterns.light);
    }

    // Safe area handling for notched devices
    setupSafeAreaHandling() {
        // Add safe area classes to relevant elements
        const header = document.querySelector('header');
        if (header) {
            header.classList.add('safe-area-top');
        }

        const mobileNav = document.querySelector('.mobile-nav');
        if (mobileNav) {
            mobileNav.classList.add('safe-area-bottom');
        }
    }

    // Enhanced modal behavior for mobile
    setupModalEnhancements() {
        document.addEventListener('click', (e) => {
            const modalTrigger = e.target.closest('[data-modal-target]');
            if (modalTrigger) {
                const targetId = modalTrigger.dataset.modalTarget;
                const modal = document.getElementById(targetId);
                if (modal) {
                    this.showMobileModal(modal);
                }
            }
        });

        // Handle modal close
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('mobile-modal')) {
                this.hideMobileModal(e.target);
            }
            
            const closeButton = e.target.closest('[data-modal-close]');
            if (closeButton) {
                const modal = closeButton.closest('.mobile-modal');
                if (modal) {
                    this.hideMobileModal(modal);
                }
            }
        });
    }

    showMobileModal(modal) {
        modal.classList.add('mobile-modal', 'show');
        document.body.classList.add('no-scroll');
        this.triggerHaptic('light');
        
        // Focus management
        const firstFocusable = modal.querySelector('button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])');
        if (firstFocusable) {
            firstFocusable.focus();
        }
    }

    hideMobileModal(modal) {
        modal.classList.remove('show');
        document.body.classList.remove('no-scroll');
        
        setTimeout(() => {
            modal.classList.remove('mobile-modal');
        }, 300);
    }

    // Utility methods
    isLandscape() {
        return window.innerWidth > window.innerHeight;
    }

    getViewportHeight() {
        return window.innerHeight || document.documentElement.clientHeight;
    }

    getViewportWidth() {
        return window.innerWidth || document.documentElement.clientWidth;
    }

    // Public API
    enablePullToRefresh(element, callback) {
        element.classList.add('pull-to-refresh');
        element.addEventListener('pullrefresh', callback);
    }

    enableSwipeGestures(element, callbacks) {
        element.classList.add('swipeable-card');
        if (callbacks.onSwipeLeft) {
            element.addEventListener('swipe', (e) => {
                if (e.detail.direction === 'left') {
                    callbacks.onSwipeLeft(e.detail.element);
                }
            });
        }
        if (callbacks.onSwipeRight) {
            element.addEventListener('swipe', (e) => {
                if (e.detail.direction === 'right') {
                    callbacks.onSwipeRight(e.detail.element);
                }
            });
        }
    }

    addTouchFeedback(element) {
        element.classList.add('touch-feedback');
    }
}

// Initialize mobile interactions
let mobileManager;

document.addEventListener('DOMContentLoaded', () => {
    mobileManager = new MobileInteractionsManager();
    
    // Add body padding for mobile nav
    if (window.innerWidth <= 768) {
        document.body.style.paddingBottom = '80px';
    }
});

// Handle orientation changes
window.addEventListener('orientationchange', () => {
    setTimeout(() => {
        // Recalculate viewport dimensions
        const vh = window.innerHeight * 0.01;
        document.documentElement.style.setProperty('--vh', `${vh}px`);
    }, 100);
});

// Export for global access
window.mobileManager = mobileManager;
