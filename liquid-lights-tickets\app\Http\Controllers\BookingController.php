<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use App\Models\Booking;
use App\Models\Event;
use App\Models\TicketType;
use App\Services\EmailNotificationService;
use App\Services\TicketService;
use Carbon\Carbon;

class BookingController extends Controller
{
    protected $emailService;
    protected $ticketService;

    public function __construct(EmailNotificationService $emailService, TicketService $ticketService)
    {
        $this->emailService = $emailService;
        $this->ticketService = $ticketService;
    }
    /**
     * Show cart page
     */
    public function cart()
    {
        return view('booking.cart');
    }

    /**
     * Show checkout page
     */
    public function checkout()
    {
        if (!Auth::check()) {
            return redirect()->route('login')->with('info', 'Please sign in to continue with your booking.');
        }

        return view('booking.checkout');
    }

    /**
     * Process booking
     */
    public function processBooking(Request $request)
    {
        if (!Auth::check()) {
            return response()->json([
                'success' => false,
                'message' => 'Please sign in to complete your booking.'
            ], 401);
        }

        $validator = Validator::make($request->all(), [
            'items' => 'required|array|min:1',
            'items.*.event_id' => 'required|exists:events,id',
            'items.*.ticket_type_id' => 'required|exists:ticket_types,id',
            'items.*.quantity' => 'required|integer|min:1',
            'payment_method' => 'required|string|in:razorpay,stripe,wallet',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $user = Auth::user();
        $bookings = [];
        $totalAmount = 0;

        // Validate and create bookings
        foreach ($request->items as $item) {
            $event = Event::findOrFail($item['event_id']);
            $ticketType = TicketType::findOrFail($item['ticket_type_id']);

            // Check if event is still available for booking
            if ($event->status !== 'published' || $event->event_date < Carbon::now()) {
                return response()->json([
                    'success' => false,
                    'message' => "Event '{$event->title}' is no longer available for booking."
                ], 400);
            }

            // Check ticket availability
            if ($ticketType->available_quantity < $item['quantity']) {
                return response()->json([
                    'success' => false,
                    'message' => "Only {$ticketType->available_quantity} tickets available for '{$ticketType->name}' in '{$event->title}'."
                ], 400);
            }

            // Check if ticket sales are active
            $now = Carbon::now();
            if ($now < $ticketType->sale_start_date || $now > $ticketType->sale_end_date) {
                return response()->json([
                    'success' => false,
                    'message' => "Ticket sales for '{$ticketType->name}' are not currently active."
                ], 400);
            }

            $itemTotal = $ticketType->price * $item['quantity'];
            $totalAmount += $itemTotal;

            // Create booking
            $booking = Booking::create([
                'booking_reference' => 'BK' . strtoupper(Str::random(8)),
                'user_id' => $user->id,
                'event_id' => $event->id,
                'ticket_type_id' => $ticketType->id,
                'quantity' => $item['quantity'],
                'total_amount' => $itemTotal,
                'payment_status' => 'pending',
                'payment_method' => $request->payment_method,
                'qr_code_hash' => Str::random(32),
            ]);

            $bookings[] = $booking;
        }

        // Here you would integrate with payment gateway
        // For now, we'll simulate a successful payment
        foreach ($bookings as $booking) {
            $booking->update([
                'payment_status' => 'paid',
                'payment_id' => 'pay_' . strtoupper(Str::random(16)),
            ]);

            // Update ticket type sold quantity
            $booking->ticketType->increment('quantity_sold', $booking->quantity);

            // Generate ticket PDF
            try {
                $this->ticketService->generateTicketPDF($booking);
            } catch (\Exception $e) {
                // Log error but don't fail the booking
                \Log::error('Failed to generate ticket PDF', [
                    'booking_id' => $booking->id,
                    'error' => $e->getMessage()
                ]);
            }

            // Send confirmation email
            try {
                $this->emailService->sendBookingConfirmation($booking);
                $this->emailService->sendPaymentSuccess($booking);
            } catch (\Exception $e) {
                // Log error but don't fail the booking
                \Log::error('Failed to send booking emails', [
                    'booking_id' => $booking->id,
                    'error' => $e->getMessage()
                ]);
            }
        }

        return response()->json([
            'success' => true,
            'message' => 'Booking completed successfully!',
            'bookings' => $bookings->map(function($booking) {
                return [
                    'id' => $booking->id,
                    'booking_reference' => $booking->booking_reference,
                    'event_title' => $booking->event->title,
                    'ticket_type' => $booking->ticketType->name,
                    'quantity' => $booking->quantity,
                    'total_amount' => $booking->total_amount,
                ];
            }),
            'total_amount' => $totalAmount,
            'redirect_url' => route('user.booking', $bookings[0]->id)
        ]);
    }

    /**
     * Show booking confirmation
     */
    public function confirmation($bookingId)
    {
        if (!Auth::check()) {
            return redirect()->route('login');
        }

        $booking = Auth::user()->bookings()
            ->with(['event', 'ticketType'])
            ->findOrFail($bookingId);

        return view('booking.confirmation', compact('booking'));
    }

    /**
     * Cancel booking
     */
    public function cancel(Request $request, $bookingId)
    {
        if (!Auth::check()) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized'
            ], 401);
        }

        $booking = Auth::user()->bookings()->findOrFail($bookingId);

        // Check if booking can be cancelled
        if ($booking->payment_status === 'refunded') {
            return response()->json([
                'success' => false,
                'message' => 'Booking is already refunded.'
            ], 400);
        }

        if ($booking->event->event_date < Carbon::now()) {
            return response()->json([
                'success' => false,
                'message' => 'Cannot cancel booking for past events.'
            ], 400);
        }

        // Process refund (simplified)
        if ($booking->payment_status === 'paid') {
            $booking->update(['payment_status' => 'refunded']);
            $booking->ticketType->decrement('quantity_sold', $booking->quantity);
        } else {
            $booking->update(['payment_status' => 'cancelled']);
        }

        return response()->json([
            'success' => true,
            'message' => 'Booking cancelled successfully.'
        ]);
    }
}
