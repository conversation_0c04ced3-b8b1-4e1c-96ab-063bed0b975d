@extends('admin.layout')

@section('title', 'Create Event')

@section('content')
<div x-data="eventForm">
    <!-- <PERSON> Header -->
    <div class="mb-8">
        <nav class="flex" aria-label="Breadcrumb">
            <ol class="flex items-center space-x-4">
                <li>
                    <div>
                        <a href="{{ route('admin.dashboard') }}" class="text-gray-400 hover:text-gray-500">
                            <svg class="flex-shrink-0 h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                <path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z" />
                            </svg>
                            <span class="sr-only">Home</span>
                        </a>
                    </div>
                </li>
                <li>
                    <div class="flex items-center">
                        <svg class="flex-shrink-0 h-5 w-5 text-gray-300" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M5.555 17.776l8-16 .894.448-8 16-.894-.448z" />
                        </svg>
                        <a href="{{ route('admin.events.index') }}" class="ml-4 text-sm font-medium text-gray-500 hover:text-gray-700">Events</a>
                    </div>
                </li>
                <li>
                    <div class="flex items-center">
                        <svg class="flex-shrink-0 h-5 w-5 text-gray-300" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M5.555 17.776l8-16 .894.448-8 16-.894-.448z" />
                        </svg>
                        <span class="ml-4 text-sm font-medium text-gray-500">Create</span>
                    </div>
                </li>
            </ol>
        </nav>
        <div class="mt-4">
            <h1 class="text-2xl font-bold text-gray-900">Create New Event</h1>
            <p class="mt-1 text-sm text-gray-600">Fill in the details below to create a new event.</p>
        </div>
    </div>

    <!-- Form -->
    <form @submit.prevent="submitForm" class="space-y-8">
        <!-- Basic Information -->
        <div class="bg-white shadow rounded-lg">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">Basic Information</h3>
            </div>
            <div class="px-6 py-4 space-y-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label for="title" class="block text-sm font-medium text-gray-700">Event Title *</label>
                        <input x-model="form.title" 
                               type="text" 
                               id="title" 
                               required
                               class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                        <div x-show="errors.title" class="mt-1 text-sm text-red-600" x-text="errors.title"></div>
                    </div>
                    <div>
                        <label for="venue_name" class="block text-sm font-medium text-gray-700">Venue Name *</label>
                        <input x-model="form.venue_name" 
                               type="text" 
                               id="venue_name" 
                               required
                               class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                        <div x-show="errors.venue_name" class="mt-1 text-sm text-red-600" x-text="errors.venue_name"></div>
                    </div>
                </div>

                <div>
                    <label for="short_description" class="block text-sm font-medium text-gray-700">Short Description *</label>
                    <textarea x-model="form.short_description" 
                              id="short_description" 
                              rows="3" 
                              required
                              maxlength="500"
                              class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"></textarea>
                    <p class="mt-1 text-sm text-gray-500">Brief description for event cards and previews (max 500 characters)</p>
                    <div x-show="errors.short_description" class="mt-1 text-sm text-red-600" x-text="errors.short_description"></div>
                </div>

                <div>
                    <label for="description" class="block text-sm font-medium text-gray-700">Full Description *</label>
                    <textarea x-model="form.description" 
                              id="description" 
                              rows="6" 
                              required
                              class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"></textarea>
                    <p class="mt-1 text-sm text-gray-500">Detailed description of the event</p>
                    <div x-show="errors.description" class="mt-1 text-sm text-red-600" x-text="errors.description"></div>
                </div>

                <div>
                    <label for="venue_address" class="block text-sm font-medium text-gray-700">Venue Address *</label>
                    <textarea x-model="form.venue_address" 
                              id="venue_address" 
                              rows="3" 
                              required
                              class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"></textarea>
                    <div x-show="errors.venue_address" class="mt-1 text-sm text-red-600" x-text="errors.venue_address"></div>
                </div>
            </div>
        </div>

        <!-- Date & Time -->
        <div class="bg-white shadow rounded-lg">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">Date & Time</h3>
            </div>
            <div class="px-6 py-4">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label for="event_date" class="block text-sm font-medium text-gray-700">Event Date *</label>
                        <input x-model="form.event_date" 
                               type="date" 
                               id="event_date" 
                               required
                               :min="new Date().toISOString().split('T')[0]"
                               class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                        <div x-show="errors.event_date" class="mt-1 text-sm text-red-600" x-text="errors.event_date"></div>
                    </div>
                    <div>
                        <label for="event_time" class="block text-sm font-medium text-gray-700">Event Time *</label>
                        <input x-model="form.event_time" 
                               type="time" 
                               id="event_time" 
                               required
                               class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                        <div x-show="errors.event_time" class="mt-1 text-sm text-red-600" x-text="errors.event_time"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Ticket Types -->
        <div class="bg-white shadow rounded-lg">
            <div class="px-6 py-4 border-b border-gray-200">
                <div class="flex justify-between items-center">
                    <h3 class="text-lg font-medium text-gray-900">Ticket Types</h3>
                    <button type="button" 
                            @click="addTicketType()"
                            class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-blue-700 bg-blue-100 hover:bg-blue-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        <svg class="-ml-0.5 mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                        </svg>
                        Add Ticket Type
                    </button>
                </div>
            </div>
            <div class="px-6 py-4">
                <template x-for="(ticketType, index) in form.ticket_types" :key="index">
                    <div class="border border-gray-200 rounded-lg p-4 mb-4">
                        <div class="flex justify-between items-start mb-4">
                            <h4 class="text-md font-medium text-gray-900" x-text="`Ticket Type ${index + 1}`"></h4>
                            <button type="button" 
                                    @click="removeTicketType(index)"
                                    x-show="form.ticket_types.length > 1"
                                    class="text-red-600 hover:text-red-800">
                                <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                </svg>
                            </button>
                        </div>
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700">Name *</label>
                                <input x-model="ticketType.name" 
                                       type="text" 
                                       required
                                       placeholder="e.g., Early Bird, VIP"
                                       class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700">Price (₹) *</label>
                                <input x-model="ticketType.price" 
                                       type="number" 
                                       min="0" 
                                       step="0.01" 
                                       required
                                       class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700">Quantity *</label>
                                <input x-model="ticketType.quantity_available" 
                                       type="number" 
                                       min="1" 
                                       required
                                       class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700">Sale Start *</label>
                                <input x-model="ticketType.sale_start_date" 
                                       type="datetime-local" 
                                       required
                                       class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                            </div>
                            <div class="md:col-span-2 lg:col-span-4">
                                <label class="block text-sm font-medium text-gray-700">Sale End *</label>
                                <input x-model="ticketType.sale_end_date" 
                                       type="datetime-local" 
                                       required
                                       class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                            </div>
                        </div>
                    </div>
                </template>
            </div>
        </div>

        <!-- Settings -->
        <div class="bg-white shadow rounded-lg">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">Settings</h3>
            </div>
            <div class="px-6 py-4">
                <div class="flex items-center">
                    <input x-model="form.is_featured" 
                           id="is_featured" 
                           type="checkbox" 
                           class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                    <label for="is_featured" class="ml-2 block text-sm text-gray-900">
                        Featured Event
                    </label>
                </div>
                <p class="mt-1 text-sm text-gray-500">Featured events appear prominently on the homepage</p>
            </div>
        </div>

        <!-- Form Actions -->
        <div class="flex justify-end space-x-3">
            <a href="{{ route('admin.events.index') }}" 
               class="bg-white py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                Cancel
            </a>
            <button type="submit" 
                    :disabled="loading"
                    class="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed">
                <span x-show="!loading">Create Event</span>
                <span x-show="loading" class="flex items-center">
                    <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Creating...
                </span>
            </button>
        </div>
    </form>
</div>
@endsection
