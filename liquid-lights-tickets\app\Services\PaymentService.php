<?php

namespace App\Services;

use App\Models\Booking;
use App\Models\Payment;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Log;
use Exception;

class PaymentService
{
    protected $razorpayService;
    protected $stripeService;

    public function __construct(RazorpayService $razorpayService, StripeService $stripeService)
    {
        $this->razorpayService = $razorpayService;
        $this->stripeService = $stripeService;
    }

    /**
     * Create payment intent for booking
     */
    public function createPaymentIntent(array $bookings, string $paymentMethod, array $customerInfo)
    {
        $totalAmount = collect($bookings)->sum('total_amount');
        
        try {
            switch ($paymentMethod) {
                case 'razorpay':
                    return $this->razorpayService->createOrder($totalAmount, $bookings, $customerInfo);
                    
                case 'stripe':
                    return $this->stripeService->createPaymentIntent($totalAmount, $bookings, $customerInfo);
                    
                default:
                    throw new Exception('Unsupported payment method');
            }
        } catch (Exception $e) {
            Log::error('Payment intent creation failed', [
                'error' => $e->getMessage(),
                'payment_method' => $paymentMethod,
                'amount' => $totalAmount
            ]);
            
            throw $e;
        }
    }

    /**
     * Process payment completion
     */
    public function processPaymentSuccess(string $paymentId, string $paymentMethod, array $paymentData)
    {
        try {
            // Find payment record
            $payment = Payment::where('payment_id', $paymentId)->first();
            
            if (!$payment) {
                throw new Exception('Payment record not found');
            }

            // Verify payment with gateway
            $verified = false;
            switch ($paymentMethod) {
                case 'razorpay':
                    $verified = $this->razorpayService->verifyPayment($paymentData);
                    break;
                    
                case 'stripe':
                    $verified = $this->stripeService->verifyPayment($paymentData);
                    break;
            }

            if (!$verified) {
                throw new Exception('Payment verification failed');
            }

            // Update payment status
            $payment->update([
                'status' => 'completed',
                'gateway_response' => $paymentData,
                'completed_at' => now()
            ]);

            // Update associated bookings
            $bookingIds = json_decode($payment->booking_ids, true);
            Booking::whereIn('id', $bookingIds)->update([
                'payment_status' => 'paid',
                'payment_id' => $paymentId
            ]);

            // Update ticket quantities
            $bookings = Booking::whereIn('id', $bookingIds)->with('ticketType')->get();
            foreach ($bookings as $booking) {
                $booking->ticketType->increment('quantity_sold', $booking->quantity);
            }

            return [
                'success' => true,
                'payment' => $payment,
                'bookings' => $bookings
            ];

        } catch (Exception $e) {
            Log::error('Payment processing failed', [
                'error' => $e->getMessage(),
                'payment_id' => $paymentId,
                'payment_method' => $paymentMethod
            ]);

            throw $e;
        }
    }

    /**
     * Process payment failure
     */
    public function processPaymentFailure(string $paymentId, string $reason)
    {
        try {
            $payment = Payment::where('payment_id', $paymentId)->first();
            
            if ($payment) {
                $payment->update([
                    'status' => 'failed',
                    'failure_reason' => $reason,
                    'failed_at' => now()
                ]);

                // Update associated bookings
                $bookingIds = json_decode($payment->booking_ids, true);
                Booking::whereIn('id', $bookingIds)->update([
                    'payment_status' => 'failed'
                ]);
            }

            return ['success' => true];

        } catch (Exception $e) {
            Log::error('Payment failure processing failed', [
                'error' => $e->getMessage(),
                'payment_id' => $paymentId
            ]);

            throw $e;
        }
    }

    /**
     * Process refund
     */
    public function processRefund(Booking $booking, float $amount = null)
    {
        try {
            $refundAmount = $amount ?? $booking->total_amount;
            
            // Find payment record
            $payment = Payment::where('payment_id', $booking->payment_id)->first();
            
            if (!$payment) {
                throw new Exception('Payment record not found for refund');
            }

            // Process refund with gateway
            $refundResult = null;
            switch ($payment->payment_method) {
                case 'razorpay':
                    $refundResult = $this->razorpayService->processRefund($booking->payment_id, $refundAmount);
                    break;
                    
                case 'stripe':
                    $refundResult = $this->stripeService->processRefund($booking->payment_id, $refundAmount);
                    break;
                    
                default:
                    throw new Exception('Unsupported payment method for refund');
            }

            if ($refundResult['success']) {
                // Update booking status
                $booking->update([
                    'payment_status' => 'refunded',
                    'refund_amount' => $refundAmount,
                    'refunded_at' => now()
                ]);

                // Update ticket quantities
                $booking->ticketType->decrement('quantity_sold', $booking->quantity);

                return [
                    'success' => true,
                    'refund_id' => $refundResult['refund_id'],
                    'amount' => $refundAmount
                ];
            }

            throw new Exception('Refund processing failed');

        } catch (Exception $e) {
            Log::error('Refund processing failed', [
                'error' => $e->getMessage(),
                'booking_id' => $booking->id,
                'amount' => $amount
            ]);

            throw $e;
        }
    }

    /**
     * Get payment methods configuration
     */
    public function getPaymentMethods()
    {
        return [
            'razorpay' => [
                'enabled' => config('services.razorpay.enabled', true),
                'name' => 'Razorpay',
                'description' => 'Credit/Debit Card, UPI, Net Banking, Wallets',
                'logo' => '/images/razorpay-logo.png'
            ],
            'stripe' => [
                'enabled' => config('services.stripe.enabled', false),
                'name' => 'Stripe',
                'description' => 'Credit/Debit Cards',
                'logo' => '/images/stripe-logo.png'
            ]
        ];
    }
}
