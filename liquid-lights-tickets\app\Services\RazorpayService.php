<?php

namespace App\Services;

use App\Models\Payment;
use Razorpay\Api\Api;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Log;
use Exception;

class RazorpayService
{
    protected $api;
    protected $keyId;
    protected $keySecret;

    public function __construct()
    {
        $this->keyId = config('services.razorpay.key_id');
        $this->keySecret = config('services.razorpay.key_secret');
        $this->api = new Api($this->keyId, $this->keySecret);
    }

    /**
     * Create Razorpay order
     */
    public function createOrder(float $amount, array $bookings, array $customerInfo)
    {
        try {
            $orderData = [
                'receipt' => 'order_' . Str::random(10),
                'amount' => $amount * 100, // Convert to paise
                'currency' => 'INR',
                'notes' => [
                    'booking_count' => count($bookings),
                    'customer_email' => $customerInfo['email'],
                    'customer_phone' => $customerInfo['phone'] ?? ''
                ]
            ];

            $order = $this->api->order->create($orderData);

            // Create payment record
            $payment = Payment::create([
                'payment_id' => $order['id'],
                'payment_method' => 'razorpay',
                'amount' => $amount,
                'currency' => 'INR',
                'status' => 'pending',
                'booking_ids' => json_encode(collect($bookings)->pluck('id')->toArray()),
                'customer_info' => json_encode($customerInfo),
                'gateway_order_id' => $order['id'],
                'created_at' => now()
            ]);

            return [
                'success' => true,
                'order_id' => $order['id'],
                'amount' => $amount,
                'currency' => 'INR',
                'key_id' => $this->keyId,
                'payment_id' => $payment->id,
                'customer' => [
                    'name' => $customerInfo['name'],
                    'email' => $customerInfo['email'],
                    'contact' => $customerInfo['phone'] ?? ''
                ],
                'options' => [
                    'checkout' => [
                        'name' => config('app.name'),
                        'description' => 'Event Ticket Booking',
                        'image' => asset('images/logo.png'),
                        'theme' => [
                            'color' => '#3B82F6'
                        ]
                    ]
                ]
            ];

        } catch (Exception $e) {
            Log::error('Razorpay order creation failed', [
                'error' => $e->getMessage(),
                'amount' => $amount
            ]);

            throw new Exception('Failed to create payment order: ' . $e->getMessage());
        }
    }

    /**
     * Verify Razorpay payment
     */
    public function verifyPayment(array $paymentData)
    {
        try {
            $attributes = [
                'razorpay_order_id' => $paymentData['razorpay_order_id'],
                'razorpay_payment_id' => $paymentData['razorpay_payment_id'],
                'razorpay_signature' => $paymentData['razorpay_signature']
            ];

            $this->api->utility->verifyPaymentSignature($attributes);

            // Fetch payment details from Razorpay
            $payment = $this->api->payment->fetch($paymentData['razorpay_payment_id']);

            return [
                'verified' => true,
                'payment_details' => $payment->toArray()
            ];

        } catch (Exception $e) {
            Log::error('Razorpay payment verification failed', [
                'error' => $e->getMessage(),
                'payment_data' => $paymentData
            ]);

            return ['verified' => false];
        }
    }

    /**
     * Process refund
     */
    public function processRefund(string $paymentId, float $amount)
    {
        try {
            $refundData = [
                'amount' => $amount * 100, // Convert to paise
                'speed' => 'normal',
                'notes' => [
                    'reason' => 'Customer requested refund',
                    'processed_at' => now()->toISOString()
                ]
            ];

            $payment = $this->api->payment->fetch($paymentId);
            $refund = $payment->refund($refundData);

            return [
                'success' => true,
                'refund_id' => $refund['id'],
                'amount' => $amount,
                'status' => $refund['status']
            ];

        } catch (Exception $e) {
            Log::error('Razorpay refund failed', [
                'error' => $e->getMessage(),
                'payment_id' => $paymentId,
                'amount' => $amount
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Handle webhook
     */
    public function handleWebhook(array $payload, string $signature)
    {
        try {
            // Verify webhook signature
            $webhookSecret = config('services.razorpay.webhook_secret');
            $expectedSignature = hash_hmac('sha256', json_encode($payload), $webhookSecret);

            if (!hash_equals($expectedSignature, $signature)) {
                throw new Exception('Invalid webhook signature');
            }

            $event = $payload['event'];
            $paymentEntity = $payload['payload']['payment']['entity'];

            switch ($event) {
                case 'payment.captured':
                    return $this->handlePaymentCaptured($paymentEntity);
                    
                case 'payment.failed':
                    return $this->handlePaymentFailed($paymentEntity);
                    
                case 'refund.processed':
                    return $this->handleRefundProcessed($payload['payload']['refund']['entity']);
                    
                default:
                    Log::info('Unhandled Razorpay webhook event', ['event' => $event]);
                    return ['success' => true];
            }

        } catch (Exception $e) {
            Log::error('Razorpay webhook handling failed', [
                'error' => $e->getMessage(),
                'payload' => $payload
            ]);

            throw $e;
        }
    }

    /**
     * Handle payment captured webhook
     */
    protected function handlePaymentCaptured(array $paymentEntity)
    {
        $payment = Payment::where('gateway_order_id', $paymentEntity['order_id'])->first();
        
        if ($payment && $payment->status === 'pending') {
            $payment->update([
                'status' => 'completed',
                'gateway_payment_id' => $paymentEntity['id'],
                'gateway_response' => $paymentEntity,
                'completed_at' => now()
            ]);

            // Update bookings
            $bookingIds = json_decode($payment->booking_ids, true);
            \App\Models\Booking::whereIn('id', $bookingIds)->update([
                'payment_status' => 'paid',
                'payment_id' => $paymentEntity['id']
            ]);
        }

        return ['success' => true];
    }

    /**
     * Handle payment failed webhook
     */
    protected function handlePaymentFailed(array $paymentEntity)
    {
        $payment = Payment::where('gateway_order_id', $paymentEntity['order_id'])->first();
        
        if ($payment && $payment->status === 'pending') {
            $payment->update([
                'status' => 'failed',
                'failure_reason' => $paymentEntity['error_description'] ?? 'Payment failed',
                'gateway_response' => $paymentEntity,
                'failed_at' => now()
            ]);

            // Update bookings
            $bookingIds = json_decode($payment->booking_ids, true);
            \App\Models\Booking::whereIn('id', $bookingIds)->update([
                'payment_status' => 'failed'
            ]);
        }

        return ['success' => true];
    }

    /**
     * Handle refund processed webhook
     */
    protected function handleRefundProcessed(array $refundEntity)
    {
        // Update refund status in database if needed
        Log::info('Refund processed', ['refund' => $refundEntity]);
        
        return ['success' => true];
    }

    /**
     * Get payment details
     */
    public function getPaymentDetails(string $paymentId)
    {
        try {
            $payment = $this->api->payment->fetch($paymentId);
            return $payment->toArray();
        } catch (Exception $e) {
            Log::error('Failed to fetch payment details', [
                'error' => $e->getMessage(),
                'payment_id' => $paymentId
            ]);
            
            throw $e;
        }
    }
}
