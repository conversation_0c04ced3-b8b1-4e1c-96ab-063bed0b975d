<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Event Reminder</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f4f4f4;
        }
        .container {
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            border-bottom: 2px solid #F59E0B;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        .logo {
            font-size: 24px;
            font-weight: bold;
            color: #3B82F6;
            margin-bottom: 10px;
        }
        .reminder-icon {
            width: 60px;
            height: 60px;
            background-color: #F59E0B;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
        }
        .title {
            color: #F59E0B;
            font-size: 28px;
            margin-bottom: 10px;
        }
        .subtitle {
            color: #666;
            font-size: 16px;
        }
        .countdown {
            background: linear-gradient(135deg, #F59E0B 0%, #D97706 100%);
            color: white;
            padding: 25px;
            border-radius: 8px;
            margin: 20px 0;
            text-align: center;
        }
        .countdown-text {
            font-size: 18px;
            margin-bottom: 10px;
        }
        .countdown-time {
            font-size: 36px;
            font-weight: bold;
        }
        .event-info {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 25px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .event-title {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 20px;
        }
        .event-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
        }
        .event-detail {
            display: flex;
            align-items: center;
        }
        .event-detail svg {
            width: 18px;
            height: 18px;
            margin-right: 10px;
            opacity: 0.9;
        }
        .checklist {
            background-color: #f0f9ff;
            border: 1px solid #0ea5e9;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .checklist h3 {
            color: #0c4a6e;
            margin-top: 0;
            margin-bottom: 15px;
        }
        .checklist-item {
            display: flex;
            align-items: flex-start;
            margin-bottom: 12px;
            color: #0c4a6e;
        }
        .checklist-item:last-child {
            margin-bottom: 0;
        }
        .checklist-icon {
            width: 20px;
            height: 20px;
            background-color: #0ea5e9;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 12px;
            flex-shrink: 0;
            margin-top: 2px;
        }
        .weather-info {
            background-color: #f1f5f9;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
            text-align: center;
        }
        .cta-buttons {
            text-align: center;
            margin: 30px 0;
        }
        .cta-button {
            display: inline-block;
            background-color: #3B82F6;
            color: white;
            padding: 12px 24px;
            text-decoration: none;
            border-radius: 6px;
            font-weight: bold;
            margin: 0 10px;
        }
        .cta-button.secondary {
            background-color: #F59E0B;
        }
        .footer {
            text-align: center;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #e9ecef;
            color: #666;
            font-size: 14px;
        }
        @media (max-width: 600px) {
            .event-details {
                grid-template-columns: 1fr;
            }
            .cta-button {
                display: block;
                margin: 10px 0;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <div class="logo">🎫 LIQUID LIGHTS</div>
            <div class="reminder-icon">
                <svg width="30" height="30" fill="white" viewBox="0 0 24 24">
                    <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                </svg>
            </div>
            <h1 class="title">Event Reminder</h1>
            <p class="subtitle">Don't forget about your upcoming event, {{ $user->name }}!</p>
        </div>

        <!-- Countdown -->
        <div class="countdown">
            <div class="countdown-text">Your event is in</div>
            <div class="countdown-time">{{ $hoursBeforeEvent }} Hours</div>
        </div>

        <!-- Event Information -->
        <div class="event-info">
            <div class="event-title">{{ $event->title }}</div>
            <div class="event-details">
                <div class="event-detail">
                    <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                    </svg>
                    {{ \Carbon\Carbon::parse($event->event_date)->format('Tomorrow, F d') }}
                </div>
                <div class="event-detail">
                    <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    {{ \Carbon\Carbon::parse($event->event_time)->format('g:i A') }}
                </div>
                <div class="event-detail">
                    <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                    </svg>
                    {{ $event->venue_name }}
                </div>
                <div class="event-detail">
                    <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                    </svg>
                    {{ $booking->quantity }} {{ Str::plural('Ticket', $booking->quantity) }}
                </div>
            </div>
        </div>

        <!-- Pre-Event Checklist -->
        <div class="checklist">
            <h3>Pre-Event Checklist</h3>
            
            <div class="checklist-item">
                <div class="checklist-icon">
                    <svg width="12" height="12" fill="white" viewBox="0 0 24 24">
                        <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"/>
                    </svg>
                </div>
                <div>
                    <strong>Download your tickets</strong> - Make sure you have your QR code ready on your phone or printed
                </div>
            </div>
            
            <div class="checklist-item">
                <div class="checklist-icon">
                    <svg width="12" height="12" fill="white" viewBox="0 0 24 24">
                        <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"/>
                    </svg>
                </div>
                <div>
                    <strong>Bring valid ID</strong> - Government-issued photo ID required for entry verification
                </div>
            </div>
            
            <div class="checklist-item">
                <div class="checklist-icon">
                    <svg width="12" height="12" fill="white" viewBox="0 0 24 24">
                        <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"/>
                    </svg>
                </div>
                <div>
                    <strong>Arrive early</strong> - Come 30 minutes before the event starts to avoid queues
                </div>
            </div>
            
            <div class="checklist-item">
                <div class="checklist-icon">
                    <svg width="12" height="12" fill="white" viewBox="0 0 24 24">
                        <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"/>
                    </svg>
                </div>
                <div>
                    <strong>Check the weather</strong> - Dress appropriately for tomorrow's conditions
                </div>
            </div>
            
            <div class="checklist-item">
                <div class="checklist-icon">
                    <svg width="12" height="12" fill="white" viewBox="0 0 24 24">
                        <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"/>
                    </svg>
                </div>
                <div>
                    <strong>Plan your route</strong> - Check traffic and parking options in advance
                </div>
            </div>
        </div>

        <!-- Weather Info -->
        <div class="weather-info">
            <h3 style="margin-top: 0; color: #1a1a1a;">Tomorrow's Weather</h3>
            <p style="margin: 0; color: #666;">
                Check your local weather forecast and dress accordingly for the outdoor portions of the event.
            </p>
        </div>

        <!-- Call to Action Buttons -->
        <div class="cta-buttons">
            <a href="{{ route('user.booking.download', $booking->id) }}" class="cta-button">
                Download Tickets
            </a>
            <a href="{{ route('user.booking', $booking->id) }}" class="cta-button secondary">
                View Details
            </a>
        </div>

        <!-- Venue Information -->
        @if($event->venue_address)
        <div style="margin: 20px 0; padding: 20px; background-color: #f9fafb; border-radius: 8px;">
            <h3 style="margin-top: 0; color: #1a1a1a;">Venue Information</h3>
            <p style="margin: 0 0 10px 0;"><strong>{{ $event->venue_name }}</strong></p>
            <p style="margin: 0; color: #666;">{{ $event->venue_address }}</p>
            
            <div style="margin-top: 15px;">
                <a href="https://maps.google.com/?q={{ urlencode($event->venue_name . ' ' . $event->venue_address) }}" 
                   style="color: #3B82F6; text-decoration: none; font-weight: bold;">
                    📍 Get Directions
                </a>
            </div>
        </div>
        @endif

        <!-- Important Notes -->
        <div style="background-color: #fef3c7; border: 1px solid #f59e0b; padding: 15px; border-radius: 8px; margin: 20px 0;">
            <h3 style="color: #92400e; margin-top: 0; margin-bottom: 10px;">Important Reminders</h3>
            <ul style="margin: 0; padding-left: 20px; color: #92400e;">
                <li>Screenshots of tickets will NOT be accepted</li>
                <li>Tickets are non-transferable and non-refundable</li>
                <li>Outside food and beverages are not permitted</li>
                <li>Security screening may be required at entry</li>
            </ul>
        </div>

        <!-- Footer -->
        <div class="footer">
            <p>
                Need help or have questions? Contact us at 
                <a href="mailto:<EMAIL>"><EMAIL></a> 
                or call +91-XXXXXXXXXX
            </p>
            
            <p style="margin-top: 20px; font-size: 12px; color: #999;">
                © {{ date('Y') }} Liquid Lights Tickets. All rights reserved.<br>
                This reminder was sent to {{ $user->email }}
            </p>
        </div>
    </div>
</body>
</html>
