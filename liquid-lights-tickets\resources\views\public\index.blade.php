@extends('public.layout')

@section('title', 'Liquid Lights Tickets - Premium Event Ticketing')
@section('description', 'Discover and book tickets for the hottest events, concerts, and shows. Secure booking, instant confirmation, and premium customer service.')

@section('content')
<!-- Hero Section -->
<section class="relative bg-gradient-to-r from-blue-600 via-purple-600 to-blue-800 text-white overflow-hidden">
    <div class="absolute inset-0 bg-black opacity-20"></div>
    <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24">
        <div class="text-center">
            <h1 class="text-4xl md:text-6xl font-bold mb-6">
                Discover Amazing
                <span class="text-transparent bg-clip-text bg-gradient-to-r from-yellow-400 to-orange-500">Events</span>
            </h1>
            <p class="text-xl md:text-2xl mb-8 text-gray-200 max-w-3xl mx-auto">
                Book tickets for concerts, shows, festivals, and exclusive events. Your next unforgettable experience is just a click away.
            </p>
            
            <!-- Hero Search -->
            <div class="max-w-2xl mx-auto">
                <form action="{{ route('public.search') }}" method="GET" class="flex flex-col sm:flex-row gap-4">
                    <div class="flex-1">
                        <input type="text" 
                               name="q" 
                               placeholder="Search for events, artists, or venues..." 
                               class="w-full px-6 py-4 text-gray-900 rounded-lg focus:ring-4 focus:ring-blue-300 focus:outline-none text-lg">
                    </div>
                    <button type="submit" 
                            class="px-8 py-4 bg-yellow-500 hover:bg-yellow-600 text-gray-900 font-semibold rounded-lg transition-colors text-lg">
                        Search Events
                    </button>
                </form>
            </div>
        </div>
    </div>
    
    <!-- Animated Background Elements -->
    <div class="absolute top-0 left-0 w-full h-full overflow-hidden pointer-events-none">
        <div class="absolute top-1/4 left-1/4 w-64 h-64 bg-blue-400 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse"></div>
        <div class="absolute top-1/3 right-1/4 w-72 h-72 bg-purple-400 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse animation-delay-2000"></div>
        <div class="absolute bottom-1/4 left-1/3 w-80 h-80 bg-pink-400 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse animation-delay-4000"></div>
    </div>
</section>

<!-- Featured Events Section -->
@if($featuredEvents->count() > 0)
<section class="py-16 bg-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
            <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">Featured Events</h2>
            <p class="text-xl text-gray-600 max-w-2xl mx-auto">Don't miss out on these handpicked premium events</p>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            @foreach($featuredEvents as $event)
            <div class="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300 group">
                <div class="relative">
                    <img src="{{ $event->banner_image ?: '/images/event-placeholder.jpg' }}" 
                         alt="{{ $event->title }}" 
                         class="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300">
                    <div class="absolute top-4 left-4">
                        <span class="bg-yellow-500 text-gray-900 px-3 py-1 rounded-full text-sm font-semibold">
                            Featured
                        </span>
                    </div>
                    <div class="absolute top-4 right-4">
                        <span class="bg-white bg-opacity-90 text-gray-900 px-3 py-1 rounded-full text-sm font-semibold">
                            {{ \Carbon\Carbon::parse($event->event_date)->format('M d') }}
                        </span>
                    </div>
                </div>
                
                <div class="p-6">
                    <h3 class="text-xl font-bold text-gray-900 mb-2 group-hover:text-blue-600 transition-colors">
                        {{ $event->title }}
                    </h3>
                    <p class="text-gray-600 mb-4 line-clamp-2">{{ $event->short_description }}</p>
                    
                    <div class="flex items-center text-gray-500 mb-4">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                        </svg>
                        {{ $event->venue_name }}
                    </div>
                    
                    @if($event->artists->count() > 0)
                    <div class="flex items-center mb-4">
                        <span class="text-sm text-gray-500 mr-2">Artists:</span>
                        <div class="flex flex-wrap gap-1">
                            @foreach($event->artists->take(2) as $artist)
                            <span class="bg-gray-100 text-gray-700 px-2 py-1 rounded text-xs">{{ $artist->name }}</span>
                            @endforeach
                            @if($event->artists->count() > 2)
                            <span class="text-gray-500 text-xs">+{{ $event->artists->count() - 2 }} more</span>
                            @endif
                        </div>
                    </div>
                    @endif
                    
                    <div class="flex justify-between items-center">
                        <div>
                            <span class="text-2xl font-bold text-blue-600">
                                ₹{{ number_format($event->ticketTypes->min('price')) }}
                            </span>
                            @if($event->ticketTypes->min('price') != $event->ticketTypes->max('price'))
                            <span class="text-gray-500">onwards</span>
                            @endif
                        </div>
                        <a href="{{ route('public.event', $event->id) }}" 
                           class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg font-semibold transition-colors">
                            Book Now
                        </a>
                    </div>
                </div>
            </div>
            @endforeach
        </div>
        
        <div class="text-center mt-12">
            <a href="{{ route('public.events') }}" 
               class="inline-flex items-center px-8 py-3 border border-blue-600 text-blue-600 hover:bg-blue-600 hover:text-white rounded-lg font-semibold transition-colors">
                View All Events
                <svg class="ml-2 w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
                </svg>
            </a>
        </div>
    </div>
</section>
@endif

<!-- Upcoming Events Section -->
@if($upcomingEvents->count() > 0)
<section class="py-16 bg-gray-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
            <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">Upcoming Events</h2>
            <p class="text-xl text-gray-600 max-w-2xl mx-auto">Discover what's happening near you</p>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            @foreach($upcomingEvents as $event)
            <div class="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow duration-300 group">
                <div class="relative">
                    <img src="{{ $event->banner_image ?: '/images/event-placeholder.jpg' }}" 
                         alt="{{ $event->title }}" 
                         class="w-full h-32 object-cover group-hover:scale-105 transition-transform duration-300">
                    <div class="absolute top-2 right-2">
                        <span class="bg-white bg-opacity-90 text-gray-900 px-2 py-1 rounded text-xs font-semibold">
                            {{ \Carbon\Carbon::parse($event->event_date)->format('M d') }}
                        </span>
                    </div>
                </div>
                
                <div class="p-4">
                    <h3 class="font-bold text-gray-900 mb-2 group-hover:text-blue-600 transition-colors line-clamp-1">
                        {{ $event->title }}
                    </h3>
                    <p class="text-gray-600 text-sm mb-3 line-clamp-2">{{ $event->short_description }}</p>
                    
                    <div class="flex items-center text-gray-500 text-sm mb-3">
                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                        </svg>
                        {{ Str::limit($event->venue_name, 20) }}
                    </div>
                    
                    <div class="flex justify-between items-center">
                        <span class="text-lg font-bold text-blue-600">
                            ₹{{ number_format($event->ticketTypes->min('price')) }}
                        </span>
                        <a href="{{ route('public.event', $event->id) }}" 
                           class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-1 rounded text-sm font-semibold transition-colors">
                            Book
                        </a>
                    </div>
                </div>
            </div>
            @endforeach
        </div>
    </div>
</section>
@endif

<!-- Popular Artists Section -->
@if($popularArtists->count() > 0)
<section class="py-16 bg-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
            <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">Popular Artists</h2>
            <p class="text-xl text-gray-600 max-w-2xl mx-auto">Follow your favorite artists and never miss their shows</p>
        </div>
        
        <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-6">
            @foreach($popularArtists as $artist)
            <div class="text-center group">
                <a href="{{ route('public.artist', $artist->id) }}" class="block">
                    <div class="relative mb-4">
                        <img src="{{ $artist->image ?: '/images/artist-placeholder.jpg' }}" 
                             alt="{{ $artist->name }}" 
                             class="w-24 h-24 mx-auto rounded-full object-cover group-hover:scale-110 transition-transform duration-300">
                        <div class="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 rounded-full transition-all duration-300"></div>
                    </div>
                    <h3 class="font-semibold text-gray-900 group-hover:text-blue-600 transition-colors">{{ $artist->name }}</h3>
                    <p class="text-sm text-gray-500">{{ $artist->events_count }} upcoming events</p>
                </a>
            </div>
            @endforeach
        </div>
        
        <div class="text-center mt-12">
            <a href="{{ route('public.artists') }}" 
               class="inline-flex items-center px-8 py-3 border border-blue-600 text-blue-600 hover:bg-blue-600 hover:text-white rounded-lg font-semibold transition-colors">
                View All Artists
                <svg class="ml-2 w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
                </svg>
            </a>
        </div>
    </div>
</section>
@endif

<!-- Call to Action Section -->
<section class="py-16 bg-gradient-to-r from-blue-600 to-purple-600 text-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <h2 class="text-3xl md:text-4xl font-bold mb-4">Ready to Experience Something Amazing?</h2>
        <p class="text-xl mb-8 text-blue-100 max-w-2xl mx-auto">
            Join thousands of event-goers who trust Liquid Lights for their entertainment needs.
        </p>
        <div class="flex flex-col sm:flex-row gap-4 justify-center">
            <a href="{{ route('public.events') }}" 
               class="bg-white text-blue-600 hover:bg-gray-100 px-8 py-3 rounded-lg font-semibold transition-colors">
                Browse Events
            </a>
            @guest
            <a href="{{ route('register') }}" 
               class="bg-yellow-500 text-gray-900 hover:bg-yellow-600 px-8 py-3 rounded-lg font-semibold transition-colors">
                Sign Up Free
            </a>
            @endguest
        </div>
    </div>
</section>

<!-- Features Section -->
<section class="py-16 bg-gray-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
            <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">Why Choose Liquid Lights?</h2>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div class="text-center">
                <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg class="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-bold text-gray-900 mb-2">Secure Booking</h3>
                <p class="text-gray-600">Your transactions are protected with bank-level security and instant confirmation.</p>
            </div>
            
            <div class="text-center">
                <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg class="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-bold text-gray-900 mb-2">Instant Confirmation</h3>
                <p class="text-gray-600">Get your tickets immediately after booking with QR codes for easy entry.</p>
            </div>
            
            <div class="text-center">
                <div class="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg class="w-8 h-8 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18.364 5.636l-3.536 3.536m0 5.656l3.536 3.536M9.172 9.172L5.636 5.636m3.536 9.192L5.636 18.364M21 12a9 9 0 11-18 0 9 9 0 0118 0zm-5 0a4 4 0 11-8 0 4 4 0 018 0z"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-bold text-gray-900 mb-2">24/7 Support</h3>
                <p class="text-gray-600">Our dedicated support team is always ready to help you with any questions.</p>
            </div>
        </div>
    </div>
</section>
@endsection
