<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\SponsorshipTier;
use Illuminate\Support\Str;

class SponsorshipTierController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = SponsorshipTier::query();

        // Search functionality
        if ($request->filled('search')) {
            $query->where('name', 'like', "%{$request->search}%")
                  ->orWhere('description', 'like', "%{$request->search}%");
        }

        // Status filter
        if ($request->filled('status')) {
            $query->where('is_active', $request->status === 'active');
        }

        // Sort
        $sortBy = $request->get('sort_by', 'display_order');
        $sortOrder = $request->get('sort_order', 'asc');
        $query->orderBy($sortBy, $sortOrder);

        $tiers = $query->paginate(15)->withQueryString();

        // Get statistics
        $stats = [
            'total' => SponsorshipTier::count(),
            'active' => SponsorshipTier::where('is_active', true)->count(),
            'inactive' => SponsorshipTier::where('is_active', false)->count(),
        ];

        return view('admin.sponsorship-tiers.index', compact('tiers', 'stats'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('admin.sponsorship-tiers.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'slug' => 'nullable|string|max:255|unique:sponsorship_tiers,slug',
            'description' => 'nullable|string',
            'price' => 'nullable|numeric|min:0',
            'benefits' => 'nullable|array',
            'benefits.*' => 'string',
            'color' => 'required|string|regex:/^#[0-9A-Fa-f]{6}$/',
            'max_sponsors_per_event' => 'required|integer|min:1',
            'display_order' => 'required|integer|min:0',
            'is_active' => 'boolean'
        ]);

        // Generate slug if not provided
        if (empty($validated['slug'])) {
            $validated['slug'] = Str::slug($validated['name']);
        }

        // Convert benefits array to proper format
        if (isset($validated['benefits'])) {
            $validated['benefits'] = array_filter($validated['benefits']);
        }

        $tier = SponsorshipTier::create($validated);

        return redirect()->route('admin.sponsorship-tiers.show', $tier)
            ->with('success', 'Sponsorship tier created successfully.');
    }

    /**
     * Display the specified resource.
     */
    public function show(SponsorshipTier $sponsorshipTier)
    {
        $sponsorshipTier->load(['eventSponsors.sponsor', 'eventSponsors.event']);

        // Get statistics
        $stats = [
            'total_sponsorships' => $sponsorshipTier->eventSponsors()->count(),
            'active_sponsorships' => $sponsorshipTier->eventSponsors()->where('status', 'active')->count(),
            'total_revenue' => $sponsorshipTier->eventSponsors()->where('status', 'active')->sum('amount'),
            'avg_sponsorship_value' => $sponsorshipTier->eventSponsors()->where('status', 'active')->avg('amount'),
        ];

        return view('admin.sponsorship-tiers.show', compact('sponsorshipTier', 'stats'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(SponsorshipTier $sponsorshipTier)
    {
        return view('admin.sponsorship-tiers.edit', compact('sponsorshipTier'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, SponsorshipTier $sponsorshipTier)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'slug' => 'nullable|string|max:255|unique:sponsorship_tiers,slug,' . $sponsorshipTier->id,
            'description' => 'nullable|string',
            'price' => 'nullable|numeric|min:0',
            'benefits' => 'nullable|array',
            'benefits.*' => 'string',
            'color' => 'required|string|regex:/^#[0-9A-Fa-f]{6}$/',
            'max_sponsors_per_event' => 'required|integer|min:1',
            'display_order' => 'required|integer|min:0',
            'is_active' => 'boolean'
        ]);

        // Generate slug if not provided
        if (empty($validated['slug'])) {
            $validated['slug'] = Str::slug($validated['name']);
        }

        // Convert benefits array to proper format
        if (isset($validated['benefits'])) {
            $validated['benefits'] = array_filter($validated['benefits']);
        }

        $sponsorshipTier->update($validated);

        return redirect()->route('admin.sponsorship-tiers.show', $sponsorshipTier)
            ->with('success', 'Sponsorship tier updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(SponsorshipTier $sponsorshipTier)
    {
        // Check if tier has active sponsorships
        if ($sponsorshipTier->eventSponsors()->where('status', 'active')->exists()) {
            return redirect()->route('admin.sponsorship-tiers.index')
                ->with('error', 'Cannot delete tier with active sponsorships.');
        }

        $sponsorshipTier->delete();

        return redirect()->route('admin.sponsorship-tiers.index')
            ->with('success', 'Sponsorship tier deleted successfully.');
    }
}
