// Offline Data Manager for Liquid Lights Tickets PWA
class OfflineDataManager {
    constructor() {
        this.dbName = 'LiquidLightsTickets';
        this.dbVersion = 1;
        this.db = null;
        this.isOnline = navigator.onLine;
        
        this.stores = {
            events: 'events',
            bookings: 'bookings',
            tickets: 'tickets',
            userProfile: 'userProfile',
            offlineActions: 'offlineActions',
            cachedImages: 'cachedImages'
        };
        
        this.init();
    }

    async init() {
        try {
            await this.openDatabase();
            this.setupEventListeners();
            await this.syncOnlineData();
            console.log('Offline data manager initialized');
        } catch (error) {
            console.error('Failed to initialize offline data manager:', error);
        }
    }

    async openDatabase() {
        return new Promise((resolve, reject) => {
            const request = indexedDB.open(this.dbName, this.dbVersion);
            
            request.onerror = () => reject(request.error);
            request.onsuccess = () => {
                this.db = request.result;
                resolve(this.db);
            };
            
            request.onupgradeneeded = (event) => {
                const db = event.target.result;
                
                // Events store
                if (!db.objectStoreNames.contains(this.stores.events)) {
                    const eventsStore = db.createObjectStore(this.stores.events, { keyPath: 'id' });
                    eventsStore.createIndex('status', 'status', { unique: false });
                    eventsStore.createIndex('event_date', 'event_date', { unique: false });
                }
                
                // Bookings store
                if (!db.objectStoreNames.contains(this.stores.bookings)) {
                    const bookingsStore = db.createObjectStore(this.stores.bookings, { keyPath: 'id' });
                    bookingsStore.createIndex('user_id', 'user_id', { unique: false });
                    bookingsStore.createIndex('payment_status', 'payment_status', { unique: false });
                }
                
                // Tickets store (for offline ticket viewing)
                if (!db.objectStoreNames.contains(this.stores.tickets)) {
                    const ticketsStore = db.createObjectStore(this.stores.tickets, { keyPath: 'booking_id' });
                    ticketsStore.createIndex('qr_code_hash', 'qr_code_hash', { unique: true });
                }
                
                // User profile store
                if (!db.objectStoreNames.contains(this.stores.userProfile)) {
                    db.createObjectStore(this.stores.userProfile, { keyPath: 'id' });
                }
                
                // Offline actions store (for sync when online)
                if (!db.objectStoreNames.contains(this.stores.offlineActions)) {
                    const actionsStore = db.createObjectStore(this.stores.offlineActions, { keyPath: 'id', autoIncrement: true });
                    actionsStore.createIndex('timestamp', 'timestamp', { unique: false });
                    actionsStore.createIndex('type', 'type', { unique: false });
                }
                
                // Cached images store
                if (!db.objectStoreNames.contains(this.stores.cachedImages)) {
                    const imagesStore = db.createObjectStore(this.stores.cachedImages, { keyPath: 'url' });
                    imagesStore.createIndex('cached_at', 'cached_at', { unique: false });
                }
            };
        });
    }

    setupEventListeners() {
        window.addEventListener('online', () => {
            this.isOnline = true;
            this.syncOfflineActions();
            this.updateOnlineStatus();
        });
        
        window.addEventListener('offline', () => {
            this.isOnline = false;
            this.updateOnlineStatus();
        });
    }

    updateOnlineStatus() {
        const statusElement = document.getElementById('offline-indicator');
        if (statusElement) {
            if (this.isOnline) {
                statusElement.style.display = 'none';
            } else {
                statusElement.style.display = 'block';
                statusElement.innerHTML = `
                    <div class="bg-yellow-500 text-white px-4 py-2 text-center text-sm">
                        📱 You're offline. Some features may be limited.
                    </div>
                `;
            }
        }
    }

    // Store data in IndexedDB
    async storeData(storeName, data) {
        if (!this.db) return false;
        
        try {
            const transaction = this.db.transaction([storeName], 'readwrite');
            const store = transaction.objectStore(storeName);
            
            if (Array.isArray(data)) {
                for (const item of data) {
                    await store.put(item);
                }
            } else {
                await store.put(data);
            }
            
            return true;
        } catch (error) {
            console.error(`Error storing data in ${storeName}:`, error);
            return false;
        }
    }

    // Retrieve data from IndexedDB
    async getData(storeName, key = null) {
        if (!this.db) return null;
        
        try {
            const transaction = this.db.transaction([storeName], 'readonly');
            const store = transaction.objectStore(storeName);
            
            if (key) {
                const request = store.get(key);
                return new Promise((resolve, reject) => {
                    request.onsuccess = () => resolve(request.result);
                    request.onerror = () => reject(request.error);
                });
            } else {
                const request = store.getAll();
                return new Promise((resolve, reject) => {
                    request.onsuccess = () => resolve(request.result);
                    request.onerror = () => reject(request.error);
                });
            }
        } catch (error) {
            console.error(`Error retrieving data from ${storeName}:`, error);
            return null;
        }
    }

    // Store user's events for offline viewing
    async cacheEvents(events) {
        const success = await this.storeData(this.stores.events, events);
        if (success) {
            console.log(`Cached ${events.length} events for offline viewing`);
        }
        return success;
    }

    // Store user's bookings for offline viewing
    async cacheBookings(bookings) {
        const success = await this.storeData(this.stores.bookings, bookings);
        if (success) {
            console.log(`Cached ${bookings.length} bookings for offline viewing`);
        }
        return success;
    }

    // Store ticket data with QR codes for offline access
    async cacheTickets(tickets) {
        const success = await this.storeData(this.stores.tickets, tickets);
        if (success) {
            console.log(`Cached ${tickets.length} tickets for offline viewing`);
        }
        return success;
    }

    // Get cached events for offline viewing
    async getCachedEvents() {
        return await this.getData(this.stores.events);
    }

    // Get cached bookings for offline viewing
    async getCachedBookings() {
        return await this.getData(this.stores.bookings);
    }

    // Get cached tickets for offline viewing
    async getCachedTickets() {
        return await this.getData(this.stores.tickets);
    }

    // Store offline action for later sync
    async storeOfflineAction(action) {
        const actionData = {
            ...action,
            timestamp: Date.now(),
            synced: false
        };
        
        return await this.storeData(this.stores.offlineActions, actionData);
    }

    // Sync offline actions when connection is restored
    async syncOfflineActions() {
        if (!this.isOnline) return;
        
        try {
            const actions = await this.getData(this.stores.offlineActions);
            const unsyncedActions = actions.filter(action => !action.synced);
            
            for (const action of unsyncedActions) {
                try {
                    await this.processOfflineAction(action);
                    
                    // Mark as synced
                    action.synced = true;
                    await this.storeData(this.stores.offlineActions, action);
                    
                } catch (error) {
                    console.error('Failed to sync offline action:', action, error);
                }
            }
            
            console.log(`Synced ${unsyncedActions.length} offline actions`);
        } catch (error) {
            console.error('Error syncing offline actions:', error);
        }
    }

    // Process individual offline action
    async processOfflineAction(action) {
        switch (action.type) {
            case 'profile_update':
                return await this.syncProfileUpdate(action.data);
            case 'notification_preference':
                return await this.syncNotificationPreference(action.data);
            case 'bookmark_event':
                return await this.syncBookmarkEvent(action.data);
            default:
                console.warn('Unknown offline action type:', action.type);
        }
    }

    // Sync profile updates
    async syncProfileUpdate(data) {
        const response = await fetch('/api/user/profile', {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify(data)
        });
        
        if (!response.ok) {
            throw new Error('Failed to sync profile update');
        }
        
        return await response.json();
    }

    // Cache images for offline viewing
    async cacheImage(url) {
        try {
            const response = await fetch(url);
            if (!response.ok) return false;
            
            const blob = await response.blob();
            const imageData = {
                url: url,
                blob: blob,
                cached_at: Date.now()
            };
            
            return await this.storeData(this.stores.cachedImages, imageData);
        } catch (error) {
            console.error('Error caching image:', url, error);
            return false;
        }
    }

    // Get cached image
    async getCachedImage(url) {
        const imageData = await this.getData(this.stores.cachedImages, url);
        if (imageData) {
            return URL.createObjectURL(imageData.blob);
        }
        return null;
    }

    // Sync data when online
    async syncOnlineData() {
        if (!this.isOnline) return;
        
        try {
            // Sync user bookings
            const bookingsResponse = await fetch('/api/user/bookings');
            if (bookingsResponse.ok) {
                const bookings = await bookingsResponse.json();
                await this.cacheBookings(bookings.data || bookings);
            }
            
            // Sync user profile
            const profileResponse = await fetch('/api/user/profile');
            if (profileResponse.ok) {
                const profile = await profileResponse.json();
                await this.storeData(this.stores.userProfile, profile);
            }
            
            // Sync recent events
            const eventsResponse = await fetch('/api/events?limit=50');
            if (eventsResponse.ok) {
                const events = await eventsResponse.json();
                await this.cacheEvents(events.data || events);
            }
            
            console.log('Online data synced successfully');
        } catch (error) {
            console.error('Error syncing online data:', error);
        }
    }

    // Check if data is available offline
    async isDataAvailableOffline(type, id = null) {
        try {
            const data = await this.getData(this.stores[type], id);
            return data !== null && data !== undefined;
        } catch (error) {
            return false;
        }
    }

    // Clear old cached data
    async clearOldCache(maxAge = 7 * 24 * 60 * 60 * 1000) { // 7 days
        const cutoffTime = Date.now() - maxAge;
        
        try {
            // Clear old cached images
            const images = await this.getData(this.stores.cachedImages);
            const oldImages = images.filter(img => img.cached_at < cutoffTime);
            
            for (const image of oldImages) {
                await this.deleteData(this.stores.cachedImages, image.url);
            }
            
            console.log(`Cleared ${oldImages.length} old cached images`);
        } catch (error) {
            console.error('Error clearing old cache:', error);
        }
    }

    // Delete data from store
    async deleteData(storeName, key) {
        if (!this.db) return false;
        
        try {
            const transaction = this.db.transaction([storeName], 'readwrite');
            const store = transaction.objectStore(storeName);
            await store.delete(key);
            return true;
        } catch (error) {
            console.error(`Error deleting data from ${storeName}:`, error);
            return false;
        }
    }

    // Get storage usage statistics
    async getStorageStats() {
        if (!navigator.storage || !navigator.storage.estimate) {
            return null;
        }
        
        try {
            const estimate = await navigator.storage.estimate();
            return {
                used: estimate.usage,
                available: estimate.quota,
                percentage: Math.round((estimate.usage / estimate.quota) * 100)
            };
        } catch (error) {
            console.error('Error getting storage stats:', error);
            return null;
        }
    }
}

// Initialize offline data manager
let offlineManager;

document.addEventListener('DOMContentLoaded', () => {
    offlineManager = new OfflineDataManager();
    
    // Add offline indicator to page
    if (!document.getElementById('offline-indicator')) {
        const indicator = document.createElement('div');
        indicator.id = 'offline-indicator';
        indicator.style.display = 'none';
        indicator.style.position = 'fixed';
        indicator.style.top = '0';
        indicator.style.left = '0';
        indicator.style.right = '0';
        indicator.style.zIndex = '9999';
        document.body.appendChild(indicator);
    }
});

// Export for global access
window.offlineManager = offlineManager;
