<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use App\Models\TicketType;
use App\Models\Event;

class TicketTypeController extends Controller
{
    /**
     * Display ticket types for a specific event
     */
    public function index(Request $request, $eventId = null)
    {
        $query = TicketType::with(['event']);

        if ($eventId) {
            $query->where('event_id', $eventId);
        }

        // Sorting
        $sortBy = $request->get('sort_by', 'name');
        $sortOrder = $request->get('sort_order', 'asc');
        $query->orderBy($sortBy, $sortOrder);

        $ticketTypes = $query->get();

        return response()->json([
            'success' => true,
            'data' => $ticketTypes
        ]);
    }

    /**
     * Store a newly created ticket type
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'event_id' => 'required|exists:events,id',
            'name' => 'required|string|max:255',
            'price' => 'required|numeric|min:0',
            'quantity_available' => 'required|integer|min:1',
            'sale_start_date' => 'required|date',
            'sale_end_date' => 'required|date|after:sale_start_date',
            'is_active' => 'boolean',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $ticketType = TicketType::create($request->all());

        return response()->json([
            'success' => true,
            'message' => 'Ticket type created successfully',
            'data' => $ticketType->load('event')
        ], 201);
    }

    /**
     * Display the specified ticket type
     */
    public function show($id)
    {
        $ticketType = TicketType::with(['event', 'bookings'])->findOrFail($id);

        return response()->json([
            'success' => true,
            'data' => $ticketType
        ]);
    }

    /**
     * Update the specified ticket type
     */
    public function update(Request $request, $id)
    {
        $ticketType = TicketType::findOrFail($id);

        $validator = Validator::make($request->all(), [
            'name' => 'sometimes|string|max:255',
            'price' => 'sometimes|numeric|min:0',
            'quantity_available' => 'sometimes|integer|min:1',
            'sale_start_date' => 'sometimes|date',
            'sale_end_date' => 'sometimes|date|after:sale_start_date',
            'is_active' => 'boolean',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        // Check if we can reduce quantity (not below sold tickets)
        if ($request->has('quantity_available')) {
            if ($request->quantity_available < $ticketType->quantity_sold) {
                return response()->json([
                    'success' => false,
                    'message' => 'Cannot reduce quantity below already sold tickets (' . $ticketType->quantity_sold . ')'
                ], 400);
            }
        }

        $ticketType->update($request->all());

        return response()->json([
            'success' => true,
            'message' => 'Ticket type updated successfully',
            'data' => $ticketType->load('event')
        ]);
    }

    /**
     * Remove the specified ticket type
     */
    public function destroy($id)
    {
        $ticketType = TicketType::findOrFail($id);

        // Check if ticket type has bookings
        if ($ticketType->bookings()->where('payment_status', 'paid')->exists()) {
            return response()->json([
                'success' => false,
                'message' => 'Cannot delete ticket type with paid bookings'
            ], 400);
        }

        $ticketType->delete();

        return response()->json([
            'success' => true,
            'message' => 'Ticket type deleted successfully'
        ]);
    }

    /**
     * Toggle ticket type active status
     */
    public function toggleStatus($id)
    {
        $ticketType = TicketType::findOrFail($id);

        $ticketType->update([
            'is_active' => !$ticketType->is_active
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Ticket type status updated successfully',
            'data' => $ticketType
        ]);
    }
}
