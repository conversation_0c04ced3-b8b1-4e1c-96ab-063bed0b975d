<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Sponsor;
use App\Models\EventSponsor;
use App\Models\SponsorshipTier;
use App\Models\Event;
use App\Models\Booking;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class SponsorAnalyticsController extends Controller
{
    /**
     * Display sponsor analytics dashboard
     */
    public function index(Request $request)
    {
        $dateRange = $this->getDateRange($request);

        // Overall statistics
        $stats = [
            'total_sponsors' => Sponsor::count(),
            'active_sponsors' => Sponsor::where('status', 'active')->count(),
            'total_sponsorships' => EventSponsor::where('status', 'active')->count(),
            'total_revenue' => EventSponsor::where('status', 'active')
                ->whereBetween('created_at', $dateRange)
                ->sum('amount'),
            'avg_sponsorship_value' => EventSponsor::where('status', 'active')
                ->whereBetween('created_at', $dateRange)
                ->avg('amount'),
            'events_with_sponsors' => Event::whereHas('activeSponsors')->count()
        ];

        // Top sponsors by revenue
        $topSponsors = Sponsor::select('sponsors.*')
            ->selectRaw('SUM(event_sponsors.amount) as total_revenue')
            ->selectRaw('COUNT(event_sponsors.id) as total_sponsorships')
            ->join('event_sponsors', 'sponsors.id', '=', 'event_sponsors.sponsor_id')
            ->where('event_sponsors.status', 'active')
            ->whereBetween('event_sponsors.created_at', $dateRange)
            ->groupBy('sponsors.id')
            ->orderByDesc('total_revenue')
            ->limit(10)
            ->get();

        // Sponsorship by tier
        $sponsorshipByTier = SponsorshipTier::select('sponsorship_tiers.*')
            ->selectRaw('COUNT(event_sponsors.id) as sponsorship_count')
            ->selectRaw('SUM(event_sponsors.amount) as total_revenue')
            ->leftJoin('event_sponsors', function($join) use ($dateRange) {
                $join->on('sponsorship_tiers.id', '=', 'event_sponsors.sponsorship_tier_id')
                     ->where('event_sponsors.status', 'active')
                     ->whereBetween('event_sponsors.created_at', $dateRange);
            })
            ->groupBy('sponsorship_tiers.id')
            ->orderBy('sponsorship_tiers.display_order')
            ->get();

        // Monthly revenue trend
        $monthlyRevenue = EventSponsor::select(
                DB::raw('DATE_FORMAT(created_at, "%Y-%m") as month'),
                DB::raw('SUM(amount) as revenue'),
                DB::raw('COUNT(*) as count')
            )
            ->where('status', 'active')
            ->whereBetween('created_at', $dateRange)
            ->groupBy('month')
            ->orderBy('month')
            ->get();

        // Recent sponsorships
        $recentSponsorships = EventSponsor::with(['sponsor', 'event', 'sponsorshipTier'])
            ->where('status', 'active')
            ->orderByDesc('created_at')
            ->limit(10)
            ->get();

        return view('admin.sponsor-analytics.index', compact(
            'stats',
            'topSponsors',
            'sponsorshipByTier',
            'monthlyRevenue',
            'recentSponsorships',
            'dateRange'
        ));
    }

    /**
     * Get sponsor performance report
     */
    public function sponsorReport(Request $request, Sponsor $sponsor)
    {
        $dateRange = $this->getDateRange($request);

        // Sponsor statistics
        $stats = [
            'total_sponsorships' => $sponsor->eventSponsors()
                ->where('status', 'active')
                ->whereBetween('created_at', $dateRange)
                ->count(),
            'total_investment' => $sponsor->eventSponsors()
                ->where('status', 'active')
                ->whereBetween('created_at', $dateRange)
                ->sum('amount'),
            'events_sponsored' => $sponsor->eventSponsors()
                ->where('status', 'active')
                ->whereBetween('created_at', $dateRange)
                ->distinct('event_id')
                ->count(),
            'avg_sponsorship_value' => $sponsor->eventSponsors()
                ->where('status', 'active')
                ->whereBetween('created_at', $dateRange)
                ->avg('amount')
        ];

        // Sponsorship history
        $sponsorships = $sponsor->eventSponsors()
            ->with(['event', 'sponsorshipTier'])
            ->where('status', 'active')
            ->whereBetween('created_at', $dateRange)
            ->orderByDesc('created_at')
            ->paginate(15);

        // ROI Analysis (based on event attendance)
        $roiData = $sponsor->eventSponsors()
            ->with(['event.bookings'])
            ->where('status', 'active')
            ->whereBetween('created_at', $dateRange)
            ->get()
            ->map(function ($sponsorship) {
                $totalAttendees = $sponsorship->event->bookings()
                    ->where('payment_status', 'paid')
                    ->sum('quantity');

                $costPerAttendee = $totalAttendees > 0 ? $sponsorship->amount / $totalAttendees : 0;

                return [
                    'event' => $sponsorship->event,
                    'sponsorship' => $sponsorship,
                    'attendees' => $totalAttendees,
                    'cost_per_attendee' => $costPerAttendee
                ];
            });

        // Tier distribution
        $tierDistribution = $sponsor->eventSponsors()
            ->select('sponsorship_tier_id')
            ->selectRaw('COUNT(*) as count')
            ->selectRaw('SUM(amount) as total_amount')
            ->with('sponsorshipTier')
            ->where('status', 'active')
            ->whereBetween('created_at', $dateRange)
            ->groupBy('sponsorship_tier_id')
            ->get();

        return view('admin.sponsor-analytics.sponsor-report', compact(
            'sponsor',
            'stats',
            'sponsorships',
            'roiData',
            'tierDistribution',
            'dateRange'
        ));
    }

    /**
     * Export sponsor analytics data
     */
    public function export(Request $request)
    {
        $dateRange = $this->getDateRange($request);
        $format = $request->get('format', 'csv');

        $data = EventSponsor::with(['sponsor', 'event', 'sponsorshipTier'])
            ->where('status', 'active')
            ->whereBetween('created_at', $dateRange)
            ->get()
            ->map(function ($sponsorship) {
                return [
                    'Sponsor Name' => $sponsorship->sponsor->name,
                    'Event Title' => $sponsorship->event->title,
                    'Event Date' => $sponsorship->event->event_date,
                    'Sponsorship Tier' => $sponsorship->sponsorshipTier->name,
                    'Amount' => $sponsorship->amount,
                    'Start Date' => $sponsorship->start_date,
                    'End Date' => $sponsorship->end_date,
                    'Status' => $sponsorship->status,
                    'Created At' => $sponsorship->created_at
                ];
            });

        if ($format === 'csv') {
            return $this->exportToCsv($data, 'sponsor-analytics-' . now()->format('Y-m-d'));
        }

        return response()->json($data);
    }

    /**
     * Get API data for charts
     */
    public function chartData(Request $request)
    {
        $dateRange = $this->getDateRange($request);
        $type = $request->get('type', 'revenue');

        switch ($type) {
            case 'revenue':
                return $this->getRevenueChartData($dateRange);
            case 'tier-distribution':
                return $this->getTierDistributionData($dateRange);
            case 'sponsor-performance':
                return $this->getSponsorPerformanceData($dateRange);
            default:
                return response()->json(['error' => 'Invalid chart type'], 400);
        }
    }

    /**
     * Get date range from request
     */
    private function getDateRange(Request $request)
    {
        $period = $request->get('period', '30');

        switch ($period) {
            case '7':
                return [Carbon::now()->subDays(7), Carbon::now()];
            case '30':
                return [Carbon::now()->subDays(30), Carbon::now()];
            case '90':
                return [Carbon::now()->subDays(90), Carbon::now()];
            case '365':
                return [Carbon::now()->subYear(), Carbon::now()];
            case 'custom':
                $start = $request->get('start_date', Carbon::now()->subDays(30));
                $end = $request->get('end_date', Carbon::now());
                return [Carbon::parse($start), Carbon::parse($end)];
            default:
                return [Carbon::now()->subDays(30), Carbon::now()];
        }
    }

    /**
     * Get revenue chart data
     */
    private function getRevenueChartData($dateRange)
    {
        $data = EventSponsor::select(
                DB::raw('DATE(created_at) as date'),
                DB::raw('SUM(amount) as revenue'),
                DB::raw('COUNT(*) as count')
            )
            ->where('status', 'active')
            ->whereBetween('created_at', $dateRange)
            ->groupBy('date')
            ->orderBy('date')
            ->get();

        return response()->json([
            'labels' => $data->pluck('date'),
            'revenue' => $data->pluck('revenue'),
            'count' => $data->pluck('count')
        ]);
    }

    /**
     * Get tier distribution data
     */
    private function getTierDistributionData($dateRange)
    {
        $data = SponsorshipTier::select('sponsorship_tiers.name', 'sponsorship_tiers.color')
            ->selectRaw('COUNT(event_sponsors.id) as count')
            ->selectRaw('SUM(event_sponsors.amount) as revenue')
            ->leftJoin('event_sponsors', function($join) use ($dateRange) {
                $join->on('sponsorship_tiers.id', '=', 'event_sponsors.sponsorship_tier_id')
                     ->where('event_sponsors.status', 'active')
                     ->whereBetween('event_sponsors.created_at', $dateRange);
            })
            ->groupBy('sponsorship_tiers.id')
            ->orderBy('sponsorship_tiers.display_order')
            ->get();

        return response()->json([
            'labels' => $data->pluck('name'),
            'data' => $data->pluck('count'),
            'revenue' => $data->pluck('revenue'),
            'colors' => $data->pluck('color')
        ]);
    }

    /**
     * Get sponsor performance data
     */
    private function getSponsorPerformanceData($dateRange)
    {
        $data = Sponsor::select('sponsors.name')
            ->selectRaw('SUM(event_sponsors.amount) as total_revenue')
            ->selectRaw('COUNT(event_sponsors.id) as total_sponsorships')
            ->join('event_sponsors', 'sponsors.id', '=', 'event_sponsors.sponsor_id')
            ->where('event_sponsors.status', 'active')
            ->whereBetween('event_sponsors.created_at', $dateRange)
            ->groupBy('sponsors.id')
            ->orderByDesc('total_revenue')
            ->limit(10)
            ->get();

        return response()->json([
            'labels' => $data->pluck('name'),
            'revenue' => $data->pluck('total_revenue'),
            'sponsorships' => $data->pluck('total_sponsorships')
        ]);
    }

    /**
     * Export data to CSV
     */
    private function exportToCsv($data, $filename)
    {
        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="' . $filename . '.csv"',
        ];

        $callback = function() use ($data) {
            $file = fopen('php://output', 'w');

            // Add CSV headers
            if ($data->isNotEmpty()) {
                fputcsv($file, array_keys($data->first()));
            }

            // Add data rows
            foreach ($data as $row) {
                fputcsv($file, $row);
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }
}
