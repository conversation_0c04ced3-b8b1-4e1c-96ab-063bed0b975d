<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Event;
use App\Models\Artist;
use Carbon\Carbon;

class PublicController extends Controller
{
    /**
     * Display the homepage
     */
    public function index()
    {
        try {
            // Test database connection first
            \DB::connection()->getPdo();

            // Get featured events
            $featuredEvents = Event::where('status', 'published')
                ->where('is_featured', true)
                ->where('event_date', '>=', Carbon::now())
                ->with(['ticketTypes', 'artists'])
                ->orderBy('event_date')
                ->limit(6)
                ->get();

            // Get upcoming events
            $upcomingEvents = Event::where('status', 'published')
                ->where('event_date', '>=', Carbon::now())
                ->with(['ticketTypes', 'artists'])
                ->orderBy('event_date')
                ->limit(8)
                ->get();

            // Get popular artists
            $popularArtists = Artist::where('is_active', true)
                ->withCount(['events' => function($query) {
                    $query->where('status', 'published')
                          ->where('event_date', '>=', Carbon::now());
                }])
                ->having('events_count', '>', 0)
                ->orderBy('events_count', 'desc')
                ->limit(6)
                ->get();

            // Get sponsors
            $sponsors = \App\Models\Sponsor::where('is_active', true)
                ->orderBy('name')
                ->limit(6)
                ->get();

        } catch (\Exception $e) {
            // Demo data when database is not available
            $featuredEvents = collect([
                (object) [
                    'id' => 1,
                    'title' => 'Electronic Night 2025',
                    'description' => 'Experience the ultimate electronic music journey with world-class DJs and immersive light shows that will transport you to another dimension.',
                    'short_description' => 'Ultimate electronic music journey with world-class DJs',
                    'event_date' => '2025-08-15 21:00:00',
                    'event_time' => '21:00:00',
                    'start_time' => '21:00:00',
                    'end_time' => '03:00:00',
                    'venue' => 'Liquid Lights Main Floor',
                    'venue_name' => 'Liquid Lights Main Floor',
                    'venue_address' => 'Bandra West, Mumbai, Maharashtra 400050',
                    'location' => 'Mumbai, Maharashtra',
                    'banner_image' => 'https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?auto=format&fit=crop&w=800&h=600',
                    'thumbnail_image' => 'https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?auto=format&fit=crop&w=400&h=300',
                    'is_featured' => true,
                    'status' => 'published',
                    'capacity' => 500,
                    'min_price' => 1500,
                    'max_price' => 3000,
                    'category' => 'Electronic',
                    'duration' => '6 hours',
                    'age_restriction' => '21+',
                    'dress_code' => 'Smart Casual',
                    'terms_conditions' => 'Standard event terms apply',
                    'refund_policy' => 'No refunds 24 hours before event',
                    'contact_phone' => '+91 98765 43210',
                    'contact_email' => '<EMAIL>',
                    'social_media' => (object) [
                        'facebook' => 'liquidlights',
                        'instagram' => 'liquidlights',
                        'twitter' => 'liquidlights'
                    ],
                    'tags' => ['electronic', 'nightlife', 'music', 'dance'],
                    'featured_image' => 'https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?auto=format&fit=crop&w=800&h=600',
                    'gallery_images' => [
                        'https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?auto=format&fit=crop&w=400&h=300',
                        'https://images.unsplash.com/photo-1516450360452-9312f5e86fc7?auto=format&fit=crop&w=400&h=300'
                    ],
                    'created_at' => '2025-07-01 10:00:00',
                    'updated_at' => '2025-07-13 10:00:00',
                    'ticket_types' => collect([
                        (object) ['id' => 1, 'name' => 'General', 'price' => 1500, 'quantity' => 300, 'available' => 250],
                        (object) ['id' => 2, 'name' => 'VIP', 'price' => 3000, 'quantity' => 100, 'available' => 80]
                    ]),
                    'artists' => collect([
                        (object) ['id' => 1, 'name' => 'DJ Neon Storm', 'image' => 'https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?auto=format&fit=crop&w=200&h=200']
                    ])
                ],
                (object) [
                    'id' => 2,
                    'title' => 'Neon Nights Festival',
                    'description' => 'A spectacular light and music festival under neon skies featuring multiple stages, food courts, and interactive art installations.',
                    'short_description' => 'Spectacular light and music festival under neon skies',
                    'event_date' => '2025-08-22 20:00:00',
                    'event_time' => '20:00:00',
                    'start_time' => '20:00:00',
                    'end_time' => '04:00:00',
                    'venue' => 'Liquid Lights Outdoor Arena',
                    'venue_name' => 'Liquid Lights Outdoor Arena',
                    'venue_address' => 'Juhu Beach, Mumbai, Maharashtra 400049',
                    'location' => 'Mumbai, Maharashtra',
                    'banner_image' => 'https://images.unsplash.com/photo-1516450360452-9312f5e86fc7?auto=format&fit=crop&w=800&h=600',
                    'thumbnail_image' => 'https://images.unsplash.com/photo-1516450360452-9312f5e86fc7?auto=format&fit=crop&w=400&h=300',
                    'is_featured' => true,
                    'status' => 'published',
                    'capacity' => 1000,
                    'min_price' => 1200,
                    'max_price' => 1800,
                    'category' => 'Festival',
                    'duration' => '8 hours',
                    'age_restriction' => '18+',
                    'dress_code' => 'Festival Wear',
                    'terms_conditions' => 'Festival terms and conditions apply',
                    'refund_policy' => 'Refunds available up to 48 hours before event',
                    'contact_phone' => '+91 98765 43211',
                    'contact_email' => '<EMAIL>',
                    'social_media' => (object) [
                        'facebook' => 'liquidlightsfestival',
                        'instagram' => 'liquidlightsfestival',
                        'twitter' => 'liquidlightsfest'
                    ],
                    'tags' => ['festival', 'outdoor', 'music', 'lights'],
                    'featured_image' => 'https://images.unsplash.com/photo-1516450360452-9312f5e86fc7?auto=format&fit=crop&w=800&h=600',
                    'gallery_images' => [
                        'https://images.unsplash.com/photo-1516450360452-9312f5e86fc7?auto=format&fit=crop&w=400&h=300',
                        'https://images.unsplash.com/photo-1571266028243-d220c9c3b8c2?auto=format&fit=crop&w=400&h=300'
                    ],
                    'created_at' => '2025-07-01 10:00:00',
                    'updated_at' => '2025-07-13 10:00:00',
                    'ticket_types' => collect([
                        (object) ['id' => 3, 'name' => 'Early Bird', 'price' => 1200, 'quantity' => 500, 'available' => 200],
                        (object) ['id' => 4, 'name' => 'Regular', 'price' => 1800, 'quantity' => 500, 'available' => 400]
                    ]),
                    'artists' => collect([
                        (object) ['id' => 2, 'name' => 'Liquid Beats', 'image' => 'https://images.unsplash.com/photo-1516450360452-9312f5e86fc7?auto=format&fit=crop&w=200&h=200']
                    ])
                ],
                (object) [
                    'id' => 3,
                    'title' => 'Liquid Bass Drop',
                    'description' => 'Heavy bass, liquid beats, and unforgettable drops in an underground setting with state-of-the-art sound systems.',
                    'short_description' => 'Heavy bass, liquid beats, and unforgettable drops',
                    'event_date' => '2025-09-05 22:00:00',
                    'event_time' => '22:00:00',
                    'start_time' => '22:00:00',
                    'end_time' => '03:00:00',
                    'venue' => 'Liquid Lights Underground',
                    'venue_name' => 'Liquid Lights Underground',
                    'venue_address' => 'Lower Parel, Mumbai, Maharashtra 400013',
                    'location' => 'Mumbai, Maharashtra',
                    'banner_image' => 'https://images.unsplash.com/photo-1571266028243-d220c9c3b8c2?auto=format&fit=crop&w=800&h=600',
                    'thumbnail_image' => 'https://images.unsplash.com/photo-1571266028243-d220c9c3b8c2?auto=format&fit=crop&w=400&h=300',
                    'is_featured' => true,
                    'status' => 'published',
                    'capacity' => 300,
                    'min_price' => 2000,
                    'max_price' => 4000,
                    'category' => 'Bass',
                    'duration' => '5 hours',
                    'age_restriction' => '21+',
                    'dress_code' => 'Underground Style',
                    'terms_conditions' => 'Underground event terms apply',
                    'refund_policy' => 'No refunds for underground events',
                    'contact_phone' => '+91 98765 43212',
                    'contact_email' => '<EMAIL>',
                    'social_media' => (object) [
                        'facebook' => 'liquidlightsunderground',
                        'instagram' => 'liquidlightsunderground',
                        'twitter' => 'liquidunderground'
                    ],
                    'tags' => ['bass', 'underground', 'heavy', 'drops'],
                    'featured_image' => 'https://images.unsplash.com/photo-1571266028243-d220c9c3b8c2?auto=format&fit=crop&w=800&h=600',
                    'gallery_images' => [
                        'https://images.unsplash.com/photo-1571266028243-d220c9c3b8c2?auto=format&fit=crop&w=400&h=300',
                        'https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?auto=format&fit=crop&w=400&h=300'
                    ],
                    'created_at' => '2025-07-01 10:00:00',
                    'updated_at' => '2025-07-13 10:00:00',
                    'ticket_types' => collect([
                        (object) ['id' => 5, 'name' => 'General', 'price' => 2000, 'quantity' => 200, 'available' => 150],
                        (object) ['id' => 6, 'name' => 'VIP', 'price' => 4000, 'quantity' => 100, 'available' => 75]
                    ]),
                    'artists' => collect([
                        (object) ['id' => 3, 'name' => 'Bass Master X', 'image' => 'https://images.unsplash.com/photo-1571266028243-d220c9c3b8c2?auto=format&fit=crop&w=200&h=200']
                    ])
                ]
            ]);

            $upcomingEvents = $featuredEvents;

            $popularArtists = collect([
                (object) [
                    'id' => 1,
                    'name' => 'DJ Neon Storm',
                    'bio' => 'Electronic music pioneer with over 10 years of experience in creating unforgettable nightlife experiences.',
                    'short_bio' => 'Electronic music pioneer',
                    'image' => 'https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?auto=format&fit=crop&w=400&h=400',
                    'profile_image' => 'https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?auto=format&fit=crop&w=400&h=400',
                    'genre' => 'Electronic, Techno',
                    'country' => 'India',
                    'social_media' => (object) [
                        'instagram' => '@djneonstorm',
                        'twitter' => '@djneonstorm',
                        'spotify' => 'DJ Neon Storm'
                    ],
                    'is_active' => true,
                    'events_count' => 5,
                    'created_at' => '2025-01-01 10:00:00',
                    'updated_at' => '2025-07-13 10:00:00'
                ],
                (object) [
                    'id' => 2,
                    'name' => 'Liquid Beats',
                    'bio' => 'Bass master extraordinaire known for creating liquid soundscapes that move souls and bodies.',
                    'short_bio' => 'Bass master extraordinaire',
                    'image' => 'https://images.unsplash.com/photo-1516450360452-9312f5e86fc7?auto=format&fit=crop&w=400&h=400',
                    'profile_image' => 'https://images.unsplash.com/photo-1516450360452-9312f5e86fc7?auto=format&fit=crop&w=400&h=400',
                    'genre' => 'Bass, Dubstep',
                    'country' => 'India',
                    'social_media' => (object) [
                        'instagram' => '@liquidbeats',
                        'twitter' => '@liquidbeats',
                        'spotify' => 'Liquid Beats'
                    ],
                    'is_active' => true,
                    'events_count' => 3,
                    'created_at' => '2025-01-01 10:00:00',
                    'updated_at' => '2025-07-13 10:00:00'
                ],
                (object) [
                    'id' => 3,
                    'name' => 'Bass Master X',
                    'bio' => 'Underground bass specialist bringing the deepest drops and heaviest beats to the liquid lights scene.',
                    'short_bio' => 'Underground bass specialist',
                    'image' => 'https://images.unsplash.com/photo-1571266028243-d220c9c3b8c2?auto=format&fit=crop&w=400&h=400',
                    'profile_image' => 'https://images.unsplash.com/photo-1571266028243-d220c9c3b8c2?auto=format&fit=crop&w=400&h=400',
                    'genre' => 'Bass, Trap',
                    'country' => 'India',
                    'social_media' => (object) [
                        'instagram' => '@bassmasterx',
                        'twitter' => '@bassmasterx',
                        'spotify' => 'Bass Master X'
                    ],
                    'is_active' => true,
                    'events_count' => 2,
                    'created_at' => '2025-01-01 10:00:00',
                    'updated_at' => '2025-07-13 10:00:00'
                ]
            ]);

            $sponsors = collect([
                (object) [
                    'id' => 1,
                    'name' => 'TechCorp Solutions',
                    'description' => 'Leading technology solutions provider for events and entertainment',
                    'logo' => '/images/event-placeholder.svg',
                    'logo_url' => '/images/event-placeholder.svg',
                    'website' => 'https://techcorp.com',
                    'contact_email' => '<EMAIL>',
                    'contact_phone' => '+91 98765 43210',
                    'tier' => 'platinum',
                    'is_active' => true,
                    'created_at' => '2025-01-01 10:00:00',
                    'updated_at' => '2025-07-13 10:00:00'
                ],
                (object) [
                    'id' => 2,
                    'name' => 'Mumbai Music Store',
                    'description' => 'Your one-stop destination for all music equipment and instruments',
                    'logo' => '/images/event-placeholder.svg',
                    'logo_url' => '/images/event-placeholder.svg',
                    'website' => 'https://mumbaimusicstore.com',
                    'contact_email' => '<EMAIL>',
                    'contact_phone' => '+91 98765 43211',
                    'tier' => 'gold',
                    'is_active' => true,
                    'created_at' => '2025-01-01 10:00:00',
                    'updated_at' => '2025-07-13 10:00:00'
                ],
                (object) [
                    'id' => 3,
                    'name' => 'Event Catering Co',
                    'description' => 'Premium catering services for nightlife and entertainment events',
                    'logo' => '/images/event-placeholder.svg',
                    'logo_url' => '/images/event-placeholder.svg',
                    'website' => 'https://eventcatering.com',
                    'contact_email' => '<EMAIL>',
                    'contact_phone' => '+91 98765 43212',
                    'tier' => 'silver',
                    'is_active' => true,
                    'created_at' => '2025-01-01 10:00:00',
                    'updated_at' => '2025-07-13 10:00:00'
                ]
            ]);

            // Log the error for debugging
            \Log::error('Database connection failed, using demo data: ' . $e->getMessage());
        }

        return view('public.index', compact('featuredEvents', 'upcomingEvents', 'popularArtists', 'sponsors'));
    }

    /**
     * Display events listing page
     */
    public function events(Request $request)
    {
        try {
            // Test database connection first
            \DB::connection()->getPdo();

            $query = Event::where('status', 'published')
                ->where('event_date', '>=', Carbon::now())
                ->with(['ticketTypes', 'artists', 'activeSponsors.sponsor']);

        // Apply filters
        if ($request->has('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%")
                  ->orWhere('venue_name', 'like', "%{$search}%");
            });
        }

        if ($request->has('category')) {
            // Add category filter when categories are implemented
        }

        if ($request->has('date_from')) {
            $query->whereDate('event_date', '>=', $request->date_from);
        }

        if ($request->has('date_to')) {
            $query->whereDate('event_date', '<=', $request->date_to);
        }

        if ($request->has('price_min')) {
            $query->whereHas('ticketTypes', function($q) use ($request) {
                $q->where('price', '>=', $request->price_min);
            });
        }

        if ($request->has('price_max')) {
            $query->whereHas('ticketTypes', function($q) use ($request) {
                $q->where('price', '<=', $request->price_max);
            });
        }

        // Sorting
        $sortBy = $request->get('sort', 'date');
        switch ($sortBy) {
            case 'price_low':
                $query->join('ticket_types', 'events.id', '=', 'ticket_types.event_id')
                      ->orderBy('ticket_types.price', 'asc');
                break;
            case 'price_high':
                $query->join('ticket_types', 'events.id', '=', 'ticket_types.event_id')
                      ->orderBy('ticket_types.price', 'desc');
                break;
            case 'popularity':
                // Order by ticket sales
                $query->withCount(['bookings' => function($q) {
                    $q->where('payment_status', 'paid');
                }])->orderBy('bookings_count', 'desc');
                break;
            default:
                $query->orderBy('event_date', 'asc');
        }

            $events = $query->paginate(12);

        } catch (\Exception $e) {
            // Use demo data when database is not available
            $demoEvents = collect([
                (object) [
                    'id' => 1,
                    'title' => 'Electronic Night 2025',
                    'description' => 'Experience the ultimate electronic music journey with world-class DJs',
                    'short_description' => 'Ultimate electronic music journey with world-class DJs',
                    'event_date' => '2025-08-15 21:00:00',
                    'event_time' => '21:00:00',
                    'start_time' => '21:00:00',
                    'end_time' => '03:00:00',
                    'venue' => 'Liquid Lights Main Floor',
                    'venue_name' => 'Liquid Lights Main Floor',
                    'venue_address' => 'Bandra West, Mumbai, Maharashtra 400050',
                    'location' => 'Mumbai, Maharashtra',
                    'banner_image' => 'https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?auto=format&fit=crop&w=800&h=600',
                    'thumbnail_image' => 'https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?auto=format&fit=crop&w=400&h=300',
                    'is_featured' => true,
                    'status' => 'published',
                    'capacity' => 500,
                    'min_price' => 1500,
                    'max_price' => 3000,
                    'category' => 'Electronic',
                    'ticket_types' => collect([
                        (object) ['name' => 'General', 'price' => 1500],
                        (object) ['name' => 'VIP', 'price' => 3000]
                    ]),
                    'artists' => collect([
                        (object) ['name' => 'DJ Neon Storm']
                    ])
                ],
                (object) [
                    'id' => 2,
                    'title' => 'Neon Nights Festival',
                    'description' => 'A spectacular light and music festival under neon skies',
                    'short_description' => 'Spectacular light and music festival under neon skies',
                    'event_date' => '2025-08-22 20:00:00',
                    'event_time' => '20:00:00',
                    'start_time' => '20:00:00',
                    'end_time' => '04:00:00',
                    'venue' => 'Liquid Lights Outdoor Arena',
                    'venue_name' => 'Liquid Lights Outdoor Arena',
                    'venue_address' => 'Juhu Beach, Mumbai, Maharashtra 400049',
                    'location' => 'Mumbai, Maharashtra',
                    'banner_image' => 'https://images.unsplash.com/photo-1516450360452-9312f5e86fc7?auto=format&fit=crop&w=800&h=600',
                    'thumbnail_image' => 'https://images.unsplash.com/photo-1516450360452-9312f5e86fc7?auto=format&fit=crop&w=400&h=300',
                    'is_featured' => true,
                    'status' => 'published',
                    'capacity' => 1000,
                    'min_price' => 1200,
                    'max_price' => 1800,
                    'category' => 'Festival',
                    'ticket_types' => collect([
                        (object) ['name' => 'Early Bird', 'price' => 1200],
                        (object) ['name' => 'Regular', 'price' => 1800]
                    ]),
                    'artists' => collect([
                        (object) ['name' => 'Liquid Beats']
                    ])
                ],
                (object) [
                    'id' => 3,
                    'title' => 'Liquid Bass Drop',
                    'description' => 'Heavy bass, liquid beats, and unforgettable drops',
                    'short_description' => 'Heavy bass, liquid beats, and unforgettable drops',
                    'event_date' => '2025-09-05 22:00:00',
                    'event_time' => '22:00:00',
                    'start_time' => '22:00:00',
                    'end_time' => '03:00:00',
                    'venue' => 'Liquid Lights Underground',
                    'venue_name' => 'Liquid Lights Underground',
                    'venue_address' => 'Lower Parel, Mumbai, Maharashtra 400013',
                    'location' => 'Mumbai, Maharashtra',
                    'banner_image' => 'https://images.unsplash.com/photo-1571266028243-d220c9c3b8c2?auto=format&fit=crop&w=800&h=600',
                    'thumbnail_image' => 'https://images.unsplash.com/photo-1571266028243-d220c9c3b8c2?auto=format&fit=crop&w=400&h=300',
                    'is_featured' => true,
                    'status' => 'published',
                    'capacity' => 300,
                    'min_price' => 2000,
                    'max_price' => 4000,
                    'category' => 'Bass',
                    'ticket_types' => collect([
                        (object) ['name' => 'General', 'price' => 2000],
                        (object) ['name' => 'VIP', 'price' => 4000]
                    ]),
                    'artists' => collect([
                        (object) ['name' => 'Bass Master X']
                    ])
                ]
            ]);

            // Apply basic search filter to demo data
            if ($request->has('search')) {
                $search = strtolower($request->search);
                $demoEvents = $demoEvents->filter(function($event) use ($search) {
                    return str_contains(strtolower($event->title), $search) ||
                           str_contains(strtolower($event->description), $search) ||
                           str_contains(strtolower($event->venue), $search);
                });
            }

            // Create a mock paginator for demo data
            $events = new \Illuminate\Pagination\LengthAwarePaginator(
                $demoEvents->take(12),
                $demoEvents->count(),
                12,
                1,
                ['path' => request()->url(), 'pageName' => 'page']
            );
        }

        return view('public.events.index', compact('events'));
    }

    /**
     * Display single event page
     */
    public function event($id)
    {
        $event = Event::where('status', 'published')
            ->with(['ticketTypes' => function($query) {
                $query->where('is_active', true)
                      ->where('sale_start_date', '<=', Carbon::now())
                      ->where('sale_end_date', '>=', Carbon::now());
            }, 'artists', 'activeSponsors.sponsor', 'activeSponsors.sponsorshipTier'])
            ->findOrFail($id);

        // Get related events
        $relatedEvents = Event::where('status', 'published')
            ->where('id', '!=', $event->id)
            ->where('event_date', '>=', Carbon::now())
            ->with(['ticketTypes', 'artists'])
            ->limit(4)
            ->get();

        return view('public.events.show', compact('event', 'relatedEvents'));
    }

    /**
     * Display artist page
     */
    public function artist($id)
    {
        $artist = Artist::where('is_active', true)->findOrFail($id);

        $events = Event::where('status', 'published')
            ->whereHas('artists', function($query) use ($id) {
                $query->where('artists.id', $id);
            })
            ->where('event_date', '>=', Carbon::now())
            ->with(['ticketTypes', 'artists'])
            ->orderBy('event_date')
            ->paginate(8);

        return view('public.artists.show', compact('artist', 'events'));
    }

    /**
     * Search functionality
     */
    public function search(Request $request)
    {
        $query = $request->get('q');

        if (!$query) {
            return redirect()->route('public.events');
        }

        $events = Event::where('status', 'published')
            ->where('event_date', '>=', Carbon::now())
            ->where(function($q) use ($query) {
                $q->where('title', 'like', "%{$query}%")
                  ->orWhere('description', 'like', "%{$query}%")
                  ->orWhere('venue_name', 'like', "%{$query}%");
            })
            ->with(['ticketTypes', 'artists'])
            ->paginate(12);

        return view('public.search', compact('events', 'query'));
    }
}
