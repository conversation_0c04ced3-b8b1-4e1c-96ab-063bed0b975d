<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Event;
use App\Models\Artist;
use Carbon\Carbon;

class PublicController extends Controller
{
    /**
     * Display the homepage
     */
    public function index()
    {
        try {
            // Test database connection first
            \DB::connection()->getPdo();

            // Get featured events
            $featuredEvents = Event::where('status', 'published')
                ->where('is_featured', true)
                ->where('event_date', '>=', Carbon::now())
                ->with(['ticketTypes', 'artists'])
                ->orderBy('event_date')
                ->limit(6)
                ->get();

            // Get upcoming events
            $upcomingEvents = Event::where('status', 'published')
                ->where('event_date', '>=', Carbon::now())
                ->with(['ticketTypes', 'artists'])
                ->orderBy('event_date')
                ->limit(8)
                ->get();

            // Get popular artists
            $popularArtists = Artist::where('is_active', true)
                ->withCount(['events' => function($query) {
                    $query->where('status', 'published')
                          ->where('event_date', '>=', Carbon::now());
                }])
                ->having('events_count', '>', 0)
                ->orderBy('events_count', 'desc')
                ->limit(6)
                ->get();

            // Get sponsors
            $sponsors = \App\Models\Sponsor::where('is_active', true)
                ->orderBy('name')
                ->limit(6)
                ->get();

        } catch (\Exception $e) {
            // Demo data when database is not available
            $featuredEvents = collect([
                (object) [
                    'id' => 1,
                    'title' => 'Electronic Night 2025',
                    'description' => 'Experience the ultimate electronic music journey with world-class DJs',
                    'event_date' => '2025-08-15 21:00:00',
                    'venue' => 'Liquid Lights Main Floor',
                    'banner_image' => 'https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?auto=format&fit=crop&w=800&h=600',
                    'is_featured' => true,
                    'status' => 'published',
                    'ticket_types' => collect([
                        (object) ['name' => 'General', 'price' => 1500],
                        (object) ['name' => 'VIP', 'price' => 3000]
                    ])
                ],
                (object) [
                    'id' => 2,
                    'title' => 'Neon Nights Festival',
                    'description' => 'A spectacular light and music festival under neon skies',
                    'event_date' => '2025-08-22 20:00:00',
                    'venue' => 'Liquid Lights Outdoor Arena',
                    'banner_image' => 'https://images.unsplash.com/photo-1516450360452-9312f5e86fc7?auto=format&fit=crop&w=800&h=600',
                    'is_featured' => true,
                    'status' => 'published',
                    'ticket_types' => collect([
                        (object) ['name' => 'Early Bird', 'price' => 1200],
                        (object) ['name' => 'Regular', 'price' => 1800]
                    ])
                ],
                (object) [
                    'id' => 3,
                    'title' => 'Liquid Bass Drop',
                    'description' => 'Heavy bass, liquid beats, and unforgettable drops',
                    'event_date' => '2025-09-05 22:00:00',
                    'venue' => 'Liquid Lights Underground',
                    'banner_image' => 'https://images.unsplash.com/photo-1571266028243-d220c9c3b8c2?auto=format&fit=crop&w=800&h=600',
                    'is_featured' => true,
                    'status' => 'published',
                    'ticket_types' => collect([
                        (object) ['name' => 'General', 'price' => 2000],
                        (object) ['name' => 'VIP', 'price' => 4000]
                    ])
                ]
            ]);

            $upcomingEvents = $featuredEvents;

            $popularArtists = collect([
                (object) [
                    'id' => 1,
                    'name' => 'DJ Neon Storm',
                    'bio' => 'Electronic music pioneer',
                    'image' => 'https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?auto=format&fit=crop&w=400&h=400',
                    'is_active' => true
                ],
                (object) [
                    'id' => 2,
                    'name' => 'Liquid Beats',
                    'bio' => 'Bass master extraordinaire',
                    'image' => 'https://images.unsplash.com/photo-1516450360452-9312f5e86fc7?auto=format&fit=crop&w=400&h=400',
                    'is_active' => true
                ]
            ]);

            $sponsors = collect([
                (object) [
                    'id' => 1,
                    'name' => 'TechCorp Solutions',
                    'logo' => '/images/event-placeholder.svg',
                    'is_active' => true
                ],
                (object) [
                    'id' => 2,
                    'name' => 'Mumbai Music Store',
                    'logo' => '/images/event-placeholder.svg',
                    'is_active' => true
                ]
            ]);

            // Log the error for debugging
            \Log::error('Database connection failed, using demo data: ' . $e->getMessage());
        }

        return view('public.index', compact('featuredEvents', 'upcomingEvents', 'popularArtists', 'sponsors'));
    }

    /**
     * Display events listing page
     */
    public function events(Request $request)
    {
        $query = Event::where('status', 'published')
            ->where('event_date', '>=', Carbon::now())
            ->with(['ticketTypes', 'artists', 'activeSponsors.sponsor']);

        // Apply filters
        if ($request->has('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%")
                  ->orWhere('venue_name', 'like', "%{$search}%");
            });
        }

        if ($request->has('category')) {
            // Add category filter when categories are implemented
        }

        if ($request->has('date_from')) {
            $query->whereDate('event_date', '>=', $request->date_from);
        }

        if ($request->has('date_to')) {
            $query->whereDate('event_date', '<=', $request->date_to);
        }

        if ($request->has('price_min')) {
            $query->whereHas('ticketTypes', function($q) use ($request) {
                $q->where('price', '>=', $request->price_min);
            });
        }

        if ($request->has('price_max')) {
            $query->whereHas('ticketTypes', function($q) use ($request) {
                $q->where('price', '<=', $request->price_max);
            });
        }

        // Sorting
        $sortBy = $request->get('sort', 'date');
        switch ($sortBy) {
            case 'price_low':
                $query->join('ticket_types', 'events.id', '=', 'ticket_types.event_id')
                      ->orderBy('ticket_types.price', 'asc');
                break;
            case 'price_high':
                $query->join('ticket_types', 'events.id', '=', 'ticket_types.event_id')
                      ->orderBy('ticket_types.price', 'desc');
                break;
            case 'popularity':
                // Order by ticket sales
                $query->withCount(['bookings' => function($q) {
                    $q->where('payment_status', 'paid');
                }])->orderBy('bookings_count', 'desc');
                break;
            default:
                $query->orderBy('event_date', 'asc');
        }

        $events = $query->paginate(12);

        return view('public.events.index', compact('events'));
    }

    /**
     * Display single event page
     */
    public function event($id)
    {
        $event = Event::where('status', 'published')
            ->with(['ticketTypes' => function($query) {
                $query->where('is_active', true)
                      ->where('sale_start_date', '<=', Carbon::now())
                      ->where('sale_end_date', '>=', Carbon::now());
            }, 'artists', 'activeSponsors.sponsor', 'activeSponsors.sponsorshipTier'])
            ->findOrFail($id);

        // Get related events
        $relatedEvents = Event::where('status', 'published')
            ->where('id', '!=', $event->id)
            ->where('event_date', '>=', Carbon::now())
            ->with(['ticketTypes', 'artists'])
            ->limit(4)
            ->get();

        return view('public.events.show', compact('event', 'relatedEvents'));
    }

    /**
     * Display artist page
     */
    public function artist($id)
    {
        $artist = Artist::where('is_active', true)->findOrFail($id);

        $events = Event::where('status', 'published')
            ->whereHas('artists', function($query) use ($id) {
                $query->where('artists.id', $id);
            })
            ->where('event_date', '>=', Carbon::now())
            ->with(['ticketTypes', 'artists'])
            ->orderBy('event_date')
            ->paginate(8);

        return view('public.artists.show', compact('artist', 'events'));
    }

    /**
     * Search functionality
     */
    public function search(Request $request)
    {
        $query = $request->get('q');

        if (!$query) {
            return redirect()->route('public.events');
        }

        $events = Event::where('status', 'published')
            ->where('event_date', '>=', Carbon::now())
            ->where(function($q) use ($query) {
                $q->where('title', 'like', "%{$query}%")
                  ->orWhere('description', 'like', "%{$query}%")
                  ->orWhere('venue_name', 'like', "%{$query}%");
            })
            ->with(['ticketTypes', 'artists'])
            ->paginate(12);

        return view('public.search', compact('events', 'query'));
    }
}
