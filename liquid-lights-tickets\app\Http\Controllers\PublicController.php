<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Event;
use App\Models\Artist;
use Carbon\Carbon;

class PublicController extends Controller
{
    /**
     * Display the homepage
     */
    public function index()
    {
        // Get featured events
        $featuredEvents = Event::where('status', 'published')
            ->where('is_featured', true)
            ->where('event_date', '>=', Carbon::now())
            ->with(['ticketTypes', 'artists'])
            ->orderBy('event_date')
            ->limit(6)
            ->get();

        // Get upcoming events
        $upcomingEvents = Event::where('status', 'published')
            ->where('event_date', '>=', Carbon::now())
            ->with(['ticketTypes', 'artists'])
            ->orderBy('event_date')
            ->limit(8)
            ->get();

        // Get popular artists
        $popularArtists = Artist::where('is_active', true)
            ->withCount(['events' => function($query) {
                $query->where('status', 'published')
                      ->where('event_date', '>=', Carbon::now());
            }])
            ->having('events_count', '>', 0)
            ->orderBy('events_count', 'desc')
            ->limit(6)
            ->get();

        return view('public.index', compact('featuredEvents', 'upcomingEvents', 'popularArtists'));
    }

    /**
     * Display events listing page
     */
    public function events(Request $request)
    {
        $query = Event::where('status', 'published')
            ->where('event_date', '>=', Carbon::now())
            ->with(['ticketTypes', 'artists', 'activeSponsors.sponsor']);

        // Apply filters
        if ($request->has('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%")
                  ->orWhere('venue_name', 'like', "%{$search}%");
            });
        }

        if ($request->has('category')) {
            // Add category filter when categories are implemented
        }

        if ($request->has('date_from')) {
            $query->whereDate('event_date', '>=', $request->date_from);
        }

        if ($request->has('date_to')) {
            $query->whereDate('event_date', '<=', $request->date_to);
        }

        if ($request->has('price_min')) {
            $query->whereHas('ticketTypes', function($q) use ($request) {
                $q->where('price', '>=', $request->price_min);
            });
        }

        if ($request->has('price_max')) {
            $query->whereHas('ticketTypes', function($q) use ($request) {
                $q->where('price', '<=', $request->price_max);
            });
        }

        // Sorting
        $sortBy = $request->get('sort', 'date');
        switch ($sortBy) {
            case 'price_low':
                $query->join('ticket_types', 'events.id', '=', 'ticket_types.event_id')
                      ->orderBy('ticket_types.price', 'asc');
                break;
            case 'price_high':
                $query->join('ticket_types', 'events.id', '=', 'ticket_types.event_id')
                      ->orderBy('ticket_types.price', 'desc');
                break;
            case 'popularity':
                // Order by ticket sales
                $query->withCount(['bookings' => function($q) {
                    $q->where('payment_status', 'paid');
                }])->orderBy('bookings_count', 'desc');
                break;
            default:
                $query->orderBy('event_date', 'asc');
        }

        $events = $query->paginate(12);

        return view('public.events.index', compact('events'));
    }

    /**
     * Display single event page
     */
    public function event($id)
    {
        $event = Event::where('status', 'published')
            ->with(['ticketTypes' => function($query) {
                $query->where('is_active', true)
                      ->where('sale_start_date', '<=', Carbon::now())
                      ->where('sale_end_date', '>=', Carbon::now());
            }, 'artists', 'activeSponsors.sponsor', 'activeSponsors.sponsorshipTier'])
            ->findOrFail($id);

        // Get related events
        $relatedEvents = Event::where('status', 'published')
            ->where('id', '!=', $event->id)
            ->where('event_date', '>=', Carbon::now())
            ->with(['ticketTypes', 'artists'])
            ->limit(4)
            ->get();

        return view('public.events.show', compact('event', 'relatedEvents'));
    }

    /**
     * Display artist page
     */
    public function artist($id)
    {
        $artist = Artist::where('is_active', true)->findOrFail($id);

        $events = Event::where('status', 'published')
            ->whereHas('artists', function($query) use ($id) {
                $query->where('artists.id', $id);
            })
            ->where('event_date', '>=', Carbon::now())
            ->with(['ticketTypes', 'artists'])
            ->orderBy('event_date')
            ->paginate(8);

        return view('public.artists.show', compact('artist', 'events'));
    }

    /**
     * Search functionality
     */
    public function search(Request $request)
    {
        $query = $request->get('q');

        if (!$query) {
            return redirect()->route('public.events');
        }

        $events = Event::where('status', 'published')
            ->where('event_date', '>=', Carbon::now())
            ->where(function($q) use ($query) {
                $q->where('title', 'like', "%{$query}%")
                  ->orWhere('description', 'like', "%{$query}%")
                  ->orWhere('venue_name', 'like', "%{$query}%");
            })
            ->with(['ticketTypes', 'artists'])
            ->paginate(12);

        return view('public.search', compact('events', 'query'));
    }
}
