/* Mobile Enhancements for Liquid Lights */

/* Touch-friendly interactions */
.touch-target {
    min-height: 44px;
    min-width: 44px;
}

/* Smooth scrolling on mobile */
html {
    -webkit-overflow-scrolling: touch;
    scroll-behavior: smooth;
}

/* Mobile-optimized buttons */
@media (max-width: 768px) {
    .btn {
        padding: 12px 24px;
        font-size: 16px;
        min-height: 48px;
    }
    
    .btn-sm {
        padding: 8px 16px;
        font-size: 14px;
        min-height: 40px;
    }
}

/* Prevent zoom on input focus */
@media (max-width: 768px) {
    input[type="text"],
    input[type="email"],
    input[type="password"],
    input[type="tel"],
    input[type="url"],
    textarea,
    select {
        font-size: 16px;
    }
}

/* Safe area handling for notched devices */
@supports (padding: max(0px)) {
    .safe-area-top {
        padding-top: max(1rem, env(safe-area-inset-top));
    }
    
    .safe-area-bottom {
        padding-bottom: max(1rem, env(safe-area-inset-bottom));
    }
    
    .safe-area-left {
        padding-left: max(1rem, env(safe-area-inset-left));
    }
    
    .safe-area-right {
        padding-right: max(1rem, env(safe-area-inset-right));
    }
}

/* Mobile navigation enhancements */
@media (max-width: 768px) {
    .mobile-nav {
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        background: white;
        border-top: 1px solid #e5e7eb;
        padding: 8px 0;
        z-index: 50;
    }
    
    .mobile-nav-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 8px;
        text-decoration: none;
        color: #6b7280;
        font-size: 12px;
    }
    
    .mobile-nav-item.active {
        color: #3b82f6;
    }
    
    .mobile-nav-icon {
        width: 24px;
        height: 24px;
        margin-bottom: 4px;
    }
}

/* Pull-to-refresh indicator */
.pull-to-refresh {
    position: fixed;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 8px 16px;
    border-radius: 0 0 8px 8px;
    font-size: 14px;
    z-index: 1000;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.pull-to-refresh.visible {
    opacity: 1;
}

/* Haptic feedback simulation */
.haptic-light {
    animation: haptic-pulse 0.1s ease-out;
}

@keyframes haptic-pulse {
    0% { transform: scale(1); }
    50% { transform: scale(0.98); }
    100% { transform: scale(1); }
}

/* Loading states */
.loading-skeleton {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading-shimmer 1.5s infinite;
}

@keyframes loading-shimmer {
    0% { background-position: -200% 0; }
    100% { background-position: 200% 0; }
}

/* Swipe gestures */
.swipeable {
    touch-action: pan-y;
    user-select: none;
}

.swipe-left {
    transform: translateX(-100%);
    transition: transform 0.3s ease;
}

.swipe-right {
    transform: translateX(100%);
    transition: transform 0.3s ease;
}

/* Mobile-optimized forms */
@media (max-width: 768px) {
    .form-group {
        margin-bottom: 1.5rem;
    }
    
    .form-label {
        font-size: 16px;
        font-weight: 500;
        margin-bottom: 0.5rem;
    }
    
    .form-input {
        width: 100%;
        padding: 12px 16px;
        font-size: 16px;
        border: 2px solid #e5e7eb;
        border-radius: 8px;
        transition: border-color 0.2s ease;
    }
    
    .form-input:focus {
        outline: none;
        border-color: #3b82f6;
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    .auto-dark {
        background-color: #1f2937;
        color: #f9fafb;
    }
    
    .auto-dark-border {
        border-color: #374151;
    }
    
    .auto-dark-text {
        color: #d1d5db;
    }
}
