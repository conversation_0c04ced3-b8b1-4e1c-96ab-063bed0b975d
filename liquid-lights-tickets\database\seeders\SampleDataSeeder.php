<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Event;
use App\Models\TicketType;
use App\Models\Sponsor;
use App\Models\EventSponsor;
use App\Models\SponsorshipTier;
use App\Models\PromoCode;
use Carbon\Carbon;

class SampleDataSeeder extends Seeder
{
    public function run(): void
    {
        // Create Events
        $events = [
            [
                'title' => 'Electronic Night 2025',
                'slug' => 'electronic-night-2025',
                'description' => 'An amazing night of electronic music featuring top DJs from around the world. Experience the best beats and rhythms in a spectacular venue.',
                'short_description' => 'Electronic music night with top DJs',
                'event_date' => Carbon::now()->addDays(30),
                'event_time' => '20:00:00',
                'venue_name' => 'Mumbai Convention Center',
                'venue_address' => 'Bandra Kurla Complex, Mumbai, Maharashtra 400051',
                'capacity' => 500,
                'status' => 'published',
                'is_featured' => true,
                'category' => 'Music',
                'created_by' => 1
            ],
            [
                'title' => 'Rock Concert Live',
                'slug' => 'rock-concert-live',
                'description' => 'Live rock performance by The Midnight Band. Get ready for an electrifying night of rock music with amazing stage presence.',
                'short_description' => 'Live rock concert experience',
                'event_date' => Carbon::now()->addDays(45),
                'event_time' => '19:30:00',
                'venue_name' => 'Phoenix Mills',
                'venue_address' => 'Lower Parel, Mumbai, Maharashtra 400013',
                'capacity' => 300,
                'status' => 'published',
                'is_featured' => true,
                'category' => 'Music',
                'created_by' => 1
            ],
            [
                'title' => 'Classical Music Evening',
                'slug' => 'classical-music-evening',
                'description' => 'An enchanting evening of classical Indian music featuring renowned vocalist Priya Sharma.',
                'short_description' => 'Classical Indian music performance',
                'event_date' => Carbon::now()->addDays(60),
                'event_time' => '18:00:00',
                'venue_name' => 'NCPA Auditorium',
                'venue_address' => 'Nariman Point, Mumbai, Maharashtra 400021',
                'capacity' => 200,
                'status' => 'published',
                'is_featured' => false,
                'category' => 'Music',
                'created_by' => 1
            ]
        ];

        foreach ($events as $eventData) {
            $event = Event::create($eventData);
            
            // Create ticket types for each event
            $this->createTicketTypes($event);
            
            echo "Created event: {$event->title}\n";
        }

        // Create Sponsors
        $this->createSponsors();

        // Create Promo Codes
        $this->createPromoCodes();

        echo "Sample data seeding completed!\n";
    }

    private function createTicketTypes(Event $event): void
    {
        $ticketTypes = [
            [
                'name' => 'General Admission',
                'price' => 1500.00,
                'quantity_available' => (int)($event->capacity * 0.6),
                'quantity_sold' => 0,
                'is_active' => true,
                'sale_start_date' => Carbon::now()->subDays(7),
                'sale_end_date' => $event->event_date->subHours(2),
            ],
            [
                'name' => 'VIP',
                'price' => 3000.00,
                'quantity_available' => (int)($event->capacity * 0.3),
                'quantity_sold' => 0,
                'is_active' => true,
                'sale_start_date' => Carbon::now()->subDays(7),
                'sale_end_date' => $event->event_date->subHours(2),
            ],
            [
                'name' => 'Early Bird',
                'price' => 1200.00,
                'quantity_available' => (int)($event->capacity * 0.1),
                'quantity_sold' => (int)($event->capacity * 0.1), // Sold out
                'is_active' => false,
                'sale_start_date' => Carbon::now()->subDays(30),
                'sale_end_date' => Carbon::now()->subDays(15),
            ]
        ];

        foreach ($ticketTypes as $ticketData) {
            $ticketData['event_id'] = $event->id;
            TicketType::create($ticketData);
        }
    }

    private function createSponsors(): void
    {
        $sponsors = [
            [
                'name' => 'TechCorp Solutions',
                'slug' => 'techcorp-solutions',
                'description' => 'Leading technology solutions provider',
                'website_url' => 'https://techcorp.com',
                'contact_person' => 'John Smith',
                'contact_email' => '<EMAIL>',
                'contact_phone' => '+91 98765 43210',
                'status' => 'active',
                'social_links' => [
                    'facebook' => 'https://facebook.com/techcorp',
                    'twitter' => 'https://twitter.com/techcorp',
                    'linkedin' => 'https://linkedin.com/company/techcorp'
                ]
            ],
            [
                'name' => 'Mumbai Music Store',
                'slug' => 'mumbai-music-store',
                'description' => 'Your one-stop shop for all musical instruments',
                'website_url' => 'https://mumbaimusicstore.com',
                'contact_person' => 'Priya Patel',
                'contact_email' => '<EMAIL>',
                'contact_phone' => '+91 98765 43211',
                'status' => 'active',
                'social_links' => [
                    'facebook' => 'https://facebook.com/mumbaimusicstore',
                    'instagram' => 'https://instagram.com/mumbaimusicstore'
                ]
            ],
            [
                'name' => 'Event Catering Co',
                'slug' => 'event-catering-co',
                'description' => 'Premium catering services for events',
                'website_url' => 'https://eventcatering.com',
                'contact_person' => 'Raj Kumar',
                'contact_email' => '<EMAIL>',
                'contact_phone' => '+91 98765 43212',
                'status' => 'active'
            ]
        ];

        foreach ($sponsors as $sponsorData) {
            $sponsor = Sponsor::create($sponsorData);
            
            // Create event sponsorships
            $this->createEventSponsorships($sponsor);
            
            echo "Created sponsor: {$sponsor->name}\n";
        }
    }

    private function createEventSponsorships(Sponsor $sponsor): void
    {
        $events = Event::all();
        $tiers = SponsorshipTier::all();

        if ($events->count() > 0 && $tiers->count() > 0) {
            // Assign random sponsorships
            $randomEvent = $events->random();
            $randomTier = $tiers->random();

            EventSponsor::create([
                'event_id' => $randomEvent->id,
                'sponsor_id' => $sponsor->id,
                'sponsorship_tier_id' => $randomTier->id,
                'amount' => $randomTier->price,
                'status' => 'active',
                'start_date' => Carbon::now(),
                'end_date' => $randomEvent->event_date->addDays(1),
            ]);
        }
    }

    private function createPromoCodes(): void
    {
        $promoCodes = [
            [
                'code' => 'EARLY2025',
                'discount_type' => 'percentage',
                'discount_value' => 15.00,
                'usage_limit' => 100,
                'used_count' => 0,
                'valid_from' => Carbon::now(),
                'valid_until' => Carbon::now()->addDays(90),
                'is_active' => true
            ],
            [
                'code' => 'NEWUSER',
                'discount_type' => 'fixed',
                'discount_value' => 200.00,
                'usage_limit' => 500,
                'used_count' => 0,
                'valid_from' => Carbon::now(),
                'valid_until' => Carbon::now()->addDays(365),
                'is_active' => true
            ],
            [
                'code' => 'VIP50',
                'discount_type' => 'percentage',
                'discount_value' => 10.00,
                'usage_limit' => 50,
                'used_count' => 0,
                'valid_from' => Carbon::now(),
                'valid_until' => Carbon::now()->addDays(60),
                'is_active' => true
            ]
        ];

        foreach ($promoCodes as $promoData) {
            PromoCode::create($promoData);
            echo "Created promo code: {$promoData['code']}\n";
        }
    }
}
