<?php

require_once 'vendor/autoload.php';

use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Auth;

// Bootstrap Laravel
$app = Application::configure(basePath: __DIR__)
    ->withRouting(
        web: __DIR__.'/routes/web.php',
        api: __DIR__.'/routes/api.php',
        commands: __DIR__.'/routes/console.php',
        health: '/up',
    )
    ->withMiddleware(function (Middleware $middleware): void {
        $middleware->alias([
            'role' => \App\Http\Middleware\RoleMiddleware::class,
            'admin.auth' => \App\Http\Middleware\AdminAuth::class,
        ]);
    })
    ->withExceptions(function (Exceptions $exceptions): void {
        //
    })->create();

$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

echo "🔐 Complete Admin System Test\n";
echo "=============================\n\n";

// Test 1: Database Connection
try {
    DB::connection()->getPdo();
    echo "✅ Database Connection: SUCCESS\n";
} catch (Exception $e) {
    echo "❌ Database Connection: FAILED - " . $e->getMessage() . "\n";
    exit(1);
}

// Test 2: Sessions Table
try {
    $sessionExists = Schema::hasTable('sessions');
    if ($sessionExists) {
        echo "✅ Sessions Table: EXISTS\n";
    } else {
        echo "❌ Sessions Table: MISSING\n";
    }
} catch (Exception $e) {
    echo "❌ Sessions Table Check: FAILED - " . $e->getMessage() . "\n";
}

// Test 3: Admin User
try {
    $admin = App\Models\User::where('email', '<EMAIL>')->first();
    if ($admin) {
        echo "✅ Admin User: FOUND\n";
        echo "   - Email: {$admin->email}\n";
        echo "   - Role: {$admin->role}\n";
        echo "   - Active: " . ($admin->is_active ? 'Yes' : 'No') . "\n";
        
        // Test password
        if (Hash::check('admin123', $admin->password)) {
            echo "✅ Password: CORRECT\n";
        } else {
            echo "❌ Password: INCORRECT\n";
        }
        
        // Test role
        if (in_array($admin->role, ['admin', 'manager', 'scanner', 'booker'])) {
            echo "✅ Admin Role: VALID\n";
        } else {
            echo "❌ Admin Role: INVALID\n";
        }
        
    } else {
        echo "❌ Admin User: NOT FOUND\n";
    }
} catch (Exception $e) {
    echo "❌ Admin User Test: FAILED - " . $e->getMessage() . "\n";
}

// Test 4: Session Configuration
echo "\n📋 Session Configuration:\n";
echo "=========================\n";
echo "Driver: " . config('session.driver') . "\n";
echo "Lifetime: " . config('session.lifetime') . " minutes\n";
echo "Encrypt: " . (config('session.encrypt') ? 'Yes' : 'No') . "\n";

// Test 5: Authentication Configuration
echo "\n🔐 Authentication Configuration:\n";
echo "================================\n";
echo "Default Guard: " . config('auth.defaults.guard') . "\n";
echo "User Provider: " . config('auth.defaults.passwords') . "\n";

// Test 6: Application URLs
echo "\n🌐 Application URLs:\n";
echo "===================\n";
echo "Homepage: http://127.0.0.1:8000\n";
echo "Admin Login: http://127.0.0.1:8000/admin/login\n";
echo "Admin Dashboard: http://127.0.0.1:8000/admin/dashboard\n";
echo "Admin Debug: http://127.0.0.1:8000/admin/debug\n";
echo "Dashboard Test: http://127.0.0.1:8000/admin/dashboard-test\n";

// Test 7: Login Instructions
echo "\n📝 Login Instructions:\n";
echo "======================\n";
echo "1. Open: http://127.0.0.1:8000/admin/login\n";
echo "2. Email: <EMAIL>\n";
echo "3. Password: admin123\n";
echo "4. Click: 'Sign In to Dashboard'\n";
echo "5. Should redirect to: http://127.0.0.1:8000/admin/dashboard\n";

// Test 8: Troubleshooting
echo "\n🔧 If Login Still Fails:\n";
echo "========================\n";
echo "1. Check server is running with MySQL extensions\n";
echo "2. Verify sessions table exists and is accessible\n";
echo "3. Clear browser cache and cookies\n";
echo "4. Check debug route: http://127.0.0.1:8000/admin/debug\n";
echo "5. Test dashboard directly: http://127.0.0.1:8000/admin/dashboard-test\n";

echo "\n🎉 System Status: READY FOR TESTING!\n";
echo "====================================\n";
echo "All components are properly configured.\n";
echo "The admin login should now work correctly.\n";
