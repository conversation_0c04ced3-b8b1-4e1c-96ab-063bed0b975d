<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class AdminAuth
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next)
    {
        // Simple session-based check
        if (!session('admin_logged_in') || !session('admin_user_id')) {
            return redirect('/admin/login')->with('error', 'Please login to access admin panel');
        }

        // Verify user still exists and is valid
        try {
            $user = \App\Models\User::find(session('admin_user_id'));

            if (!$user || !$user->is_active || !in_array($user->role, ['admin', 'manager', 'scanner', 'booker'])) {
                session()->forget(['admin_logged_in', 'admin_user_id']);
                return redirect('/admin/login')->with('error', 'Session expired or access denied');
            }

            // Set user for the request
            $request->attributes->set('admin_user', $user);

        } catch (\Exception $e) {
            session()->forget(['admin_logged_in', 'admin_user_id']);
            return redirect('/admin/login')->with('error', 'Authentication error');
        }

        return $next($request);
    }
}
