<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class AdminAuth
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next)
    {
        // Check if user is authenticated
        if (!Auth::check()) {
            return redirect('/admin/login')->with('error', 'Please login to access admin panel');
        }

        $user = Auth::user();

        // Check if user has admin privileges
        if (!in_array($user->role, ['admin', 'manager', 'scanner', 'booker'])) {
            Auth::logout();
            return redirect('/admin/login')->with('error', 'Access denied. Admin privileges required.');
        }

        // Check if user is active
        if (!$user->is_active) {
            Auth::logout();
            return redirect('/admin/login')->with('error', 'Account is deactivated.');
        }

        return $next($request);
    }
}
