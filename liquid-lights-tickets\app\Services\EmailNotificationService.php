<?php

namespace App\Services;

use App\Models\Booking;
use App\Models\Event;
use App\Models\User;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Log;
use Exception;

class EmailNotificationService
{
    /**
     * Send booking confirmation email
     */
    public function sendBookingConfirmation(Booking $booking)
    {
        try {
            $booking->load(['event', 'ticketType', 'user']);

            $data = [
                'booking' => $booking,
                'event' => $booking->event,
                'ticketType' => $booking->ticketType,
                'user' => $booking->user,
                'subject' => 'Booking Confirmation - ' . $booking->event->title
            ];

            Mail::send('emails.booking-confirmation', $data, function($message) use ($booking, $data) {
                $message->to($booking->user->email, $booking->user->name)
                        ->subject($data['subject']);
            });

            Log::info('Booking confirmation email sent', [
                'booking_id' => $booking->id,
                'user_email' => $booking->user->email
            ]);

            return ['success' => true];

        } catch (Exception $e) {
            Log::error('Failed to send booking confirmation email', [
                'booking_id' => $booking->id,
                'error' => $e->getMessage()
            ]);

            return ['success' => false, 'error' => $e->getMessage()];
        }
    }

    /**
     * Send payment success email
     */
    public function sendPaymentSuccess(Booking $booking)
    {
        try {
            $booking->load(['event', 'ticketType', 'user']);

            $data = [
                'booking' => $booking,
                'event' => $booking->event,
                'ticketType' => $booking->ticketType,
                'user' => $booking->user,
                'subject' => 'Payment Successful - ' . $booking->event->title
            ];

            Mail::send('emails.payment-success', $data, function($message) use ($booking, $data) {
                $message->to($booking->user->email, $booking->user->name)
                        ->subject($data['subject']);
            });

            Log::info('Payment success email sent', [
                'booking_id' => $booking->id,
                'user_email' => $booking->user->email
            ]);

            return ['success' => true];

        } catch (Exception $e) {
            Log::error('Failed to send payment success email', [
                'booking_id' => $booking->id,
                'error' => $e->getMessage()
            ]);

            return ['success' => false, 'error' => $e->getMessage()];
        }
    }

    /**
     * Send payment failure email
     */
    public function sendPaymentFailure(Booking $booking, string $reason = '')
    {
        try {
            $booking->load(['event', 'ticketType', 'user']);

            $data = [
                'booking' => $booking,
                'event' => $booking->event,
                'ticketType' => $booking->ticketType,
                'user' => $booking->user,
                'reason' => $reason,
                'subject' => 'Payment Failed - ' . $booking->event->title
            ];

            Mail::send('emails.payment-failure', $data, function($message) use ($booking, $data) {
                $message->to($booking->user->email, $booking->user->name)
                        ->subject($data['subject']);
            });

            Log::info('Payment failure email sent', [
                'booking_id' => $booking->id,
                'user_email' => $booking->user->email
            ]);

            return ['success' => true];

        } catch (Exception $e) {
            Log::error('Failed to send payment failure email', [
                'booking_id' => $booking->id,
                'error' => $e->getMessage()
            ]);

            return ['success' => false, 'error' => $e->getMessage()];
        }
    }

    /**
     * Send refund confirmation email
     */
    public function sendRefundConfirmation(Booking $booking, float $refundAmount, string $reason = '')
    {
        try {
            $booking->load(['event', 'ticketType', 'user']);

            $data = [
                'booking' => $booking,
                'event' => $booking->event,
                'ticketType' => $booking->ticketType,
                'user' => $booking->user,
                'refundAmount' => $refundAmount,
                'reason' => $reason,
                'subject' => 'Refund Processed - ' . $booking->event->title
            ];

            Mail::send('emails.refund-confirmation', $data, function($message) use ($booking, $data) {
                $message->to($booking->user->email, $booking->user->name)
                        ->subject($data['subject']);
            });

            Log::info('Refund confirmation email sent', [
                'booking_id' => $booking->id,
                'user_email' => $booking->user->email,
                'refund_amount' => $refundAmount
            ]);

            return ['success' => true];

        } catch (Exception $e) {
            Log::error('Failed to send refund confirmation email', [
                'booking_id' => $booking->id,
                'error' => $e->getMessage()
            ]);

            return ['success' => false, 'error' => $e->getMessage()];
        }
    }

    /**
     * Send event reminder email
     */
    public function sendEventReminder(Booking $booking, int $hoursBeforeEvent = 24)
    {
        try {
            $booking->load(['event', 'ticketType', 'user']);

            $data = [
                'booking' => $booking,
                'event' => $booking->event,
                'ticketType' => $booking->ticketType,
                'user' => $booking->user,
                'hoursBeforeEvent' => $hoursBeforeEvent,
                'subject' => 'Event Reminder - ' . $booking->event->title . ' is tomorrow!'
            ];

            Mail::send('emails.event-reminder', $data, function($message) use ($booking, $data) {
                $message->to($booking->user->email, $booking->user->name)
                        ->subject($data['subject']);
            });

            Log::info('Event reminder email sent', [
                'booking_id' => $booking->id,
                'user_email' => $booking->user->email,
                'hours_before' => $hoursBeforeEvent
            ]);

            return ['success' => true];

        } catch (Exception $e) {
            Log::error('Failed to send event reminder email', [
                'booking_id' => $booking->id,
                'error' => $e->getMessage()
            ]);

            return ['success' => false, 'error' => $e->getMessage()];
        }
    }

    /**
     * Send event cancellation email
     */
    public function sendEventCancellation(Event $event, string $reason = '')
    {
        try {
            $bookings = $event->bookings()
                ->where('payment_status', 'paid')
                ->with(['user', 'ticketType'])
                ->get();

            $successCount = 0;
            $failureCount = 0;

            foreach ($bookings as $booking) {
                try {
                    $data = [
                        'booking' => $booking,
                        'event' => $event,
                        'ticketType' => $booking->ticketType,
                        'user' => $booking->user,
                        'reason' => $reason,
                        'subject' => 'Event Cancelled - ' . $event->title
                    ];

                    Mail::send('emails.event-cancellation', $data, function($message) use ($booking, $data) {
                        $message->to($booking->user->email, $booking->user->name)
                                ->subject($data['subject']);
                    });

                    $successCount++;

                } catch (Exception $e) {
                    $failureCount++;
                    Log::error('Failed to send event cancellation email to user', [
                        'booking_id' => $booking->id,
                        'user_email' => $booking->user->email,
                        'error' => $e->getMessage()
                    ]);
                }
            }

            Log::info('Event cancellation emails sent', [
                'event_id' => $event->id,
                'total_bookings' => $bookings->count(),
                'success_count' => $successCount,
                'failure_count' => $failureCount
            ]);

            return [
                'success' => true,
                'total_sent' => $successCount,
                'total_failed' => $failureCount
            ];

        } catch (Exception $e) {
            Log::error('Failed to send event cancellation emails', [
                'event_id' => $event->id,
                'error' => $e->getMessage()
            ]);

            return ['success' => false, 'error' => $e->getMessage()];
        }
    }

    /**
     * Send bulk event reminders
     */
    public function sendBulkEventReminders(int $hoursBeforeEvent = 24)
    {
        try {
            $targetDateTime = now()->addHours($hoursBeforeEvent);
            
            $events = Event::where('status', 'published')
                ->whereDate('event_date', $targetDateTime->toDateString())
                ->whereBetween('event_time', [
                    $targetDateTime->subMinutes(30)->toTimeString(),
                    $targetDateTime->addMinutes(30)->toTimeString()
                ])
                ->with(['bookings' => function($query) {
                    $query->where('payment_status', 'paid')
                          ->where('reminder_sent', false)
                          ->with(['user', 'ticketType']);
                }])
                ->get();

            $totalSent = 0;
            $totalFailed = 0;

            foreach ($events as $event) {
                foreach ($event->bookings as $booking) {
                    $result = $this->sendEventReminder($booking, $hoursBeforeEvent);
                    
                    if ($result['success']) {
                        $booking->update(['reminder_sent' => true]);
                        $totalSent++;
                    } else {
                        $totalFailed++;
                    }
                }
            }

            Log::info('Bulk event reminders sent', [
                'hours_before' => $hoursBeforeEvent,
                'events_processed' => $events->count(),
                'total_sent' => $totalSent,
                'total_failed' => $totalFailed
            ]);

            return [
                'success' => true,
                'events_processed' => $events->count(),
                'total_sent' => $totalSent,
                'total_failed' => $totalFailed
            ];

        } catch (Exception $e) {
            Log::error('Failed to send bulk event reminders', [
                'error' => $e->getMessage()
            ]);

            return ['success' => false, 'error' => $e->getMessage()];
        }
    }

    /**
     * Send admin notification for new booking
     */
    public function sendAdminBookingNotification(Booking $booking)
    {
        try {
            $booking->load(['event', 'ticketType', 'user']);

            $adminEmails = User::role('admin')->pluck('email')->toArray();
            
            if (empty($adminEmails)) {
                return ['success' => true, 'message' => 'No admin emails configured'];
            }

            $data = [
                'booking' => $booking,
                'event' => $booking->event,
                'ticketType' => $booking->ticketType,
                'user' => $booking->user,
                'subject' => 'New Booking - ' . $booking->event->title
            ];

            Mail::send('emails.admin-booking-notification', $data, function($message) use ($adminEmails, $data) {
                $message->to($adminEmails)
                        ->subject($data['subject']);
            });

            Log::info('Admin booking notification sent', [
                'booking_id' => $booking->id,
                'admin_count' => count($adminEmails)
            ]);

            return ['success' => true];

        } catch (Exception $e) {
            Log::error('Failed to send admin booking notification', [
                'booking_id' => $booking->id,
                'error' => $e->getMessage()
            ]);

            return ['success' => false, 'error' => $e->getMessage()];
        }
    }
}
