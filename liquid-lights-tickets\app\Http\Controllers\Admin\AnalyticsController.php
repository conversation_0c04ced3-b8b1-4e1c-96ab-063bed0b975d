<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Event;
use App\Models\Booking;
use App\Models\User;
use App\Models\TicketType;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class AnalyticsController extends Controller
{
    /**
     * Get comprehensive analytics dashboard data
     */
    public function dashboard(Request $request)
    {
        $startDate = $request->get('start_date', Carbon::now()->subDays(30));
        $endDate = $request->get('end_date', Carbon::now());

        $data = [
            'overview' => $this->getOverviewStats($startDate, $endDate),
            'revenue_trends' => $this->getRevenueTrends($startDate, $endDate),
            'event_performance' => $this->getEventPerformance($startDate, $endDate),
            'user_analytics' => $this->getUserAnalytics($startDate, $endDate),
            'ticket_analytics' => $this->getTicketAnalytics($startDate, $endDate),
            'geographic_data' => $this->getGeographicData($startDate, $endDate),
        ];

        return response()->json([
            'success' => true,
            'data' => $data
        ]);
    }

    /**
     * Get overview statistics
     */
    private function getOverviewStats($startDate, $endDate)
    {
        $currentPeriod = [
            'total_revenue' => Booking::where('payment_status', 'paid')
                ->whereBetween('created_at', [$startDate, $endDate])
                ->sum('total_amount'),
            'total_bookings' => Booking::whereBetween('created_at', [$startDate, $endDate])->count(),
            'total_tickets' => Booking::where('payment_status', 'paid')
                ->whereBetween('created_at', [$startDate, $endDate])
                ->sum('quantity'),
            'new_users' => User::whereBetween('created_at', [$startDate, $endDate])->count(),
            'active_events' => Event::where('status', 'published')
                ->where('event_date', '>=', Carbon::now())
                ->count(),
        ];

        // Previous period for comparison
        $periodDiff = Carbon::parse($endDate)->diffInDays($startDate);
        $prevStartDate = Carbon::parse($startDate)->subDays($periodDiff);
        $prevEndDate = $startDate;

        $previousPeriod = [
            'total_revenue' => Booking::where('payment_status', 'paid')
                ->whereBetween('created_at', [$prevStartDate, $prevEndDate])
                ->sum('total_amount'),
            'total_bookings' => Booking::whereBetween('created_at', [$prevStartDate, $prevEndDate])->count(),
            'total_tickets' => Booking::where('payment_status', 'paid')
                ->whereBetween('created_at', [$prevStartDate, $prevEndDate])
                ->sum('quantity'),
            'new_users' => User::whereBetween('created_at', [$prevStartDate, $prevEndDate])->count(),
        ];

        // Calculate growth percentages
        $growth = [];
        foreach ($currentPeriod as $key => $value) {
            if (isset($previousPeriod[$key]) && $previousPeriod[$key] > 0) {
                $growth[$key] = round((($value - $previousPeriod[$key]) / $previousPeriod[$key]) * 100, 1);
            } else {
                $growth[$key] = $value > 0 ? 100 : 0;
            }
        }

        return [
            'current' => $currentPeriod,
            'previous' => $previousPeriod,
            'growth' => $growth
        ];
    }

    /**
     * Get revenue trends data
     */
    private function getRevenueTrends($startDate, $endDate)
    {
        $dailyRevenue = Booking::select(
                DB::raw('DATE(created_at) as date'),
                DB::raw('SUM(CASE WHEN payment_status = "paid" THEN total_amount ELSE 0 END) as revenue'),
                DB::raw('COUNT(*) as bookings'),
                DB::raw('SUM(CASE WHEN payment_status = "paid" THEN quantity ELSE 0 END) as tickets_sold')
            )
            ->whereBetween('created_at', [$startDate, $endDate])
            ->groupBy('date')
            ->orderBy('date')
            ->get();

        $monthlyRevenue = Booking::select(
                DB::raw('YEAR(created_at) as year'),
                DB::raw('MONTH(created_at) as month'),
                DB::raw('SUM(CASE WHEN payment_status = "paid" THEN total_amount ELSE 0 END) as revenue'),
                DB::raw('COUNT(*) as bookings')
            )
            ->whereBetween('created_at', [Carbon::parse($startDate)->subMonths(12), $endDate])
            ->groupBy('year', 'month')
            ->orderBy('year')
            ->orderBy('month')
            ->get();

        return [
            'daily' => $dailyRevenue,
            'monthly' => $monthlyRevenue
        ];
    }

    /**
     * Get event performance analytics
     */
    private function getEventPerformance($startDate, $endDate)
    {
        $topEvents = Event::select('events.*')
            ->selectRaw('COUNT(bookings.id) as booking_count')
            ->selectRaw('SUM(CASE WHEN bookings.payment_status = "paid" THEN bookings.total_amount ELSE 0 END) as revenue')
            ->selectRaw('SUM(CASE WHEN bookings.payment_status = "paid" THEN bookings.quantity ELSE 0 END) as tickets_sold')
            ->leftJoin('bookings', 'events.id', '=', 'bookings.event_id')
            ->whereBetween('bookings.created_at', [$startDate, $endDate])
            ->groupBy('events.id')
            ->orderBy('revenue', 'desc')
            ->limit(10)
            ->get();

        $eventsByStatus = Event::select('status')
            ->selectRaw('COUNT(*) as count')
            ->groupBy('status')
            ->get();

        $upcomingEvents = Event::where('status', 'published')
            ->where('event_date', '>=', Carbon::now())
            ->with(['ticketTypes'])
            ->orderBy('event_date')
            ->limit(5)
            ->get()
            ->map(function ($event) {
                $totalTickets = $event->ticketTypes->sum('quantity_available');
                $soldTickets = $event->bookings()->where('payment_status', 'paid')->sum('quantity');

                return [
                    'id' => $event->id,
                    'title' => $event->title,
                    'date' => $event->event_date,
                    'venue' => $event->venue_name,
                    'total_tickets' => $totalTickets,
                    'sold_tickets' => $soldTickets,
                    'sales_percentage' => $totalTickets > 0 ? round(($soldTickets / $totalTickets) * 100, 1) : 0
                ];
            });

        return [
            'top_events' => $topEvents,
            'events_by_status' => $eventsByStatus,
            'upcoming_events' => $upcomingEvents
        ];
    }

    /**
     * Get user analytics
     */
    private function getUserAnalytics($startDate, $endDate)
    {
        $userGrowth = User::select(
                DB::raw('DATE(created_at) as date'),
                DB::raw('COUNT(*) as new_users')
            )
            ->whereBetween('created_at', [$startDate, $endDate])
            ->groupBy('date')
            ->orderBy('date')
            ->get();

        $usersByRole = User::select('role')
            ->selectRaw('COUNT(*) as count')
            ->groupBy('role')
            ->get();

        $topCustomers = User::select('users.*')
            ->selectRaw('COUNT(bookings.id) as booking_count')
            ->selectRaw('SUM(CASE WHEN bookings.payment_status = "paid" THEN bookings.total_amount ELSE 0 END) as total_spent')
            ->leftJoin('bookings', 'users.id', '=', 'bookings.user_id')
            ->whereBetween('bookings.created_at', [$startDate, $endDate])
            ->groupBy('users.id')
            ->orderBy('total_spent', 'desc')
            ->limit(10)
            ->get();

        return [
            'user_growth' => $userGrowth,
            'users_by_role' => $usersByRole,
            'top_customers' => $topCustomers
        ];
    }

    /**
     * Get ticket analytics
     */
    private function getTicketAnalytics($startDate, $endDate)
    {
        $ticketTypePerformance = TicketType::select('ticket_types.*')
            ->selectRaw('SUM(CASE WHEN bookings.payment_status = "paid" THEN bookings.quantity ELSE 0 END) as tickets_sold')
            ->selectRaw('SUM(CASE WHEN bookings.payment_status = "paid" THEN bookings.total_amount ELSE 0 END) as revenue')
            ->leftJoin('bookings', 'ticket_types.id', '=', 'bookings.ticket_type_id')
            ->whereBetween('bookings.created_at', [$startDate, $endDate])
            ->groupBy('ticket_types.id')
            ->orderBy('revenue', 'desc')
            ->get();

        $priceDistribution = TicketType::select(
                DB::raw('CASE
                    WHEN price < 500 THEN "Under ₹500"
                    WHEN price < 1000 THEN "₹500-₹1000"
                    WHEN price < 2000 THEN "₹1000-₹2000"
                    WHEN price < 5000 THEN "₹2000-₹5000"
                    ELSE "Above ₹5000"
                END as price_range'),
                DB::raw('COUNT(*) as count')
            )
            ->groupBy('price_range')
            ->get();

        return [
            'ticket_type_performance' => $ticketTypePerformance,
            'price_distribution' => $priceDistribution
        ];
    }

    /**
     * Get geographic data (placeholder for future implementation)
     */
    private function getGeographicData($startDate, $endDate)
    {
        // This would require additional user location data
        // For now, return placeholder data
        return [
            'top_cities' => [
                ['city' => 'Mumbai', 'bookings' => 150, 'revenue' => 75000],
                ['city' => 'Delhi', 'bookings' => 120, 'revenue' => 60000],
                ['city' => 'Bangalore', 'bookings' => 100, 'revenue' => 50000],
                ['city' => 'Chennai', 'bookings' => 80, 'revenue' => 40000],
                ['city' => 'Pune', 'bookings' => 60, 'revenue' => 30000],
            ]
        ];
    }

    /**
     * Get real-time analytics
     */
    public function realtime(Request $request)
    {
        $today = Carbon::today();

        $stats = [
            'today_bookings' => Booking::whereDate('created_at', $today)->count(),
            'today_revenue' => Booking::where('payment_status', 'paid')
                ->whereDate('created_at', $today)
                ->sum('total_amount'),
            'today_tickets' => Booking::where('payment_status', 'paid')
                ->whereDate('created_at', $today)
                ->sum('quantity'),
            'online_users' => User::where('updated_at', '>=', Carbon::now()->subMinutes(5))->count(),
            'pending_bookings' => Booking::where('payment_status', 'pending')->count(),
            'recent_bookings' => Booking::with(['user', 'event'])
                ->orderBy('created_at', 'desc')
                ->limit(5)
                ->get(),
        ];

        return response()->json([
            'success' => true,
            'data' => $stats
        ]);
    }

    /**
     * Export analytics data
     */
    public function export(Request $request)
    {
        $type = $request->get('type', 'overview');
        $startDate = $request->get('start_date', Carbon::now()->subDays(30));
        $endDate = $request->get('end_date', Carbon::now());

        switch ($type) {
            case 'revenue':
                return $this->exportRevenue($startDate, $endDate);
            case 'events':
                return $this->exportEvents($startDate, $endDate);
            case 'users':
                return $this->exportUsers($startDate, $endDate);
            default:
                return $this->exportOverview($startDate, $endDate);
        }
    }

    private function exportOverview($startDate, $endDate)
    {
        $data = $this->getOverviewStats($startDate, $endDate);

        return response()->json([
            'success' => true,
            'data' => $data,
            'filename' => 'analytics_overview_' . date('Y-m-d_H-i-s') . '.json'
        ]);
    }

    private function exportRevenue($startDate, $endDate)
    {
        $data = $this->getRevenueTrends($startDate, $endDate);

        return response()->json([
            'success' => true,
            'data' => $data,
            'filename' => 'revenue_analytics_' . date('Y-m-d_H-i-s') . '.json'
        ]);
    }

    private function exportEvents($startDate, $endDate)
    {
        $data = $this->getEventPerformance($startDate, $endDate);

        return response()->json([
            'success' => true,
            'data' => $data,
            'filename' => 'event_analytics_' . date('Y-m-d_H-i-s') . '.json'
        ]);
    }

    private function exportUsers($startDate, $endDate)
    {
        $data = $this->getUserAnalytics($startDate, $endDate);

        return response()->json([
            'success' => true,
            'data' => $data,
            'filename' => 'user_analytics_' . date('Y-m-d_H-i-s') . '.json'
        ]);
    }
}
