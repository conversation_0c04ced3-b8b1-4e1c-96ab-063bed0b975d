<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use App\Services\RazorpayService;
use App\Services\StripeService;
use Exception;

class WebhookController extends Controller
{
    protected $razorpayService;
    protected $stripeService;

    public function __construct(RazorpayService $razorpayService, StripeService $stripeService)
    {
        $this->razorpayService = $razorpayService;
        $this->stripeService = $stripeService;
    }

    /**
     * Handle Razorpay webhook
     */
    public function razorpay(Request $request)
    {
        try {
            $payload = $request->all();
            $signature = $request->header('X-Razorpay-Signature');

            Log::info('Razorpay webhook received', [
                'event' => $payload['event'] ?? 'unknown',
                'payload_keys' => array_keys($payload)
            ]);

            $result = $this->razorpayService->handleWebhook($payload, $signature);

            return response()->json($result);

        } catch (Exception $e) {
            Log::error('Razorpay webhook error', [
                'error' => $e->getMessage(),
                'payload' => $request->all()
            ]);

            return response()->json([
                'success' => false,
                'error' => $e->getMessage()
            ], 400);
        }
    }

    /**
     * Handle Stripe webhook
     */
    public function stripe(Request $request)
    {
        try {
            $payload = $request->getContent();
            $signature = $request->header('Stripe-Signature');

            Log::info('Stripe webhook received', [
                'signature_present' => !empty($signature)
            ]);

            $result = $this->stripeService->handleWebhook($payload, $signature);

            return response()->json($result);

        } catch (Exception $e) {
            Log::error('Stripe webhook error', [
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'error' => $e->getMessage()
            ], 400);
        }
    }

    /**
     * Test webhook endpoint
     */
    public function test(Request $request)
    {
        Log::info('Test webhook called', [
            'method' => $request->method(),
            'headers' => $request->headers->all(),
            'payload' => $request->all()
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Webhook test successful',
            'timestamp' => now()->toISOString()
        ]);
    }
}
