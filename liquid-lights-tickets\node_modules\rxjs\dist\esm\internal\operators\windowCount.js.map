{"version": 3, "file": "windowCount.js", "sourceRoot": "", "sources": ["../../../../src/internal/operators/windowCount.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,OAAO,EAAE,MAAM,YAAY,CAAC;AAErC,OAAO,EAAE,OAAO,EAAE,MAAM,cAAc,CAAC;AACvC,OAAO,EAAE,wBAAwB,EAAE,MAAM,sBAAsB,CAAC;AA+DhE,MAAM,UAAU,WAAW,CAAI,UAAkB,EAAE,mBAA2B,CAAC;IAC7E,MAAM,UAAU,GAAG,gBAAgB,GAAG,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,UAAU,CAAC;IAExE,OAAO,OAAO,CAAC,CAAC,MAAM,EAAE,UAAU,EAAE,EAAE;QACpC,IAAI,OAAO,GAAG,CAAC,IAAI,OAAO,EAAK,CAAC,CAAC;QACjC,IAAI,MAAM,GAAa,EAAE,CAAC;QAC1B,IAAI,KAAK,GAAG,CAAC,CAAC;QAGd,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,YAAY,EAAE,CAAC,CAAC;QAE3C,MAAM,CAAC,SAAS,CACd,wBAAwB,CACtB,UAAU,EACV,CAAC,KAAQ,EAAE,EAAE;YAIX,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE;gBAC5B,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;aACpB;YAMD,MAAM,CAAC,GAAG,KAAK,GAAG,UAAU,GAAG,CAAC,CAAC;YACjC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,UAAU,KAAK,CAAC,EAAE;gBAClC,OAAO,CAAC,KAAK,EAAG,CAAC,QAAQ,EAAE,CAAC;aAC7B;YAOD,IAAI,EAAE,KAAK,GAAG,UAAU,KAAK,CAAC,EAAE;gBAC9B,MAAM,MAAM,GAAG,IAAI,OAAO,EAAK,CAAC;gBAChC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;gBACrB,UAAU,CAAC,IAAI,CAAC,MAAM,CAAC,YAAY,EAAE,CAAC,CAAC;aACxC;QACH,CAAC,EACD,GAAG,EAAE;YACH,OAAO,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE;gBACzB,OAAO,CAAC,KAAK,EAAG,CAAC,QAAQ,EAAE,CAAC;aAC7B;YACD,UAAU,CAAC,QAAQ,EAAE,CAAC;QACxB,CAAC,EACD,CAAC,GAAG,EAAE,EAAE;YACN,OAAO,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE;gBACzB,OAAO,CAAC,KAAK,EAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;aAC7B;YACD,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QACxB,CAAC,EACD,GAAG,EAAE;YACH,MAAM,GAAG,IAAK,CAAC;YACf,OAAO,GAAG,IAAK,CAAC;QAClB,CAAC,CACF,CACF,CAAC;IACJ,CAAC,CAAC,CAAC;AACL,CAAC"}