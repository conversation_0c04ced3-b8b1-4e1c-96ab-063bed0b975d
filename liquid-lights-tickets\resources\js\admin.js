import './bootstrap';
import Alpine from 'alpinejs';
import focus from '@alpinejs/focus';
import collapse from '@alpinejs/collapse';
import Chart from 'chart.js/auto';

// Alpine.js plugins
Alpine.plugin(focus);
Alpine.plugin(collapse);

// Admin Dashboard Data
Alpine.data('adminDashboard', () => ({
    loading: false,
    stats: {},
    chartData: {},
    
    async init() {
        await this.loadDashboardData();
        this.initCharts();
    },
    
    async loadDashboardData() {
        this.loading = true;
        try {
            const response = await fetch('/api/admin/dashboard', {
                headers: {
                    'Authorization': `Bearer ${localStorage.getItem('admin_token')}`,
                    'Accept': 'application/json'
                }
            });
            
            if (response.ok) {
                const data = await response.json();
                this.stats = data.data.kpis;
                this.chartData = data.data.charts;
            }
        } catch (error) {
            console.error('Failed to load dashboard data:', error);
        } finally {
            this.loading = false;
        }
    },
    
    initCharts() {
        // Revenue Chart
        if (this.chartData.daily_revenue) {
            this.createRevenueChart();
        }
        
        // Event Popularity Chart
        if (this.chartData.event_popularity) {
            this.createEventPopularityChart();
        }
    },
    
    createRevenueChart() {
        const ctx = document.getElementById('revenueChart');
        if (!ctx) return;
        
        new Chart(ctx, {
            type: 'line',
            data: {
                labels: this.chartData.daily_revenue.map(item => item.date),
                datasets: [{
                    label: 'Daily Revenue',
                    data: this.chartData.daily_revenue.map(item => item.revenue),
                    borderColor: 'rgb(59, 130, 246)',
                    backgroundColor: 'rgba(59, 130, 246, 0.1)',
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    title: {
                        display: true,
                        text: 'Daily Revenue Trend'
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return '₹' + value.toLocaleString();
                            }
                        }
                    }
                }
            }
        });
    },
    
    createEventPopularityChart() {
        const ctx = document.getElementById('eventPopularityChart');
        if (!ctx) return;
        
        new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: this.chartData.event_popularity.map(item => item.title),
                datasets: [{
                    data: this.chartData.event_popularity.map(item => item.booking_count),
                    backgroundColor: [
                        '#3B82F6',
                        '#10B981',
                        '#F59E0B',
                        '#EF4444',
                        '#8B5CF6'
                    ]
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    title: {
                        display: true,
                        text: 'Event Popularity'
                    }
                }
            }
        });
    }
}));

// Admin Authentication
Alpine.data('adminAuth', () => ({
    email: '',
    password: '',
    loading: false,
    error: '',
    
    async login() {
        this.loading = true;
        this.error = '';
        
        try {
            const response = await fetch('/api/admin/auth/login', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json'
                },
                body: JSON.stringify({
                    email: this.email,
                    password: this.password
                })
            });
            
            const data = await response.json();
            
            if (response.ok) {
                localStorage.setItem('admin_token', data.token);
                localStorage.setItem('admin_user', JSON.stringify(data.user));
                window.location.href = '/admin/dashboard';
            } else {
                this.error = data.message || 'Login failed';
            }
        } catch (error) {
            this.error = 'Network error. Please try again.';
        } finally {
            this.loading = false;
        }
    },
    
    logout() {
        localStorage.removeItem('admin_token');
        localStorage.removeItem('admin_user');
        window.location.href = '/admin/login';
    }
}));

// Event Management
Alpine.data('eventManager', () => ({
    events: [],
    loading: false,
    filters: {
        search: '',
        status: '',
        date_from: '',
        date_to: ''
    },
    
    async init() {
        await this.loadEvents();
    },
    
    async loadEvents() {
        this.loading = true;
        try {
            const params = new URLSearchParams(this.filters);
            const response = await fetch(`/api/admin/events?${params}`, {
                headers: {
                    'Authorization': `Bearer ${localStorage.getItem('admin_token')}`,
                    'Accept': 'application/json'
                }
            });
            
            if (response.ok) {
                const data = await response.json();
                this.events = data.data.data;
            }
        } catch (error) {
            console.error('Failed to load events:', error);
        } finally {
            this.loading = false;
        }
    },
    
    async publishEvent(eventId) {
        try {
            const response = await fetch(`/api/admin/events/${eventId}/publish`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${localStorage.getItem('admin_token')}`,
                    'Accept': 'application/json'
                }
            });
            
            if (response.ok) {
                await this.loadEvents();
            }
        } catch (error) {
            console.error('Failed to publish event:', error);
        }
    },
    
    async cancelEvent(eventId) {
        if (confirm('Are you sure you want to cancel this event?')) {
            try {
                const response = await fetch(`/api/admin/events/${eventId}/cancel`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${localStorage.getItem('admin_token')}`,
                        'Accept': 'application/json'
                    }
                });
                
                if (response.ok) {
                    await this.loadEvents();
                }
            } catch (error) {
                console.error('Failed to cancel event:', error);
            }
        }
    }
}));

// Start Alpine.js
window.Alpine = Alpine;
Alpine.start();

// Make Chart.js available globally
window.Chart = Chart;
