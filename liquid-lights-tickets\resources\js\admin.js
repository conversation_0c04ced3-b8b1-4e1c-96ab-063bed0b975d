import './bootstrap';
import Alpine from 'alpinejs';
import focus from '@alpinejs/focus';
import collapse from '@alpinejs/collapse';
import Chart from 'chart.js/auto';

// Alpine.js plugins
Alpine.plugin(focus);
Alpine.plugin(collapse);

// Admin Dashboard Data
Alpine.data('adminDashboard', () => ({
    loading: false,
    stats: {},
    chartData: {},
    
    async init() {
        await this.loadDashboardData();
        this.initCharts();
    },
    
    async loadDashboardData() {
        this.loading = true;
        try {
            const response = await fetch('/api/admin/dashboard', {
                headers: {
                    'Authorization': `Bearer ${localStorage.getItem('admin_token')}`,
                    'Accept': 'application/json'
                }
            });
            
            if (response.ok) {
                const data = await response.json();
                this.stats = data.data.kpis;
                this.chartData = data.data.charts;
            }
        } catch (error) {
            console.error('Failed to load dashboard data:', error);
        } finally {
            this.loading = false;
        }
    },
    
    initCharts() {
        // Revenue Chart
        if (this.chartData.daily_revenue) {
            this.createRevenueChart();
        }
        
        // Event Popularity Chart
        if (this.chartData.event_popularity) {
            this.createEventPopularityChart();
        }
    },
    
    createRevenueChart() {
        const ctx = document.getElementById('revenueChart');
        if (!ctx) return;
        
        new Chart(ctx, {
            type: 'line',
            data: {
                labels: this.chartData.daily_revenue.map(item => item.date),
                datasets: [{
                    label: 'Daily Revenue',
                    data: this.chartData.daily_revenue.map(item => item.revenue),
                    borderColor: 'rgb(59, 130, 246)',
                    backgroundColor: 'rgba(59, 130, 246, 0.1)',
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    title: {
                        display: true,
                        text: 'Daily Revenue Trend'
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return '₹' + value.toLocaleString();
                            }
                        }
                    }
                }
            }
        });
    },
    
    createEventPopularityChart() {
        const ctx = document.getElementById('eventPopularityChart');
        if (!ctx) return;
        
        new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: this.chartData.event_popularity.map(item => item.title),
                datasets: [{
                    data: this.chartData.event_popularity.map(item => item.booking_count),
                    backgroundColor: [
                        '#3B82F6',
                        '#10B981',
                        '#F59E0B',
                        '#EF4444',
                        '#8B5CF6'
                    ]
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    title: {
                        display: true,
                        text: 'Event Popularity'
                    }
                }
            }
        });
    }
}));

// Admin Authentication
Alpine.data('adminAuth', () => ({
    email: '',
    password: '',
    loading: false,
    error: '',
    
    async login() {
        this.loading = true;
        this.error = '';
        
        try {
            const response = await fetch('/api/admin/auth/login', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json'
                },
                body: JSON.stringify({
                    email: this.email,
                    password: this.password
                })
            });
            
            const data = await response.json();
            
            if (response.ok) {
                localStorage.setItem('admin_token', data.token);
                localStorage.setItem('admin_user', JSON.stringify(data.user));
                window.location.href = '/admin/dashboard';
            } else {
                this.error = data.message || 'Login failed';
            }
        } catch (error) {
            this.error = 'Network error. Please try again.';
        } finally {
            this.loading = false;
        }
    },
    
    logout() {
        localStorage.removeItem('admin_token');
        localStorage.removeItem('admin_user');
        window.location.href = '/admin/login';
    }
}));

// Event Management
Alpine.data('eventManager', () => ({
    events: [],
    loading: false,
    filters: {
        search: '',
        status: '',
        date_from: '',
        date_to: ''
    },
    
    async init() {
        await this.loadEvents();
    },
    
    async loadEvents() {
        this.loading = true;
        try {
            const params = new URLSearchParams(this.filters);
            const response = await fetch(`/api/admin/events?${params}`, {
                headers: {
                    'Authorization': `Bearer ${localStorage.getItem('admin_token')}`,
                    'Accept': 'application/json'
                }
            });
            
            if (response.ok) {
                const data = await response.json();
                this.events = data.data.data;
            }
        } catch (error) {
            console.error('Failed to load events:', error);
        } finally {
            this.loading = false;
        }
    },
    
    async publishEvent(eventId) {
        try {
            const response = await fetch(`/api/admin/events/${eventId}/publish`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${localStorage.getItem('admin_token')}`,
                    'Accept': 'application/json'
                }
            });
            
            if (response.ok) {
                await this.loadEvents();
            }
        } catch (error) {
            console.error('Failed to publish event:', error);
        }
    },
    
    async cancelEvent(eventId) {
        if (confirm('Are you sure you want to cancel this event?')) {
            try {
                const response = await fetch(`/api/admin/events/${eventId}/cancel`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${localStorage.getItem('admin_token')}`,
                        'Accept': 'application/json'
                    }
                });
                
                if (response.ok) {
                    await this.loadEvents();
                }
            } catch (error) {
                console.error('Failed to cancel event:', error);
            }
        }
    }
}));

// Event Form Management
Alpine.data('eventForm', () => ({
    loading: false,
    errors: {},
    form: {
        title: '',
        short_description: '',
        description: '',
        venue_name: '',
        venue_address: '',
        event_date: '',
        event_time: '',
        is_featured: false,
        ticket_types: [
            {
                name: 'General Admission',
                price: '',
                quantity_available: '',
                sale_start_date: '',
                sale_end_date: ''
            }
        ]
    },

    addTicketType() {
        this.form.ticket_types.push({
            name: '',
            price: '',
            quantity_available: '',
            sale_start_date: '',
            sale_end_date: ''
        });
    },

    removeTicketType(index) {
        if (this.form.ticket_types.length > 1) {
            this.form.ticket_types.splice(index, 1);
        }
    },

    async submitForm() {
        this.loading = true;
        this.errors = {};

        try {
            const response = await fetch('/api/admin/events', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${localStorage.getItem('admin_token')}`,
                    'Accept': 'application/json'
                },
                body: JSON.stringify(this.form)
            });

            const data = await response.json();

            if (response.ok) {
                // Success - redirect to events list
                window.location.href = '/admin/events';
            } else {
                // Handle validation errors
                if (data.errors) {
                    this.errors = data.errors;
                } else {
                    alert(data.message || 'Failed to create event');
                }
            }
        } catch (error) {
            console.error('Error creating event:', error);
            alert('Network error. Please try again.');
        } finally {
            this.loading = false;
        }
    }
}));

// Booking Management
Alpine.data('bookingManager', () => ({
    loading: false,
    bookings: [],
    events: [],
    stats: {},
    filters: {
        search: '',
        status: '',
        event_id: '',
        date_from: '',
        date_to: '',
        checked_in: ''
    },

    async init() {
        await this.loadBookings();
        await this.loadEvents();
        await this.loadStats();
    },

    async loadBookings() {
        this.loading = true;
        try {
            const params = new URLSearchParams(this.filters);
            const response = await fetch(`/api/admin/bookings?${params}`, {
                headers: {
                    'Authorization': `Bearer ${localStorage.getItem('admin_token')}`,
                    'Accept': 'application/json'
                }
            });

            if (response.ok) {
                const data = await response.json();
                this.bookings = data.data.data;
            }
        } catch (error) {
            console.error('Failed to load bookings:', error);
        } finally {
            this.loading = false;
        }
    },

    async loadEvents() {
        try {
            const response = await fetch('/api/admin/events', {
                headers: {
                    'Authorization': `Bearer ${localStorage.getItem('admin_token')}`,
                    'Accept': 'application/json'
                }
            });

            if (response.ok) {
                const data = await response.json();
                this.events = data.data.data;
            }
        } catch (error) {
            console.error('Failed to load events:', error);
        }
    },

    async loadStats() {
        try {
            const response = await fetch('/api/admin/bookings-stats', {
                headers: {
                    'Authorization': `Bearer ${localStorage.getItem('admin_token')}`,
                    'Accept': 'application/json'
                }
            });

            if (response.ok) {
                const data = await response.json();
                this.stats = data.data;
            }
        } catch (error) {
            console.error('Failed to load stats:', error);
        }
    },

    async checkInBooking(bookingId) {
        try {
            const response = await fetch(`/api/admin/bookings/${bookingId}/check-in`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${localStorage.getItem('admin_token')}`,
                    'Accept': 'application/json'
                }
            });

            if (response.ok) {
                await this.loadBookings();
            }
        } catch (error) {
            console.error('Failed to check in booking:', error);
        }
    },

    async refundBooking(bookingId) {
        if (confirm('Are you sure you want to refund this booking?')) {
            try {
                const response = await fetch(`/api/admin/bookings/${bookingId}/refund`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${localStorage.getItem('admin_token')}`,
                        'Accept': 'application/json'
                    }
                });

                if (response.ok) {
                    await this.loadBookings();
                }
            } catch (error) {
                console.error('Failed to refund booking:', error);
            }
        }
    },

    async exportBookings() {
        try {
            const params = new URLSearchParams(this.filters);
            const response = await fetch(`/api/admin/bookings-export?${params}`, {
                headers: {
                    'Authorization': `Bearer ${localStorage.getItem('admin_token')}`,
                    'Accept': 'application/json'
                }
            });

            if (response.ok) {
                const data = await response.json();
                this.downloadCSV(data.data, data.filename);
            }
        } catch (error) {
            console.error('Failed to export bookings:', error);
        }
    },

    downloadCSV(csvData, filename) {
        const csvContent = csvData.map(row => row.join(',')).join('\n');
        const blob = new Blob([csvContent], { type: 'text/csv' });
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        a.click();
        window.URL.revokeObjectURL(url);
    }
}));

// User Management
Alpine.data('userManager', () => ({
    loading: false,
    users: [],
    filters: {
        search: '',
        role: '',
        status: '',
        verified: ''
    },

    async init() {
        await this.loadUsers();
    },

    async loadUsers() {
        this.loading = true;
        try {
            const params = new URLSearchParams(this.filters);
            const response = await fetch(`/api/admin/users?${params}`, {
                headers: {
                    'Authorization': `Bearer ${localStorage.getItem('admin_token')}`,
                    'Accept': 'application/json'
                }
            });

            if (response.ok) {
                const data = await response.json();
                this.users = data.data.data;
            }
        } catch (error) {
            console.error('Failed to load users:', error);
        } finally {
            this.loading = false;
        }
    },

    async blacklistUser(userId) {
        if (confirm('Are you sure you want to blacklist this user?')) {
            try {
                const response = await fetch(`/api/admin/users/${userId}/blacklist`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${localStorage.getItem('admin_token')}`,
                        'Accept': 'application/json'
                    }
                });

                if (response.ok) {
                    await this.loadUsers();
                }
            } catch (error) {
                console.error('Failed to blacklist user:', error);
            }
        }
    },

    async unblacklistUser(userId) {
        try {
            const response = await fetch(`/api/admin/users/${userId}/unblacklist`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${localStorage.getItem('admin_token')}`,
                    'Accept': 'application/json'
                }
            });

            if (response.ok) {
                await this.loadUsers();
            }
        } catch (error) {
            console.error('Failed to unblacklist user:', error);
        }
    },

    async toggleUserStatus(userId) {
        try {
            const response = await fetch(`/api/admin/users/${userId}/toggle-status`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${localStorage.getItem('admin_token')}`,
                    'Accept': 'application/json'
                }
            });

            if (response.ok) {
                await this.loadUsers();
            }
        } catch (error) {
            console.error('Failed to toggle user status:', error);
        }
    },

    async exportUsers() {
        try {
            const params = new URLSearchParams(this.filters);
            const response = await fetch(`/api/admin/users-export?${params}`, {
                headers: {
                    'Authorization': `Bearer ${localStorage.getItem('admin_token')}`,
                    'Accept': 'application/json'
                }
            });

            if (response.ok) {
                const data = await response.json();
                this.downloadCSV(data.data, data.filename);
            }
        } catch (error) {
            console.error('Failed to export users:', error);
        }
    },

    downloadCSV(csvData, filename) {
        const csvContent = csvData.map(row => row.join(',')).join('\n');
        const blob = new Blob([csvContent], { type: 'text/csv' });
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        a.click();
        window.URL.revokeObjectURL(url);
    }
}));

// Analytics Management
Alpine.data('analyticsManager', () => ({
    loading: false,
    analytics: {},
    dateRange: {
        start: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        end: new Date().toISOString().split('T')[0]
    },
    charts: {},

    async init() {
        await this.loadAnalytics();
        this.initCharts();
    },

    async loadAnalytics() {
        this.loading = true;
        try {
            const params = new URLSearchParams({
                start_date: this.dateRange.start,
                end_date: this.dateRange.end
            });

            const response = await fetch(`/api/admin/analytics/dashboard?${params}`, {
                headers: {
                    'Authorization': `Bearer ${localStorage.getItem('admin_token')}`,
                    'Accept': 'application/json'
                }
            });

            if (response.ok) {
                const data = await response.json();
                this.analytics = data.data;
                this.updateCharts();
            }
        } catch (error) {
            console.error('Failed to load analytics:', error);
        } finally {
            this.loading = false;
        }
    },

    initCharts() {
        // Revenue Trends Chart
        const revenueTrendsCtx = document.getElementById('revenueTrendsChart');
        if (revenueTrendsCtx) {
            this.charts.revenueTrends = new Chart(revenueTrendsCtx, {
                type: 'line',
                data: {
                    labels: [],
                    datasets: [{
                        label: 'Daily Revenue',
                        data: [],
                        borderColor: 'rgb(59, 130, 246)',
                        backgroundColor: 'rgba(59, 130, 246, 0.1)',
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        title: {
                            display: true,
                            text: 'Daily Revenue Trend'
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                callback: function(value) {
                                    return '₹' + value.toLocaleString();
                                }
                            }
                        }
                    }
                }
            });
        }

        // Event Performance Chart
        const eventPerformanceCtx = document.getElementById('eventPerformanceChart');
        if (eventPerformanceCtx) {
            this.charts.eventPerformance = new Chart(eventPerformanceCtx, {
                type: 'bar',
                data: {
                    labels: [],
                    datasets: [{
                        label: 'Revenue',
                        data: [],
                        backgroundColor: [
                            '#3B82F6',
                            '#10B981',
                            '#F59E0B',
                            '#EF4444',
                            '#8B5CF6'
                        ]
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        title: {
                            display: true,
                            text: 'Top Events by Revenue'
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                callback: function(value) {
                                    return '₹' + value.toLocaleString();
                                }
                            }
                        }
                    }
                }
            });
        }

        // User Growth Chart
        const userGrowthCtx = document.getElementById('userGrowthChart');
        if (userGrowthCtx) {
            this.charts.userGrowth = new Chart(userGrowthCtx, {
                type: 'line',
                data: {
                    labels: [],
                    datasets: [{
                        label: 'New Users',
                        data: [],
                        borderColor: 'rgb(16, 185, 129)',
                        backgroundColor: 'rgba(16, 185, 129, 0.1)',
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        title: {
                            display: true,
                            text: 'Daily User Growth'
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
        }
    },

    updateCharts() {
        // Update Revenue Trends Chart
        if (this.charts.revenueTrends && this.analytics.revenue_trends?.daily) {
            this.charts.revenueTrends.data.labels = this.analytics.revenue_trends.daily.map(item => item.date);
            this.charts.revenueTrends.data.datasets[0].data = this.analytics.revenue_trends.daily.map(item => item.revenue);
            this.charts.revenueTrends.update();
        }

        // Update Event Performance Chart
        if (this.charts.eventPerformance && this.analytics.event_performance?.top_events) {
            this.charts.eventPerformance.data.labels = this.analytics.event_performance.top_events.slice(0, 5).map(event => event.title);
            this.charts.eventPerformance.data.datasets[0].data = this.analytics.event_performance.top_events.slice(0, 5).map(event => event.revenue);
            this.charts.eventPerformance.update();
        }

        // Update User Growth Chart
        if (this.charts.userGrowth && this.analytics.user_analytics?.user_growth) {
            this.charts.userGrowth.data.labels = this.analytics.user_analytics.user_growth.map(item => item.date);
            this.charts.userGrowth.data.datasets[0].data = this.analytics.user_analytics.user_growth.map(item => item.new_users);
            this.charts.userGrowth.update();
        }
    },

    async exportAnalytics() {
        try {
            const params = new URLSearchParams({
                type: 'overview',
                start_date: this.dateRange.start,
                end_date: this.dateRange.end
            });

            const response = await fetch(`/api/admin/analytics/export?${params}`, {
                headers: {
                    'Authorization': `Bearer ${localStorage.getItem('admin_token')}`,
                    'Accept': 'application/json'
                }
            });

            if (response.ok) {
                const data = await response.json();
                const blob = new Blob([JSON.stringify(data.data, null, 2)], { type: 'application/json' });
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = data.filename;
                a.click();
                window.URL.revokeObjectURL(url);
            }
        } catch (error) {
            console.error('Failed to export analytics:', error);
        }
    }
}));

// Start Alpine.js
window.Alpine = Alpine;
Alpine.start();

// Make Chart.js available globally
window.Chart = Chart;
